describe('Widget Creation Flow', () => {
  beforeEach(() => {
    // Intercept API calls
    cy.intercept('POST', '/api/business/search', {
      fixture: 'business-search-results.json',
    }).as('businessSearch');
    
    cy.intercept('GET', '/api/reviews/*', {
      fixture: 'reviews-data.json',
    }).as('reviewsData');
    
    cy.intercept('POST', '/api/widget/config', {
      fixture: 'widget-config.json',
    }).as('saveConfig');
    
    cy.intercept('POST', '/api/embed/generate', {
      fixture: 'embed-code.json',
    }).as('generateEmbed');

    cy.visit('/');
  });

  it('should complete the full widget creation workflow', () => {
    // Step 1: Source - Search and select business
    cy.get('[data-testid="business-search-input"]').should('be.visible');
    cy.selectBusiness('Test Restaurant');
    cy.wait('@businessSearch');
    
    // Verify business is selected
    cy.get('[data-testid="business-selected"]').should('contain', 'Test Restaurant');
    
    // Step 2: Layout - Select template
    cy.get('[data-testid="step-layout"]').click();
    cy.configureTemplate('carousel');
    
    // Verify template preview
    cy.get('[data-testid="template-preview"]').should('be.visible');
    
    // Step 3: Header - Configure header settings
    cy.get('[data-testid="step-header"]').click();
    cy.get('[data-testid="header-text-input"]').clear().type('Customer Reviews');
    cy.get('[data-testid="show-business-info"]').check();
    
    // Step 4: Reviews - Configure review settings
    cy.get('[data-testid="step-reviews"]').click();
    cy.get('[data-testid="max-reviews-slider"]').invoke('val', 10).trigger('input');
    cy.get('[data-testid="min-rating-select"]').select('4');
    cy.get('[data-testid="sort-by-select"]').select('newest');
    
    // Step 5: Style - Customize appearance
    cy.get('[data-testid="step-style"]').click();
    cy.get('[data-testid="primary-color-picker"]').invoke('val', '#ff6b6b').trigger('input');
    cy.get('[data-testid="font-family-select"]').select('Roboto');
    
    // Step 6: Settings - Final configuration
    cy.get('[data-testid="step-settings"]').click();
    cy.get('[data-testid="auto-refresh-toggle"]').check();
    cy.get('[data-testid="show-photos-toggle"]').check();
    
    // Verify live preview updates throughout
    cy.waitForWidget();
    cy.get('[data-testid="widget-preview"]').should('contain', 'Customer Reviews');
    
    // Save configuration
    cy.get('[data-testid="save-config-button"]').click();
    cy.wait('@saveConfig');
    
    // Generate embed code
    cy.get('[data-testid="generate-embed-button"]').click();
    cy.wait('@generateEmbed');
    
    // Verify embed code is generated
    cy.get('[data-testid="embed-code-display"]').should('be.visible');
    cy.get('[data-testid="copy-embed-button"]').should('be.enabled');
    
    // Test copy to clipboard
    cy.get('[data-testid="copy-embed-button"]').click();
    cy.get('[data-testid="copy-success-message"]').should('be.visible');
  });

  it('should handle errors gracefully during widget creation', () => {
    // Test network error handling
    cy.intercept('POST', '/api/business/search', {
      statusCode: 500,
      body: { error: 'Internal server error' },
    }).as('businessSearchError');
    
    cy.get('[data-testid="business-search-input"]').type('Test Business');
    cy.get('[data-testid="search-button"]').click();
    cy.wait('@businessSearchError');
    
    // Verify error message is displayed
    cy.get('[data-testid="error-message"]').should('contain', 'Unable to search for businesses');
    cy.get('[data-testid="retry-button"]').should('be.visible');
    
    // Test retry functionality
    cy.intercept('POST', '/api/business/search', {
      fixture: 'business-search-results.json',
    }).as('businessSearchRetry');
    
    cy.get('[data-testid="retry-button"]').click();
    cy.wait('@businessSearchRetry');
    
    // Verify recovery
    cy.get('[data-testid="business-result"]').should('be.visible');
  });

  it('should validate form inputs and show appropriate errors', () => {
    // Test empty business search
    cy.get('[data-testid="search-button"]').click();
    cy.get('[data-testid="validation-error"]').should('contain', 'Please enter a business name');
    
    // Navigate to customization and test invalid inputs
    cy.selectBusiness('Test Business');
    cy.get('[data-testid="step-style"]').click();
    
    // Test invalid color input
    cy.get('[data-testid="primary-color-picker"]').clear().type('invalid-color');
    cy.get('[data-testid="color-validation-error"]').should('be.visible');
    
    // Test invalid number inputs
    cy.get('[data-testid="step-reviews"]').click();
    cy.get('[data-testid="max-reviews-input"]').clear().type('-1');
    cy.get('[data-testid="reviews-validation-error"]').should('contain', 'Must be a positive number');
  });

  it('should maintain state when navigating between steps', () => {
    // Configure widget across multiple steps
    cy.selectBusiness('Test Business');
    
    cy.get('[data-testid="step-header"]').click();
    cy.get('[data-testid="header-text-input"]').clear().type('My Reviews');
    
    cy.get('[data-testid="step-style"]').click();
    cy.get('[data-testid="primary-color-picker"]').invoke('val', '#00ff00').trigger('input');
    
    // Navigate back to header step
    cy.get('[data-testid="step-header"]').click();
    cy.get('[data-testid="header-text-input"]').should('have.value', 'My Reviews');
    
    // Navigate back to style step
    cy.get('[data-testid="step-style"]').click();
    cy.get('[data-testid="primary-color-picker"]').should('have.value', '#00ff00');
    
    // Verify preview reflects all changes
    cy.waitForWidget();
    cy.get('[data-testid="widget-preview"]').should('contain', 'My Reviews');
  });

  it('should be responsive across different screen sizes', () => {
    // Test mobile viewport
    cy.viewport('iphone-x');
    cy.selectBusiness('Test Business');
    
    // Verify mobile navigation works
    cy.get('[data-testid="mobile-menu-button"]').should('be.visible');
    cy.get('[data-testid="step-navigation"]').should('not.be.visible');
    
    cy.get('[data-testid="mobile-menu-button"]').click();
    cy.get('[data-testid="mobile-step-menu"]').should('be.visible');
    
    // Test tablet viewport
    cy.viewport('ipad-2');
    cy.get('[data-testid="step-navigation"]').should('be.visible');
    cy.get('[data-testid="mobile-menu-button"]').should('not.be.visible');
    
    // Test desktop viewport
    cy.viewport(1280, 720);
    cy.get('[data-testid="step-navigation"]').should('be.visible');
    cy.get('[data-testid="widget-preview"]').should('be.visible');
  });

  it('should support keyboard navigation', () => {
    // Test tab navigation through form elements
    cy.get('body').tab();
    cy.focused().should('have.attr', 'data-testid', 'business-search-input');
    
    cy.focused().tab();
    cy.focused().should('have.attr', 'data-testid', 'search-button');
    
    // Test Enter key functionality
    cy.get('[data-testid="business-search-input"]').type('Test Business');
    cy.get('[data-testid="business-search-input"]').type('{enter}');
    cy.wait('@businessSearch');
    
    // Test arrow key navigation in step navigation
    cy.get('[data-testid="step-source"]').focus();
    cy.focused().type('{rightarrow}');
    cy.focused().should('have.attr', 'data-testid', 'step-layout');
  });
});