/// <reference types="cypress" />

// Custom command for accessibility testing
Cypress.Commands.add('checkA11y', () => {
  cy.injectAxe();
  cy.checkA11y(undefined, {
    rules: {
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'focus-management': { enabled: true },
    },
  });
});

// Custom command to wait for widget to load
Cypress.Commands.add('waitForWidget', () => {
  cy.get('[data-testid="widget-preview"]', { timeout: 10000 }).should('be.visible');
  cy.get('[data-testid="loading-skeleton"]').should('not.exist');
});

// Custom command to select business in search
Cypress.Commands.add('selectBusiness', (businessName: string) => {
  cy.get('[data-testid="business-search-input"]').type(businessName);
  cy.get('[data-testid="search-button"]').click();
  cy.get('[data-testid="business-result"]').contains(businessName).click();
  cy.get('[data-testid="business-selected"]').should('contain', businessName);
});

// Custom command to configure widget template
Cypress.Commands.add('configureTemplate', (template: string) => {
  cy.get('[data-testid="step-layout"]').click();
  cy.get(`[data-testid="template-${template}"]`).click();
  cy.get('[data-testid="template-selected"]').should('contain', template);
});