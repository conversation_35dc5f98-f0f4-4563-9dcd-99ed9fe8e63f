# Vercel Deployment Guide

Vercel is actually a better choice for this project! Here's why and how to deploy.

## 🚀 Why Vercel is Better

- **Zero configuration**: Automatic builds and deployments
- **Serverless functions**: Perfect for API endpoints
- **Static hosting**: Excellent for frontend and widget CDN
- **Edge network**: Global CDN included
- **Free tier**: Generous limits for development
- **No native compilation issues**: Handles dependencies automatically

## 📋 Deployment Strategy

### Phase 1: Frontend Deployment (Main App)
1. **Deploy Frontend to Vercel**
   - Connect GitHub repository
   - Set build directory: `packages/frontend`
   - Automatic React/Vite detection
   - Custom domain support

### Phase 2: API Deployment (Serverless Functions)
1. **Convert Backend to Vercel Functions**
   - Create `/api` directory in root
   - Convert Express routes to Vercel functions
   - Use Vercel's built-in database integrations

### Phase 3: Widget CDN (Static Hosting)
1. **Deploy Widget Runtime**
   - Build static widget files
   - Deploy to Vercel's CDN
   - Ultra-fast global distribution

## 🔧 Project Structure for Vercel

```
google-reviews-widget/
├── api/                    # Vercel serverless functions
│   ├── business/
│   │   └── search.ts      # Business search endpoint
│   ├── reviews/
│   │   └── [businessId].ts # Reviews endpoint
│   └── widget-config/
│       └── [id].ts        # Widget config endpoint
├── public/                 # Static assets
│   └── widget-platform.js # Widget platform script
├── packages/frontend/      # React app (main deployment)
└── vercel.json            # Vercel configuration
```

## 🛠️ Implementation Steps

### Step 1: Create Vercel Configuration

Create `vercel.json` in root:
```json
{
  "version": 2,
  "builds": [
    {
      "src": "packages/frontend/package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    },
    {
      "src": "/widget-platform.js",
      "dest": "/public/widget-platform.js"
    },
    {
      "src": "/(.*)",
      "dest": "/packages/frontend/dist/$1"
    }
  ],
  "functions": {
    "api/**/*.ts": {
      "runtime": "nodejs18.x"
    }
  },
  "env": {
    "NODE_ENV": "production"
  }
}
```

### Step 2: Create API Functions

Create `api/business/search.ts`:
```typescript
import { VercelRequest, VercelResponse } from '@vercel/node';
import { searchBusinesses } from '../../packages/backend/src/lib/services/business-search.service';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { query } = req.query;
    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Query parameter required' });
    }

    const businesses = await searchBusinesses(query);
    res.status(200).json(businesses);
  } catch (error) {
    console.error('Business search error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
```

### Step 3: Create Widget Platform Script

Create `public/widget-platform.js` (built from widget-runtime):
```javascript
// This will be the compiled widget platform script
// Built from packages/widget-runtime/src/widget-platform.ts
```

### Step 4: Update Frontend Configuration

Update `packages/frontend/vite.config.ts`:
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom']
        }
      }
    }
  },
  define: {
    'process.env.VITE_API_BASE_URL': JSON.stringify(process.env.VITE_API_BASE_URL || '/api'),
    'process.env.VITE_WIDGET_CDN_URL': JSON.stringify(process.env.VITE_WIDGET_CDN_URL || '')
  }
})
```

## 🌐 Database Options for Vercel

### Option 1: Vercel Postgres (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Add Vercel Postgres
vercel env add DATABASE_URL
```

### Option 2: PlanetScale (MySQL)
- Serverless MySQL database
- Excellent Vercel integration
- Generous free tier

### Option 3: Supabase (PostgreSQL)
- Keep existing Supabase setup
- Works perfectly with Vercel
- Real-time features included

## 💰 Cost Comparison

### Vercel Pricing:
- **Hobby (Free)**: 
  - 100GB bandwidth
  - 100 serverless function executions/day
  - Perfect for development and testing

- **Pro ($20/month)**:
  - 1TB bandwidth
  - 1000 serverless function executions/day
  - Custom domains
  - Analytics

**Total Cost**: $0-20/month (much cheaper than Railway!)

## 🚀 Deployment Steps

### Step 1: Prepare Repository
```bash
# Create Vercel configuration
# Convert backend routes to API functions
# Build widget platform script
git add .
git commit -m "Prepare for Vercel deployment"
git push origin main
```

### Step 2: Deploy to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Sign in with GitHub
3. Click "New Project"
4. Select your repository
5. Vercel auto-detects the setup
6. Deploy!

### Step 3: Configure Environment Variables
Add in Vercel dashboard:
```env
DATABASE_URL=your_database_url
GOOGLE_PLACES_API_KEY=your_api_key
GOOGLE_MY_BUSINESS_API_KEY=your_api_key
JWT_SECRET=your_jwt_secret
```

### Step 4: Test Deployment
- Frontend: `https://your-project.vercel.app`
- API: `https://your-project.vercel.app/api/business/search?query=test`
- Widget: `https://your-project.vercel.app/widget-platform.js`

## 🎯 Benefits of Vercel Approach

1. **No Build Issues**: Handles all dependencies automatically
2. **Instant Deployments**: Deploy in seconds, not minutes
3. **Global CDN**: Your widget loads fast worldwide
4. **Automatic HTTPS**: SSL certificates included
5. **Preview Deployments**: Test every commit automatically
6. **Zero Configuration**: Works out of the box
7. **Cost Effective**: Free tier covers most use cases

## 🔄 Migration from Railway

If you want to switch from Railway to Vercel:

1. **Create the Vercel configuration** (as shown above)
2. **Convert Express routes** to Vercel functions
3. **Build widget platform script** to static file
4. **Deploy to Vercel**
5. **Update DNS** (if using custom domains)

This approach eliminates all the compilation issues you're seeing with Railway and provides a much smoother deployment experience!

## 📝 Next Steps

1. **Create Vercel configuration files**
2. **Convert backend routes to API functions**
3. **Build widget platform as static asset**
4. **Deploy to Vercel**
5. **Test the complete flow**

Would you like me to implement this Vercel approach? It will be much more reliable than Railway for this project!
