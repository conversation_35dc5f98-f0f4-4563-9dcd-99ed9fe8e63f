#!/bin/bash

# Production deployment script
# Usage: ./scripts/deploy.sh [environment]

set -e

ENVIRONMENT=${1:-production}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Starting deployment for ${ENVIRONMENT} environment..."

# Load environment variables
if [ -f "${PROJECT_ROOT}/.env.${ENVIRONMENT}" ]; then
    source "${PROJECT_ROOT}/.env.${ENVIRONMENT}"
    echo "Loaded environment variables for ${ENVIRONMENT}"
else
    echo "Warning: No environment file found for ${ENVIRONMENT}"
fi

# Pre-deployment checks
echo "Running pre-deployment checks..."

# Check if required environment variables are set
required_vars=("DATABASE_URL" "REDIS_URL" "GOOGLE_PLACES_API_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable $var is not set"
        exit 1
    fi
done

# Check Docker and Docker Compose
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Error: Docker Compose is not installed"
    exit 1
fi

# Build and test
echo "Building applications..."
cd "$PROJECT_ROOT"

# Run tests
echo "Running tests..."
npm run test:all

# Build production images
echo "Building production Docker images..."
docker-compose -f docker-compose.prod.yml build

# Database migration
echo "Running database migrations..."
docker-compose -f docker-compose.prod.yml run --rm backend npm run db:migrate

# Backup database before deployment
echo "Creating database backup..."
if [ -f "packages/backend/scripts/db-backup.sh" ]; then
    ./packages/backend/scripts/db-backup.sh "$ENVIRONMENT"
fi

# Deploy services
echo "Deploying services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 30

# Health checks
echo "Running health checks..."
services=("frontend" "backend" "widget-runtime" "postgres" "redis")
for service in "${services[@]}"; do
    if docker-compose -f docker-compose.prod.yml ps "$service" | grep -q "Up (healthy)"; then
        echo "✓ $service is healthy"
    else
        echo "✗ $service is not healthy"
        docker-compose -f docker-compose.prod.yml logs "$service"
        exit 1
    fi
done

# Post-deployment tasks
echo "Running post-deployment tasks..."

# Database maintenance
if [ -f "packages/backend/scripts/db-maintenance.sh" ]; then
    ./packages/backend/scripts/db-maintenance.sh "$ENVIRONMENT"
fi

# Clear application caches
echo "Clearing application caches..."
# Add cache clearing commands here if needed

echo "Deployment completed successfully!"
echo "Services are running at:"
echo "  Frontend: https://app.yourdomain.com"
echo "  Backend API: https://api.yourdomain.com"
echo "  Widget CDN: https://cdn.yourdomain.com"
echo "  Monitoring: http://localhost:3000 (Grafana)"
echo "  Metrics: http://localhost:9090 (Prometheus)"