import { GetServerSideProps } from 'next';
import { useEffect } from 'react';

interface WidgetPreviewPageProps {
    widgetId: string;
}

export default function WidgetPreviewPage({ widgetId }: WidgetPreviewPageProps) {
    useEffect(() => {
        // Load the widget platform script
        const script = document.createElement('script');
        script.src = '/widget-platform.js';
        script.async = true;
        document.head.appendChild(script);

        return () => {
            // Cleanup
            document.head.removeChild(script);
        };
    }, []);

    return (
        <html lang="en">
            <head>
                <meta charSet="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <title>Google Reviews Widget Preview</title>
                <style>{`
          body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
          }
          .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .preview-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
          }
          .widget-container {
            display: flex;
            justify-content: center;
            min-height: 300px;
          }
        `}</style>
            </head>
            <body>
                <div className="preview-container">
                    <div className="preview-header">
                        <h1>Google Reviews Widget Preview</h1>
                        <p>Widget ID: {widgetId}</p>
                    </div>
                    <div className="widget-container">
                        <div className={`grw-widget-${widgetId}`} data-grw-widget-lazy></div>
                    </div>
                </div>
            </body>
        </html>
    );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
    const { id } = context.params!;

    return {
        props: {
            widgetId: id as string,
        },
    };
};
