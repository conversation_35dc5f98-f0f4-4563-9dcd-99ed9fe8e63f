# Railway Deployment Guide

Railway is perfect for this Google Reviews Widget project! Here's your step-by-step deployment strategy.

## 🚀 Why Railway is Ideal

- **Multi-service support**: Deploy frontend, backend, and widget-runtime separately
- **Automatic HTTPS**: Built-in SSL certificates
- **Environment variables**: Easy configuration management
- **PostgreSQL addon**: Managed database included
- **Redis addon**: Managed caching layer
- **GitHub integration**: Automatic deployments on push
- **Custom domains**: Easy domain configuration
- **Cost-effective**: Pay only for what you use

## 📋 Deployment Strategy

### Phase 1: Database Setup
1. **Create PostgreSQL Database**
   - In Railway dashboard, click "New Project"
   - Add "PostgreSQL" service
   - Note the connection details

2. **Create Redis Cache**
   - Add "Redis" service to your project
   - Note the connection URL

### Phase 2: Backend Deployment
1. **Deploy Backend Service**
   ```bash
   # Connect your GitHub repo to Railway
   # Create new service from GitHub repo
   # Set root directory to: packages/backend
   # Set build command to: npm run build
   # Set start command to: npm start
   ```

2. **Configure Environment Variables**
   ```env
   NODE_ENV=production
   DATABASE_URL=${{Postgres.DATABASE_URL}}
   REDIS_URL=${{Redis.REDIS_URL}}
   GOOGLE_PLACES_API_KEY=your_google_places_api_key
   GOOGLE_MY_BUSINESS_API_KEY=your_google_my_business_api_key
   JWT_SECRET=your_jwt_secret_here
   PORT=3001
   ```

### Phase 3: Widget Runtime Deployment
1. **Deploy Widget Runtime Service**
   ```bash
   # Create another service from same GitHub repo
   # Set root directory to: packages/widget-runtime
   # Set build command to: npm run build
   # Set start command to: npm run preview
   ```

2. **Configure Environment Variables**
   ```env
   NODE_ENV=production
   PORT=3002
   ```

### Phase 4: Frontend Deployment
1. **Deploy Frontend Service**
   ```bash
   # Create third service from same GitHub repo
   # Set root directory to: packages/frontend
   # Set build command to: npm run build
   # Set start command to: npm run preview
   ```

2. **Configure Environment Variables**
   ```env
   NODE_ENV=production
   VITE_API_BASE_URL=https://your-backend-service.railway.app
   VITE_WIDGET_CDN_URL=https://your-widget-runtime-service.railway.app
   PORT=3000
   ```

## 🌐 Domain Configuration

### Option 1: Railway Subdomains (Free)
Railway will provide URLs like:
- Frontend: `https://your-frontend-service.railway.app`
- Backend: `https://your-backend-service.railway.app`
- Widget CDN: `https://your-widget-runtime-service.railway.app`

### Option 2: Custom Domains (Recommended)
1. **Purchase domains** (e.g., from Namecheap, Cloudflare)
2. **Configure in Railway**:
   - Frontend: `app.yourdomain.com`
   - Backend: `api.yourdomain.com`
   - Widget CDN: `cdn.yourdomain.com`

## 📁 Required Railway Configuration Files

### 1. Backend Railway Config
Create `packages/backend/railway.json`:
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 2. Frontend Railway Config
Create `packages/frontend/railway.json`:
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm run preview",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 3. Widget Runtime Railway Config
Create `packages/widget-runtime/railway.json`:
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm run preview",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

## 🔧 Update Package.json Scripts

### Backend package.json
Add preview script to `packages/backend/package.json`:
```json
{
  "scripts": {
    "preview": "node dist/index.js"
  }
}
```

### Frontend package.json
Add preview script to `packages/frontend/package.json`:
```json
{
  "scripts": {
    "preview": "vite preview --host 0.0.0.0 --port $PORT"
  }
}
```

### Widget Runtime package.json
Add preview script to `packages/widget-runtime/package.json`:
```json
{
  "scripts": {
    "preview": "vite preview --host 0.0.0.0 --port $PORT"
  }
}
```

## 🚀 Deployment Steps

### Step 1: Prepare Repository
```bash
# Commit all changes
git add .
git commit -m "Prepare for Railway deployment"
git push origin main
```

### Step 2: Create Railway Project
1. Go to [railway.app](https://railway.app)
2. Sign in with GitHub
3. Click "New Project"
4. Select "Deploy from GitHub repo"
5. Choose your repository

### Step 3: Deploy Services

**IMPORTANT**: This is a monorepo, so you need to deploy each service separately with the correct root directory.

1. **Deploy Backend Service**
   - Click "New Service" → "GitHub Repo"
   - Select your `google-reviews-widget` repository
   - **Set Root Directory**: `packages/backend`
   - **Set Build Command**: `npm run build`
   - **Set Start Command**: `npm start`
   - Add PostgreSQL and Redis services
   - Configure environment variables (see below)

2. **Deploy Widget Runtime Service**
   - Click "New Service" → "GitHub Repo" 
   - Select your `google-reviews-widget` repository
   - **Set Root Directory**: `packages/widget-runtime`
   - **Set Build Command**: `npm run build`
   - **Set Start Command**: `npm run preview`
   - Configure environment variables (see below)

3. **Deploy Frontend Service**
   - Click "New Service" → "GitHub Repo"
   - Select your `google-reviews-widget` repository  
   - **Set Root Directory**: `packages/frontend`
   - **Set Build Command**: `npm run build`
   - **Set Start Command**: `npm run preview`
   - Configure environment variables (see below)

### Step 4: Configure Domains (Optional)
1. Add custom domains in Railway dashboard
2. Update DNS records with your domain provider
3. Update environment variables with new URLs

## 💰 Cost Estimation

Railway pricing (as of 2024):
- **Hobby Plan**: $5/month per service
- **PostgreSQL**: ~$5/month
- **Redis**: ~$3/month

**Total estimated cost**: ~$18-25/month for all services

**Pro Plan**: $20/month (better for production)
- Higher resource limits
- Priority support
- Advanced metrics

## 🔍 Testing Your Deployment

### 1. Test API Endpoints
```bash
curl https://your-backend-service.railway.app/health
```

### 2. Test Widget Platform
```bash
curl https://your-widget-runtime-service.railway.app/widget-platform.js
```

### 3. Test Frontend
Visit `https://your-frontend-service.railway.app`

### 4. Test Embed Code
Create a test HTML file:
```html
<!DOCTYPE html>
<html>
<head>
    <title>Widget Test</title>
</head>
<body>
    <h1>Testing Widget</h1>
    
    <!-- Your compact embed code -->
    <script src="https://your-widget-runtime-service.railway.app/widget-platform.js" async></script>
    <div class="grw-widget-test-123" data-grw-widget-lazy></div>
</body>
</html>
```

## 🛠️ Maintenance & Updates

### Automatic Deployments
Railway will automatically deploy when you push to your main branch.

### Manual Deployments
You can trigger manual deployments from the Railway dashboard.

### Environment Updates
Update environment variables through the Railway dashboard without redeployment.

### Monitoring
Railway provides built-in monitoring, logs, and metrics for all services.

## 🎯 Next Steps

1. **Set up Railway account** and connect GitHub
2. **Deploy backend service** with database
3. **Deploy widget runtime service**
4. **Deploy frontend service**
5. **Test the complete flow**
6. **Configure custom domains** (optional)
7. **Set up monitoring alerts**

Railway makes this deployment incredibly straightforward compared to managing your own infrastructure!
