version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: packages/frontend/Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./packages/frontend:/app/packages/frontend
      - /app/packages/frontend/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001
    depends_on:
      - backend

  backend:
    build:
      context: .
      dockerfile: packages/backend/Dockerfile.dev
    ports:
      - "3001:3001"
    volumes:
      - ./packages/backend:/app/packages/backend
      - /app/packages/backend/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://redis:6379
      - GOOGLE_PLACES_API_KEY=${GOOGLE_PLACES_API_KEY}
      - GOOGLE_MY_BUSINESS_API_KEY=${GOOGLE_MY_BUSINESS_API_KEY}
    depends_on:
      - redis

  widget-runtime:
    build:
      context: .
      dockerfile: packages/widget-runtime/Dockerfile.dev
    ports:
      - "3002:3002"
    volumes:
      - ./packages/widget-runtime:/app/packages/widget-runtime
      - /app/packages/widget-runtime/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data: