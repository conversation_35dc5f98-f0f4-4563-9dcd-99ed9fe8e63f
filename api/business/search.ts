import { VercelRequest, VercelResponse } from '@vercel/node';

export default async function handler(req: VercelRequest, res: VercelResponse) {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { query, location, googleMapsUrl } = req.query;

        // Log the incoming request for debugging (v3 - fresh deployment)
        console.log('Business search request v3:', { query, location, googleMapsUrl });

        if (!query || typeof query !== 'string') {
            return res.status(400).json({ error: 'Query parameter required' });
        }

        const apiKey = process.env.GOOGLE_PLACES_API_KEY;
        if (!apiKey) {
            console.error('Google Places API key not found');
            return res.status(500).json({ error: 'Google Places API key not configured' });
        }

        // Build the search query
        let searchQuery = query;
        if (location && typeof location === 'string') {
            searchQuery = `${query} ${location}`;
        }

        console.log('Searching Google Places for:', searchQuery);

        // Call Google Places Text Search API
        const placesUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(searchQuery)}&key=${apiKey}`;

        const response = await fetch(placesUrl);
        const data = await response.json();

        console.log('Google Places API response status:', data.status);
        console.log('Google Places API results count:', data.results?.length || 0);

        if (data.status !== 'OK') {
            console.error('Google Places API error:', data.status, data.error_message);
            return res.status(500).json({
                error: `Google Places API error: ${data.status}`,
                details: data.error_message
            });
        }

        // Transform Google Places results to our format
        const businesses = data.results.slice(0, 10).map((place: any, index: number) => ({
            id: `business-${index + 1}`,
            placeId: place.place_id,
            name: place.name,
            address: place.formatted_address || 'Address not available',
            rating: place.rating || 0,
            reviewCount: place.user_ratings_total || 0,
            photoUrl: place.photos?.[0]
                ? `https://maps.googleapis.com/maps/api/place/photo?maxwidth=150&photoreference=${place.photos[0].photo_reference}&key=${apiKey}`
                : 'https://via.placeholder.com/150x150',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }));

        console.log('Returning businesses:', businesses.length);

        res.status(200).json({
            success: true,
            businesses
        });
    } catch (error) {
        console.error('Business search error:', error);
        res.status(500).json({
            error: 'Internal server error',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}
