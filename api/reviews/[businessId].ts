import { VercelRequest, VercelResponse } from '@vercel/node';

export default async function handler(req: VercelRequest, res: VercelResponse) {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
        const { businessId } = req.query;
        if (!businessId || typeof businessId !== 'string') {
            return res.status(400).json({ error: 'Business ID required' });
        }

        // Mock reviews data - replace with actual Google Places API
        const mockReviews = [
            {
                id: 'review-1',
                author: '<PERSON>',
                rating: 5,
                text: 'Excellent service and great food! Highly recommend this place.',
                date: '2024-01-15',
                avatar: 'https://via.placeholder.com/40x40'
            },
            {
                id: 'review-2',
                author: '<PERSON>',
                rating: 4,
                text: 'Good experience overall. The staff was friendly and the atmosphere was nice.',
                date: '2024-01-10',
                avatar: 'https://via.placeholder.com/40x40'
            },
            {
                id: 'review-3',
                author: 'Mike Wilson',
                rating: 5,
                text: 'Amazing place! Will definitely come back again.',
                date: '2024-01-05',
                avatar: 'https://via.placeholder.com/40x40'
            }
        ];

        const businessInfo = {
            id: businessId,
            name: 'Sample Business',
            rating: 4.6,
            reviewCount: 127,
            address: '123 Main St, City, State'
        };

        res.status(200).json({
            success: true,
            business: businessInfo,
            reviews: mockReviews
        });
    } catch (error) {
        console.error('Reviews fetch error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
}
