import { VercelRequest, VercelResponse } from '@vercel/node';

export default async function handler(req: VercelRequest, res: VercelResponse) {
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
        return res.status(400).json({ error: 'Widget ID required' });
    }

    // Return HTML page for widget preview
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Reviews Widget Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .preview-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .widget-container {
            display: flex;
            justify-content: center;
            min-height: 300px;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>Google Reviews Widget Preview</h1>
            <div class="info-box">
                <strong>Widget ID:</strong> ${id}<br>
                <strong>Status:</strong> Loading widget...
            </div>
        </div>
        <div class="widget-container">
            <div class="grw-widget-${id}" data-grw-widget-lazy></div>
        </div>
    </div>
    
    <!-- Load Widget Platform Script -->
    <script src="/widget-platform.js" async></script>
    
    <script>
        // Add some debugging
        window.addEventListener('load', function() {
            console.log('Widget preview page loaded');
            console.log('Widget ID: ${id}');
        });
        
        // Check for errors
        window.addEventListener('error', function(e) {
            console.error('Widget error:', e.error);
        });
    </script>
</body>
</html>
    `;

    res.setHeader('Content-Type', 'text/html');
    res.status(200).send(html);
}
