import { VercelRequest, VercelResponse } from '@vercel/node';

export default async function handler(req: VercelRequest, res: VercelResponse) {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    const { id } = req.query;
    if (!id || typeof id !== 'string') {
        return res.status(400).json({ error: 'Widget ID required' });
    }

    try {
        if (req.method === 'GET') {
            // Mock widget configuration with proper structure for widget platform
            const mockConfig = {
                id: id,
                businessId: 'business-1',
                template: 'modern',
                styling: {
                    colors: {
                        primary: '#4285f4',
                        secondary: '#ffc107',
                        background: '#ffffff',
                        text: '#333333',
                        border: '#e0e0e0'
                    },
                    fonts: {
                        family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                        size: '14px',
                        weight: '400'
                    },
                    dimensions: {
                        width: '100%',
                        height: 'auto',
                        borderRadius: '8px'
                    },
                    spacing: {
                        padding: '16px',
                        margin: '0',
                        gap: '12px'
                    }
                },
                settings: {
                    maxReviews: 5,
                    minRating: 1,
                    sortBy: 'newest',
                    showPhotos: true,
                    showDates: true,
                    autoRefresh: false
                },
                headerSettings: {
                    showHeader: true,
                    headerText: 'Customer Reviews',
                    headerSize: 'medium',
                    headerColor: '#333333',
                    headerAlign: 'center',
                    showSubheader: true,
                    subheaderText: 'What our customers are saying',
                    subheaderColor: '#666666',
                    subheaderAlign: 'center'
                },
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z'
            };

            return res.status(200).json({
                success: true,
                config: mockConfig
            });
        }

        if (req.method === 'POST' || req.method === 'PUT') {
            const { businessId, template, settings } = req.body;

            // Mock save operation - replace with database save
            const savedConfig = {
                id: id,
                businessId,
                template,
                settings,
                createdAt: req.method === 'POST' ? new Date().toISOString() : '2024-01-01T00:00:00Z',
                updatedAt: new Date().toISOString()
            };

            return res.status(200).json({
                success: true,
                config: savedConfig
            });
        }

        return res.status(405).json({ error: 'Method not allowed' });
    } catch (error) {
        console.error('Widget config error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
}
