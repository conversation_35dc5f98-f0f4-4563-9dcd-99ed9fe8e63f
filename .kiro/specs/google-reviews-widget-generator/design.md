# Design Document

## Overview

The Google Reviews Widget Generator is a web application that enables users to create customizable, embeddable widgets displaying Google reviews for businesses. The system consists of a React-based frontend application for widget configuration, a Node.js backend API for Google integration and data processing, and a lightweight widget runtime that can be embedded on any website.

The architecture follows a microservices approach with clear separation between the widget generator interface, the API services, and the embeddable widget runtime. This design ensures scalability, maintainability, and optimal performance for both the configuration interface and the embedded widgets.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        WG[Widget Generator UI]
        EW[Embedded Widget]
    end
    
    subgraph "Backend Services"
        API[Main API Server]
        WS[Widget Service]
        CS[Cache Service]
    end
    
    subgraph "External Services"
        GPA[Google Places API]
        GMB[Google My Business API]
    end
    
    subgraph "Data Storage"
        DB[(Database)]
        RC[(Redis Cache)]
        CDN[CDN/Static Assets]
    end
    
    WG --> API
    EW --> WS
    API --> GPA
    API --> GMB
    API --> DB
    API --> RC
    WS --> RC
    WS --> CDN
    CS --> RC
```

### Component Architecture

The system is divided into three main components:

1. **Widget Generator Application**: React SPA for creating and customizing widgets
2. **Backend API Services**: Node.js/Express services handling Google API integration, data processing, and widget configuration storage
3. **Embeddable Widget Runtime**: Lightweight JavaScript widget that can be embedded on any website

## Components and Interfaces

### Frontend Components

#### Widget Generator Interface
- **BusinessSearchComponent**: Handles business search using Google Places API
- **TemplateSelectionComponent**: Displays available widget templates with live previews
- **CustomizationPanelComponent**: Provides styling and configuration options
- **PreviewComponent**: Real-time widget preview with responsive testing
- **CodeGeneratorComponent**: Generates and displays embed code

#### Widget Runtime
- **WidgetRenderer**: Main widget rendering engine
- **ReviewsDisplayComponent**: Handles different layout types (carousel, grid, list)
- **ReviewCardComponent**: Individual review display component
- **NavigationComponent**: Handles pagination/carousel navigation

### Backend Services

#### Main API Server
- **BusinessSearchService**: Integrates with Google Places API for business discovery
- **ReviewsService**: Fetches and processes Google reviews data
- **WidgetConfigService**: Manages widget configurations and templates
- **EmbedCodeService**: Generates optimized embed code

#### Widget Service
- **WidgetDataService**: Serves widget data to embedded widgets
- **CacheManager**: Handles review data caching and refresh strategies
- **AssetService**: Serves widget CSS and JavaScript assets

### API Interfaces

#### Business Search API
```typescript
interface BusinessSearchRequest {
  query: string;
  location?: string;
  googleMapsUrl?: string;
}

interface BusinessSearchResponse {
  businesses: Business[];
  totalResults: number;
}

interface Business {
  placeId: string;
  name: string;
  address: string;
  rating: number;
  reviewCount: number;
  photoUrl?: string;
}
```

#### Widget Configuration API
```typescript
interface WidgetConfig {
  id: string;
  businessId: string;
  template: TemplateType;
  styling: WidgetStyling;
  settings: WidgetSettings;
  createdAt: Date;
  updatedAt: Date;
}

interface WidgetStyling {
  colors: ColorScheme;
  fonts: FontSettings;
  dimensions: DimensionSettings;
  spacing: SpacingSettings;
}

interface WidgetSettings {
  maxReviews: number;
  minRating: number;
  sortBy: SortOption;
  showPhotos: boolean;
  showDates: boolean;
  autoRefresh: boolean;
}
```

#### Reviews Data API
```typescript
interface ReviewsResponse {
  reviews: Review[];
  businessInfo: BusinessInfo;
  totalReviews: number;
  averageRating: number;
  lastUpdated: Date;
}

interface Review {
  id: string;
  authorName: string;
  authorPhotoUrl?: string;
  rating: number;
  text: string;
  publishedDate: Date;
  isVerified: boolean;
}
```

## Data Models

### Database Schema

#### Businesses Table
```sql
CREATE TABLE businesses (
  id UUID PRIMARY KEY,
  place_id VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  address TEXT,
  rating DECIMAL(2,1),
  review_count INTEGER,
  photo_url TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Widget Configurations Table
```sql
CREATE TABLE widget_configs (
  id UUID PRIMARY KEY,
  business_id UUID REFERENCES businesses(id),
  template_type VARCHAR(50) NOT NULL,
  styling_config JSONB,
  widget_settings JSONB,
  embed_code TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Reviews Cache Table
```sql
CREATE TABLE reviews_cache (
  id UUID PRIMARY KEY,
  business_id UUID REFERENCES businesses(id),
  reviews_data JSONB,
  last_fetched TIMESTAMP,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Redis Cache Structure

#### Review Data Cache
```
Key: reviews:{businessId}
Value: {
  reviews: Review[],
  businessInfo: BusinessInfo,
  lastUpdated: timestamp,
  expiresAt: timestamp
}
TTL: 1 hour
```

#### Widget Configuration Cache
```
Key: widget:{widgetId}
Value: WidgetConfig
TTL: 24 hours
```

## Error Handling

### API Error Handling Strategy

#### Google API Errors
- **Rate Limit Exceeded**: Implement exponential backoff and queue requests
- **Invalid Place ID**: Return user-friendly error with search suggestions
- **API Key Issues**: Log securely and return generic error to client
- **Network Timeouts**: Retry with cached data fallback

#### Widget Runtime Errors
- **Failed to Load Reviews**: Display cached data or graceful fallback message
- **Network Connectivity**: Implement offline-first approach with cached data
- **Invalid Configuration**: Use default template with error logging
- **Cross-Origin Issues**: Provide CORS-compliant embed code

### Error Response Format
```typescript
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: Date;
  };
}
```

### Client-Side Error Handling
- Implement global error boundary in React application
- Provide user-friendly error messages with actionable suggestions
- Log errors to monitoring service for debugging
- Implement retry mechanisms for transient failures

## Testing Strategy

### Unit Testing
- **Frontend Components**: Jest + React Testing Library for all UI components
- **Backend Services**: Jest for business logic and API endpoints
- **Widget Runtime**: Jest for widget rendering and interaction logic
- **Utility Functions**: Comprehensive unit tests for data processing and validation

### Integration Testing
- **API Integration**: Test Google Places API integration with mock responses
- **Database Operations**: Test all CRUD operations with test database
- **Widget Embedding**: Test widget functionality in various website contexts
- **Cross-Browser Compatibility**: Test widget rendering across major browsers

### End-to-End Testing
- **User Workflows**: Cypress tests for complete widget creation flow
- **Widget Functionality**: Test embedded widgets in real website scenarios
- **Performance Testing**: Load testing for API endpoints and widget rendering
- **Accessibility Testing**: Ensure WCAG compliance for all components

### Testing Data Management
- Use factory pattern for generating test data
- Implement database seeding for consistent test environments
- Mock external API calls with realistic response data
- Create reusable test utilities for common operations

### Performance Testing
- **API Response Times**: Target <200ms for widget data requests
- **Widget Load Times**: Target <1s for initial widget render
- **Memory Usage**: Monitor for memory leaks in long-running widgets
- **Bundle Size**: Keep widget runtime <50KB gzipped

### Security Testing
- **API Security**: Test authentication, authorization, and input validation
- **XSS Prevention**: Test widget embedding for script injection vulnerabilities
- **CORS Configuration**: Verify proper cross-origin resource sharing setup
- **Data Privacy**: Ensure no sensitive data exposure in widget code