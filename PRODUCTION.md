# Production Deployment Guide

This document provides comprehensive instructions for deploying the Google Reviews Widget Generator to production.

## Prerequisites

### System Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (for local development)
- PostgreSQL 15+ (if not using Docker)
- Redis 7+ (if not using Docker)

### Environment Setup
1. Copy `.env.production` and configure with your values
2. Set up domain names and SSL certificates
3. Configure Google API keys
4. Set up monitoring credentials

## Deployment Process

### 1. Pre-deployment Checklist
- [ ] All tests passing (`npm run test:all`)
- [ ] Environment variables configured
- [ ] Database backup created
- [ ] SSL certificates ready
- [ ] Monitoring stack configured
- [ ] Alert channels configured (Slack, email)

### 2. Initial Deployment
```bash
# Clone repository
git clone <repository-url>
cd google-reviews-widget-generator

# Configure environment
cp .env.example .env.production
# Edit .env.production with your values

# Deploy
./scripts/deploy.sh production
```

### 3. Post-deployment Verification
- [ ] All services healthy
- [ ] Frontend accessible
- [ ] API endpoints responding
- [ ] Widget embedding works
- [ ] Database migrations applied
- [ ] Monitoring dashboards showing data
- [ ] Alerts configured and working

## Infrastructure Components

### Application Services
- **Frontend**: React SPA served by Nginx
- **Backend**: Node.js API server
- **Widget Runtime**: Embeddable widget assets
- **Database**: PostgreSQL with optimized configuration
- **Cache**: Redis for session and data caching

### Monitoring Stack
- **Prometheus**: Metrics collection
- **Grafana**: Dashboards and visualization
- **Loki**: Log aggregation
- **Promtail**: Log shipping
- **AlertManager**: Alert routing and notification

### Reverse Proxy
- **Traefik**: Load balancing and SSL termination
- Automatic SSL certificate management with Let's Encrypt

## Configuration

### Database Optimization
```sql
-- Production PostgreSQL settings
shared_preload_libraries = 'pg_stat_statements'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
```

### Redis Configuration
```
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### Nginx Caching
- Static assets: 1 year cache
- HTML files: 1 hour cache
- API responses: 5 minutes cache
- Widget assets: 1 year cache with immutable flag

## Monitoring and Alerting

### Key Metrics
- HTTP request rate and latency
- Error rates by endpoint
- Database connection pool usage
- Redis memory usage
- Google API rate limits and errors
- Widget load times and errors

### Critical Alerts
- Service downtime
- High error rates (>5%)
- Database connection failures
- High response times (>1s 95th percentile)
- Disk space low (<10%)
- Memory usage high (>90%)

### Dashboards
- Application Overview
- Infrastructure Metrics
- Google API Usage
- Widget Performance
- Error Tracking

## Backup and Recovery

### Database Backups
- Automated daily backups via cron
- Retention: 30 days local, 90 days cloud storage
- Point-in-time recovery capability
- Backup verification and restoration testing

### Application Data
- Configuration backups
- Log retention (30 days)
- Metrics retention (200 hours)

## Security

### Network Security
- All services behind reverse proxy
- SSL/TLS encryption for all external traffic
- Internal service communication over Docker network
- Rate limiting on API endpoints

### Application Security
- Input validation and sanitization
- CORS configuration
- Security headers (CSP, HSTS, etc.)
- Regular dependency updates
- Vulnerability scanning in CI/CD

### Data Protection
- Environment variables for secrets
- Database connection encryption
- API key rotation procedures
- Access logging and monitoring

## Scaling

### Horizontal Scaling
- Frontend: Multiple Nginx instances behind load balancer
- Backend: Multiple API server instances
- Database: Read replicas for read-heavy workloads
- Redis: Redis Cluster for high availability

### Vertical Scaling
- Monitor resource usage and scale containers accordingly
- Database connection pool tuning
- Redis memory optimization

## Maintenance

### Regular Tasks
- Database maintenance (`./packages/backend/scripts/db-maintenance.sh`)
- Log rotation and cleanup
- Security updates
- Performance monitoring and optimization
- Backup verification

### Scheduled Maintenance
- Weekly: Database statistics update
- Monthly: Full system backup verification
- Quarterly: Security audit and dependency updates

## Troubleshooting

### Common Issues
1. **Service won't start**: Check logs with `docker-compose logs <service>`
2. **Database connection errors**: Verify DATABASE_URL and network connectivity
3. **High memory usage**: Check for memory leaks, optimize queries
4. **Slow API responses**: Review database indexes and query performance
5. **Widget loading issues**: Check CDN configuration and CORS headers

### Log Locations
- Application logs: `/var/log/grwg/`
- Container logs: `docker-compose logs <service>`
- System logs: `/var/log/syslog`
- Nginx access logs: Container logs or mounted volume

### Performance Debugging
1. Check Grafana dashboards for anomalies
2. Review slow query logs in PostgreSQL
3. Monitor Redis memory usage and hit rates
4. Analyze application metrics in Prometheus
5. Use APM tools for detailed request tracing

## Rollback Procedures

### Application Rollback
```bash
# Rollback to previous version
git checkout <previous-tag>
./scripts/deploy.sh production

# Or rollback specific service
docker-compose -f docker-compose.prod.yml up -d --no-deps <service>
```

### Database Rollback
```bash
# Restore from backup
./packages/backend/scripts/db-restore.sh <backup-file>
```

## Support and Monitoring

### Health Checks
- `/health` endpoints on all services
- Automated health monitoring
- Uptime monitoring with external services

### Performance Monitoring
- Real User Monitoring (RUM) for frontend
- API performance tracking
- Widget load time monitoring
- Database query performance

### Error Tracking
- Centralized error logging
- Error rate monitoring and alerting
- Stack trace collection and analysis

## Contact Information

- **Operations Team**: <EMAIL>
- **Development Team**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX
- **Slack Channel**: #production-alerts