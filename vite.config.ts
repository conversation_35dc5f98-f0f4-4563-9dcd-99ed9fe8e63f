import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [
    react(),
    // Bundle analyzer plugin for development
    ...(process.env.ANALYZE ? [{
      name: 'bundle-analyzer',
      generateBundle(options, bundle) {
        const bundleInfo = Object.entries(bundle).map(([name, chunk]) => ({
          name,
          size: 'code' in chunk ? chunk.code.length : 0,
          type: chunk.type
        }));
        
        console.log('\n📦 Bundle Analysis:');
        bundleInfo.forEach(({ name, size, type }) => {
          console.log(`  ${name} (${type}): ${(size / 1024).toFixed(2)} KB`);
        });
        
        const totalSize = bundleInfo.reduce((sum, { size }) => sum + size, 0);
        console.log(`  Total: ${(totalSize / 1024).toFixed(2)} KB\n`);
      }
    }] : [])
  ],
  server: {
    port: 3000,
    host: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    // Optimize chunk splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunk for React and related libraries
          vendor: ['react', 'react-dom', 'react-router-dom'],
          // UI libraries chunk
          ui: ['styled-components', 'lucide-react'],
          // Form and validation libraries
          forms: ['react-hook-form', '@hookform/resolvers', 'zod'],
          // HTTP and state management
          api: ['axios', '@tanstack/react-query'],
        },
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop().replace('.tsx', '').replace('.ts', '') : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
      },
    },
    // Enable minification and compression
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
      },
    },
    // Set chunk size warning limit
    chunkSizeWarningLimit: 1000,
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
    css: true, // Enable CSS processing for styled-components
  },
});