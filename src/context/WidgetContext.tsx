import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { WidgetConfig, WidgetContextType, TemplateType } from '../types/widget';

// Default widget configuration
const defaultConfig: WidgetConfig = {
  template: 'carousel',
  styling: {
    colors: {
      primary: '#4285f4',
      secondary: '#34a853',
      background: '#ffffff',
      text: '#333333',
      border: '#e0e0e0',
    },
    fonts: {
      family: 'Inter, sans-serif',
      size: '14px',
      weight: '400',
    },
    dimensions: {
      width: '400px',
      height: '300px',
      borderRadius: '8px',
    },
    spacing: {
      padding: '16px',
      margin: '0px',
      gap: '12px',
    },
  },
  settings: {
    maxReviews: 5,
    minRating: 4,
    sortBy: 'newest',
    showPhotos: true,
    showDates: true,
    autoRefresh: true,
  },
  headerSettings: {
    showHeader: true,
    headerText: 'Customer Reviews',
    headerSize: 'medium',
    headerColor: '#333333',
    headerAlign: 'center',
    showSubheader: true,
    subheaderText: 'What our customers are saying',
    subheaderColor: '#666666',
    subheaderAlign: 'center',
  },
};

// Action types for reducer
type WidgetAction =
  | { type: 'UPDATE_CONFIG'; payload: Partial<WidgetConfig> }
  | { type: 'RESET_CONFIG' }
  | { type: 'SET_STEP'; payload: number };

// State interface
interface WidgetState {
  config: WidgetConfig;
  currentStep: number;
}

// Reducer function
function widgetReducer(state: WidgetState, action: WidgetAction): WidgetState {
  switch (action.type) {
    case 'UPDATE_CONFIG':
      return {
        ...state,
        config: {
          ...state.config,
          ...action.payload,
          styling: action.payload.styling
            ? { ...state.config.styling, ...action.payload.styling }
            : state.config.styling,
          settings: action.payload.settings
            ? { ...state.config.settings, ...action.payload.settings }
            : state.config.settings,
          headerSettings: action.payload.headerSettings
            ? { ...state.config.headerSettings, ...action.payload.headerSettings }
            : state.config.headerSettings,
        },
      };
    case 'RESET_CONFIG':
      return {
        ...state,
        config: { ...defaultConfig },
        currentStep: 0,
      };
    case 'SET_STEP':
      return {
        ...state,
        currentStep: action.payload,
      };
    default:
      return state;
  }
}

// Create context
const WidgetContext = createContext<WidgetContextType | undefined>(undefined);

// Provider component
interface WidgetProviderProps {
  children: ReactNode;
}

export function WidgetProvider({ children }: WidgetProviderProps) {
  // Initialize state with localStorage data if available
  const initializeState = (): WidgetState => {
    try {
      const savedState = localStorage.getItem('widgetState');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        return {
          config: { ...defaultConfig, ...parsed.config },
          currentStep: parsed.currentStep || 0,
        };
      }
    } catch (error) {
      console.warn('Failed to load widget state from localStorage:', error);
    }
    return {
      config: { ...defaultConfig },
      currentStep: 0,
    };
  };

  const [state, dispatch] = useReducer(widgetReducer, initializeState());

  // Save state to localStorage whenever it changes
  React.useEffect(() => {
    try {
      localStorage.setItem('widgetState', JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save widget state to localStorage:', error);
    }
  }, [state]);

  const updateConfig = (updates: Partial<WidgetConfig>) => {
    dispatch({ type: 'UPDATE_CONFIG', payload: updates });
  };

  const resetConfig = () => {
    dispatch({ type: 'RESET_CONFIG' });
  };

  const setCurrentStep = (step: number) => {
    dispatch({ type: 'SET_STEP', payload: step });
  };

  const value: WidgetContextType = {
    config: state.config,
    updateConfig,
    resetConfig,
    currentStep: state.currentStep,
    setCurrentStep,
  };

  return (
    <WidgetContext.Provider value={value}>
      {children}
    </WidgetContext.Provider>
  );
}

// Custom hook to use the context
export function useWidget() {
  const context = useContext(WidgetContext);
  if (context === undefined) {
    throw new Error('useWidget must be used within a WidgetProvider');
  }
  return context;
}
