// Performance monitoring utilities
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  bundleSize?: number;
  memoryUsage?: number;
  timestamp: number;
}

export interface ComponentMetrics {
  componentName: string;
  mountTime: number;
  renderTime: number;
  updateCount: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private componentMetrics: Map<string, ComponentMetrics> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initPerformanceObservers();
  }

  private initPerformanceObservers(): void {
    if (typeof PerformanceObserver === 'undefined') {
      return;
    }

    // Observe navigation timing
    try {
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
          }
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);
    } catch (error) {
      console.warn('Navigation timing observer not supported:', error);
    }

    // Observe resource timing
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.recordResourceMetrics(entry as PerformanceResourceTiming);
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    } catch (error) {
      console.warn('Resource timing observer not supported:', error);
    }

    // Observe paint timing
    try {
      const paintObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'paint') {
            this.recordPaintMetrics(entry as PerformancePaintTiming);
          }
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (error) {
      console.warn('Paint timing observer not supported:', error);
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics: PerformanceMetrics = {
      loadTime: entry.loadEventEnd - entry.fetchStart,
      renderTime: entry.domContentLoadedEventEnd - entry.fetchStart,
      timestamp: Date.now()
    };

    // Add memory usage if available
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      metrics.memoryUsage = memory.usedJSHeapSize;
    }

    this.metrics.push(metrics);
    this.reportMetrics(metrics);
  }

  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    // Track bundle sizes for JS and CSS files
    if (entry.name.includes('.js') || entry.name.includes('.css')) {
      const size = entry.transferSize || entry.encodedBodySize;
      console.log(`Resource loaded: ${entry.name}, Size: ${this.formatBytes(size)}, Load time: ${entry.duration.toFixed(2)}ms`);
    }
  }

  private recordPaintMetrics(entry: PerformancePaintTiming): void {
    console.log(`${entry.name}: ${entry.startTime.toFixed(2)}ms`);
  }

  public startComponentTiming(componentName: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      const existing = this.componentMetrics.get(componentName);
      if (existing) {
        existing.renderTime = duration;
        existing.updateCount++;
      } else {
        this.componentMetrics.set(componentName, {
          componentName,
          mountTime: duration,
          renderTime: duration,
          updateCount: 1
        });
      }
    };
  }

  public measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    
    return fn().then((result) => {
      const endTime = performance.now();
      console.log(`${name} completed in ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    }).catch((error) => {
      const endTime = performance.now();
      console.error(`${name} failed after ${(endTime - startTime).toFixed(2)}ms:`, error);
      throw error;
    });
  }

  public measureSync<T>(name: string, fn: () => T): T {
    const startTime = performance.now();
    try {
      const result = fn();
      const endTime = performance.now();
      console.log(`${name} completed in ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      console.error(`${name} failed after ${(endTime - startTime).toFixed(2)}ms:`, error);
      throw error;
    }
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getComponentMetrics(): ComponentMetrics[] {
    return Array.from(this.componentMetrics.values());
  }

  public getBundleAnalysis(): Promise<any> {
    return new Promise((resolve) => {
      // Analyze loaded resources
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const analysis = {
        totalSize: 0,
        jsSize: 0,
        cssSize: 0,
        imageSize: 0,
        resources: resources.map(resource => ({
          name: resource.name,
          size: resource.transferSize || resource.encodedBodySize,
          loadTime: resource.duration,
          type: this.getResourceType(resource.name)
        }))
      };

      analysis.resources.forEach(resource => {
        analysis.totalSize += resource.size;
        switch (resource.type) {
          case 'js':
            analysis.jsSize += resource.size;
            break;
          case 'css':
            analysis.cssSize += resource.size;
            break;
          case 'image':
            analysis.imageSize += resource.size;
            break;
        }
      });

      resolve(analysis);
    });
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'js';
    if (url.includes('.css')) return 'css';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/i)) return 'image';
    return 'other';
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private reportMetrics(metrics: PerformanceMetrics): void {
    // In development, log to console
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics');
      console.log(`Load Time: ${metrics.loadTime.toFixed(2)}ms`);
      console.log(`Render Time: ${metrics.renderTime.toFixed(2)}ms`);
      if (metrics.memoryUsage) {
        console.log(`Memory Usage: ${this.formatBytes(metrics.memoryUsage)}`);
      }
      console.groupEnd();
    }

    // In production, send to analytics service
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(metrics);
    }
  }

  private sendToAnalytics(metrics: PerformanceMetrics): void {
    // Example: Send to analytics service
    try {
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metrics)
      }).catch(() => {
        // Silently fail analytics
      });
    } catch {
      // Silently fail analytics
    }
  }

  public destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
    this.componentMetrics.clear();
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for component performance monitoring
export const usePerformanceMonitor = (componentName: string) => {
  const startTiming = () => performanceMonitor.startComponentTiming(componentName);
  
  return {
    startTiming,
    measureAsync: <T>(name: string, fn: () => Promise<T>) => 
      performanceMonitor.measureAsync(`${componentName}.${name}`, fn),
    measureSync: <T>(name: string, fn: () => T) => 
      performanceMonitor.measureSync(`${componentName}.${name}`, fn)
  };
};