import React from 'react';
import styled from 'styled-components';
import { BusinessSearchComponent } from '../components/BusinessSearch/BusinessSearchComponent';
import { Business } from '../types/widget';
import WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';

const PageContainer = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
`;

const SelectedBusinessInfo = styled.div`
  margin-top: 24px;
  padding: 16px;
  background-color: #f0f8ff;
  border: 1px solid #4285f4;
  border-radius: 8px;
`;

const SelectedBusinessTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
`;

const SelectedBusinessText = styled.p`
  font-size: 14px;
  color: #666666;
  margin: 0;
`;

export function SourcePage() {
  const handleBusinessSelect = (business: Business) => {
    console.log('Business selected:', business);
  };

  return (
    <PageContainer>
      <Title>Find Your Business</Title>
      <Description>
        Search for your business by name and address, or paste a Google Maps link to get started.
      </Description>
      <WidgetErrorBoundary widgetStep="business-search">
        <BusinessSearchComponent onBusinessSelect={handleBusinessSelect} />
      </WidgetErrorBoundary>
    </PageContainer>
  );
}