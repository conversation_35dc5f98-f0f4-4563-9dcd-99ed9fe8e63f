import React, { Suspense } from 'react';
import styled from 'styled-components';
import WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';

// Lazy load the header customization component
const HeaderCustomizationComponent = React.lazy(() =>
  import('../components/Customization/HeaderCustomizationComponent').then(module => ({
    default: module.HeaderCustomizationComponent
  }))
);

// Component loader
const ComponentLoader: React.FC = () => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '200px',
    fontSize: '14px',
    color: '#666'
  }}>
    <div style={{
      width: '20px',
      height: '20px',
      border: '2px solid #f3f3f3',
      borderTop: '2px solid #3498db',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginRight: '8px'
    }}></div>
    Loading header customization...
  </div>
);

const PageContainer = styled.div`
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 32px 16px;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

export function HeaderPage() {
  return (
    <PageContainer>
      <Title>Configure Header</Title>
      <Description>
        Customize the header text and appearance for your reviews widget.
        Configure the main title, subtitle, colors, and alignment to match your brand.
      </Description>
      <ContentContainer>
        <WidgetErrorBoundary widgetStep="header-customization">
          <Suspense fallback={<ComponentLoader />}>
            <HeaderCustomizationComponent />
          </Suspense>
        </WidgetErrorBoundary>
      </ContentContainer>
    </PageContainer>
  );
}
