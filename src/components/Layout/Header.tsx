import React from 'react';
import styled from 'styled-components';
import { useWidget } from '../../context/WidgetContext';

const HeaderContainer = styled.header`
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    padding: 0 16px;
    flex-direction: column;
    gap: 12px;
  }
`;

const Title = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 20px;
    text-align: center;
  }
`;

const Actions = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
`;

const ResetButton = styled.button`
  background: none;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
  }

  &:active {
    transform: translateY(1px);
  }
`;

const SaveButton = styled.button`
  background-color: #4285f4;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background-color: #3367d6;
  }

  &:active {
    transform: translateY(1px);
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
  }
`;

export function Header() {
  const { resetConfig, config } = useWidget();

  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset all configuration? This action cannot be undone.')) {
      resetConfig();
    }
  };

  const handleSave = () => {
    // TODO: Implement save functionality in later tasks
    console.log('Save configuration:', config);
    alert('Save functionality will be implemented in later tasks');
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <Title>Google Reviews Widget Generator</Title>
        <Actions>
          <ResetButton onClick={handleReset}>
            Reset
          </ResetButton>
          <SaveButton onClick={handleSave}>
            Save Configuration
          </SaveButton>
        </Actions>
      </HeaderContent>
    </HeaderContainer>
  );
}