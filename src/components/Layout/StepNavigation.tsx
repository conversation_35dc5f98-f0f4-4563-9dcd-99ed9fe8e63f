import React from 'react';
import styled from 'styled-components';
import { useNavigate, useLocation } from 'react-router-dom';
import { useWidget } from '../../context/WidgetContext';

const steps = [
  { id: 0, path: '/', label: 'Source', description: 'Find your business' },
  { id: 1, path: '/layout', label: 'Layout', description: 'Choose template' },
  { id: 2, path: '/header', label: 'Header', description: 'Configure header' },
  { id: 3, path: '/reviews', label: 'Reviews', description: 'Review settings' },
  { id: 4, path: '/style', label: 'Style', description: 'Customize appearance' },
  { id: 5, path: '/settings', label: 'Settings', description: 'Final configuration' },
];

const NavigationContainer = styled.nav`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
`;

const StepsList = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;

const ProgressLine = styled.div<{ progress: number }>`
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: ${props => props.progress}%;
    background-color: #4285f4;
    transition: width 0.3s ease;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const StepItem = styled.div<{ isActive: boolean; isCompleted: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 2;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    flex-direction: row;
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background-color: ${props => props.isActive ? '#f0f4ff' : 'transparent'};
    border: 1px solid ${props => props.isActive ? '#4285f4' : 'transparent'};
  }
`;

const StepCircle = styled.div<{ isActive: boolean; isCompleted: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  
  background-color: ${props => {
    if (props.isCompleted) return '#34a853';
    if (props.isActive) return '#4285f4';
    return '#e0e0e0';
  }};
  
  color: ${props => {
    if (props.isCompleted || props.isActive) return '#ffffff';
    return '#666666';
  }};

  @media (max-width: 768px) {
    margin-bottom: 0;
    margin-right: 12px;
  }
`;

const StepContent = styled.div`
  text-align: center;

  @media (max-width: 768px) {
    text-align: left;
    flex: 1;
  }
`;

const StepLabel = styled.div<{ isActive: boolean }>`
  font-weight: ${props => props.isActive ? '600' : '500'};
  font-size: 14px;
  color: ${props => props.isActive ? '#4285f4' : '#333333'};
  margin-bottom: 4px;
`;

const StepDescription = styled.div`
  font-size: 12px;
  color: #666666;
`;

export function StepNavigation() {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentStep, setCurrentStep } = useWidget();

  const handleStepClick = (step: typeof steps[0]) => {
    setCurrentStep(step.id);
    navigate(step.path);
  };

  const getCurrentStepFromPath = () => {
    const currentPath = location.pathname;
    const step = steps.find(s => s.path === currentPath);
    return step ? step.id : 0;
  };

  const activeStep = getCurrentStepFromPath();
  const progress = (activeStep / (steps.length - 1)) * 100;

  return (
    <NavigationContainer>
      <StepsList>
        <ProgressLine progress={progress} />
        {steps.map((step) => {
          const isActive = step.id === activeStep;
          const isCompleted = step.id < activeStep;
          
          return (
            <StepItem
              key={step.id}
              isActive={isActive}
              isCompleted={isCompleted}
              onClick={() => handleStepClick(step)}
            >
              <StepCircle isActive={isActive} isCompleted={isCompleted}>
                {isCompleted ? '✓' : step.id + 1}
              </StepCircle>
              <StepContent>
                <StepLabel isActive={isActive}>{step.label}</StepLabel>
                <StepDescription>{step.description}</StepDescription>
              </StepContent>
            </StepItem>
          );
        })}
      </StepsList>
    </NavigationContainer>
  );
}