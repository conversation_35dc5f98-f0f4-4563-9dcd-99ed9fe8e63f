import React, { useState, useCallback, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { useWidget } from '../../context/WidgetContext';
import { getReviewsByPlaceIdDirect } from '../../services/api';
import { Review } from '../../types/widget';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const Section = styled.div`
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e0e0e0;
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #333333;
`;

const Input = styled.input`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }
`;

const TextArea = styled.textarea`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  min-height: 80px;
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }
`;

const Select = styled.select`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  accent-color: #4285f4;
`;

const ColorInput = styled.input`
  width: 50px;
  height: 40px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  background: none;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  &::-webkit-color-swatch {
    border: none;
    border-radius: 6px;
  }
`;

const PreviewSection = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e0e0e0;
`;

const PreviewHeader = styled.div<{
  $showHeader: boolean;
  $headerColor: string;
  $headerSize: string;
  $headerAlign: string;
}>`
  display: ${props => props.$showHeader ? 'block' : 'none'};
  color: ${props => props.$headerColor};
  font-size: ${props => props.$headerSize === 'small' ? '16px' : props.$headerSize === 'medium' ? '20px' : '24px'};
  font-weight: 600;
  text-align: ${props => props.$headerAlign};
  margin-bottom: 12px;
`;

const PreviewSubheader = styled.div<{
  $showSubheader: boolean;
  $subheaderColor: string;
  $subheaderAlign: string;
}>`
  display: ${props => props.$showSubheader ? 'block' : 'none'};
  color: ${props => props.$subheaderColor};
  font-size: 14px;
  text-align: ${props => props.$subheaderAlign};
  margin-bottom: 16px;
  opacity: 0.8;
`;

const PreviewWidget = styled.div`
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const TemplatePreview = styled.div<{ $template: string }>`
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  min-height: 80px;
  
  ${props => props.$template === 'carousel' && `
    flex-direction: row;
    overflow-x: auto;
  `}
  
  ${props => props.$template === 'grid' && `
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  `}
  
  ${props => props.$template === 'badge' && `
    flex-direction: column;
    align-items: center;
  `}
  
  ${props => props.$template === 'simple-carousel' && `
    flex-direction: row;
    justify-content: space-between;
  `}
  
  ${props => props.$template === 'slider' && `
    flex-direction: column;
    position: relative;
  `}
  
  ${props => props.$template === 'floating-badge' && `
    position: relative;
    justify-content: flex-end;
    align-items: flex-end;
  `}
`;

const MockReview = styled.div`
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px;
  min-width: 120px;
  font-size: 12px;
  text-align: left;
`;

const MockStars = styled.div`
  color: #ffc107;
  font-size: 14px;
  margin-bottom: 4px;
`;

const MockText = styled.div`
  color: #666;
  font-size: 11px;
  line-height: 1.3;
`;

const TemplateLabel = styled.div`
  text-align: center;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
`;

const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
`;

const ErrorMessage = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #e74c3c;
  font-size: 12px;
  text-align: center;
`;

const RealReview = styled.div`
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px;
  min-width: 120px;
  font-size: 12px;
  text-align: left;
`;

const RealStars = styled.div`
  color: #ffc107;
  font-size: 14px;
  margin-bottom: 4px;
`;

const RealText = styled.div`
  color: #666;
  font-size: 11px;
  line-height: 1.3;
  margin-bottom: 4px;
`;

const RealAuthor = styled.div`
  color: #999;
  font-size: 10px;
  font-style: italic;
`;

const HelpText = styled.p`
  font-size: 12px;
  color: #666666;
  margin: 4px 0 0 0;
  line-height: 1.4;
`;

export function HeaderCustomizationComponent() {
  const { config, updateConfig } = useWidget();

  // State for real reviews
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [reviewsError, setReviewsError] = useState<string | null>(null);

  // Initialize header settings from config
  const [headerSettings, setHeaderSettings] = useState(() => ({
    showHeader: config.headerSettings?.showHeader ?? true,
    headerText: config.headerSettings?.headerText ?? (config.business?.name ? `${config.business.name} Reviews` : 'Customer Reviews'),
    headerSize: config.headerSettings?.headerSize ?? 'medium',
    headerColor: config.headerSettings?.headerColor ?? '#333333',
    headerAlign: config.headerSettings?.headerAlign ?? 'center',
    showSubheader: config.headerSettings?.showSubheader ?? true,
    subheaderText: config.headerSettings?.subheaderText ?? 'What our customers are saying',
    subheaderColor: config.headerSettings?.subheaderColor ?? '#666666',
    subheaderAlign: config.headerSettings?.subheaderAlign ?? 'center',
  }));

  // Fetch reviews when business is available
  useEffect(() => {
    if (config.business?.placeId) {
      setReviewsLoading(true);
      setReviewsError(null);

      getReviewsByPlaceIdDirect(config.business.placeId, {
        maxReviews: config.settings.maxReviews || 5,
        minRating: config.settings.minRating || 1,
        sortBy: config.settings.sortBy || 'newest'
      })
        .then((response: any) => {
          console.log('✅ Successfully fetched real reviews:', response);
          console.log('📝 Reviews data:', response.reviews);
          setReviews(response.reviews);
          setReviewsLoading(false);
        })
        .catch((error: any) => {
          console.error('❌ Failed to fetch reviews:', error);
          setReviewsError('Failed to load reviews');
          setReviewsLoading(false);
        });
    }
  }, [config.business?.placeId, config.settings.maxReviews, config.settings.minRating, config.settings.sortBy]);

  const handleSettingChange = useCallback((key: string, value: any) => {
    setHeaderSettings(prev => {
      const newSettings = {
        ...prev,
        [key]: value
      };

      // Defer the config update to avoid updating during render
      setTimeout(() => {
        updateConfig({
          headerSettings: newSettings
        });
      }, 0);

      return newSettings;
    });
  }, [updateConfig]);

  // Helper function to render stars
  const renderStars = (rating: number) => {
    return '★'.repeat(rating) + '☆'.repeat(5 - rating);
  };

  // Helper function to truncate text
  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Function to render reviews based on template and data availability
  const renderReviews = () => {
    if (reviewsLoading) {
      return <LoadingIndicator>Loading reviews...</LoadingIndicator>;
    }

    if (reviewsError || reviews.length === 0) {
      // Fallback to mock data when real reviews aren't available
      return renderMockReviews();
    }

    // Render real reviews
    const reviewsToShow = reviews.slice(0, getMaxReviewsForTemplate());

    if (config.template === 'badge') {
      const avgRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
      return (
        <RealReview style={{ textAlign: 'center', minWidth: '200px' }}>
          <RealStars>{renderStars(Math.round(avgRating))}</RealStars>
          <div style={{ fontSize: '16px', fontWeight: 'bold', margin: '8px 0' }}>
            {avgRating.toFixed(1)}/5
          </div>
          <RealText>Based on {reviews.length} reviews</RealText>
        </RealReview>
      );
    }

    if (config.template === 'floating-badge') {
      const avgRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
      return (
        <div style={{ position: 'relative', width: '100%', height: '60px', background: '#f0f0f0', borderRadius: '4px' }}>
          <RealReview style={{ position: 'absolute', bottom: '8px', right: '8px', minWidth: '100px' }}>
            <RealStars>{renderStars(Math.round(avgRating))}</RealStars>
            <RealText style={{ fontSize: '10px' }}>{avgRating.toFixed(1)}/5 ({reviews.length})</RealText>
          </RealReview>
        </div>
      );
    }

    if (config.template === 'slider') {
      const review = reviewsToShow[0];
      return (
        <RealReview style={{ width: '100%' }}>
          <RealStars>{renderStars(review.rating)}</RealStars>
          <RealText>"{truncateText(review.text, 80)}"</RealText>
          <RealAuthor>- {review.authorName}</RealAuthor>
          <div style={{ textAlign: 'center', marginTop: '8px', color: '#666', fontSize: '10px' }}>
            ● ○ ○
          </div>
        </RealReview>
      );
    }

    if (config.template === 'simple-carousel') {
      return (
        <>
          <RealReview>
            <RealStars>{renderStars(reviewsToShow[0]?.rating || 5)}</RealStars>
            <RealText>"{truncateText(reviewsToShow[0]?.text || 'Great!', 20)}"</RealText>
          </RealReview>
          <div style={{ color: '#666', fontSize: '12px' }}>•••</div>
          <RealReview>
            <RealStars>{renderStars(reviewsToShow[1]?.rating || 4)}</RealStars>
            <RealText>"{truncateText(reviewsToShow[1]?.text || 'Good service', 20)}"</RealText>
          </RealReview>
        </>
      );
    }

    // Default: carousel and grid
    return reviewsToShow.map((review, index) => (
      <RealReview key={review.id || index}>
        <RealStars>{renderStars(review.rating)}</RealStars>
        <RealText>"{truncateText(review.text, 40)}"</RealText>
        <RealAuthor>- {review.authorName}</RealAuthor>
      </RealReview>
    ));
  };

  // Function to get max reviews for each template
  const getMaxReviewsForTemplate = () => {
    switch (config.template) {
      case 'grid': return 4;
      case 'carousel': return 3;
      case 'simple-carousel': return 2;
      case 'slider': return 1;
      case 'badge':
      case 'floating-badge': return 0; // These show aggregated data
      default: return 3;
    }
  };

  // Fallback mock reviews function
  const renderMockReviews = () => {
    if (config.template === 'carousel') {
      return (
        <>
          <MockReview>
            <MockStars>★★★★★</MockStars>
            <MockText>"Great service and quality products!"</MockText>
          </MockReview>
          <MockReview>
            <MockStars>★★★★☆</MockStars>
            <MockText>"Very satisfied with my purchase."</MockText>
          </MockReview>
          <MockReview>
            <MockStars>★★★★★</MockStars>
            <MockText>"Excellent customer support."</MockText>
          </MockReview>
        </>
      );
    }

    if (config.template === 'grid') {
      return (
        <>
          <MockReview>
            <MockStars>★★★★★</MockStars>
            <MockText>"Amazing experience!"</MockText>
          </MockReview>
          <MockReview>
            <MockStars>★★★★☆</MockStars>
            <MockText>"Highly recommend."</MockText>
          </MockReview>
          <MockReview>
            <MockStars>★★★★★</MockStars>
            <MockText>"Top quality service."</MockText>
          </MockReview>
          <MockReview>
            <MockStars>★★★★☆</MockStars>
            <MockText>"Great value for money."</MockText>
          </MockReview>
        </>
      );
    }

    if (config.template === 'badge') {
      return (
        <MockReview style={{ textAlign: 'center', minWidth: '200px' }}>
          <MockStars>★★★★★</MockStars>
          <div style={{ fontSize: '16px', fontWeight: 'bold', margin: '8px 0' }}>4.8/5</div>
          <MockText>Based on 127 reviews</MockText>
        </MockReview>
      );
    }

    if (config.template === 'simple-carousel') {
      return (
        <>
          <MockReview>
            <MockStars>★★★★★</MockStars>
            <MockText>"Perfect!"</MockText>
          </MockReview>
          <div style={{ color: '#666', fontSize: '12px' }}>•••</div>
          <MockReview>
            <MockStars>★★★★☆</MockStars>
            <MockText>"Great service"</MockText>
          </MockReview>
        </>
      );
    }

    if (config.template === 'slider') {
      return (
        <MockReview style={{ width: '100%' }}>
          <MockStars>★★★★★</MockStars>
          <MockText>"Outstanding quality and service. Highly recommend to everyone!"</MockText>
          <div style={{ textAlign: 'center', marginTop: '8px', color: '#666', fontSize: '10px' }}>
            ● ○ ○
          </div>
        </MockReview>
      );
    }

    if (config.template === 'floating-badge') {
      return (
        <div style={{ position: 'relative', width: '100%', height: '60px', background: '#f0f0f0', borderRadius: '4px' }}>
          <MockReview style={{ position: 'absolute', bottom: '8px', right: '8px', minWidth: '100px' }}>
            <MockStars>★★★★★</MockStars>
            <MockText style={{ fontSize: '10px' }}>4.8/5 (127)</MockText>
          </MockReview>
        </div>
      );
    }

    return null;
  };

  return (
    <Container>
      <Section>
        <SectionTitle>Header Configuration</SectionTitle>

        <FormGroup>
          <CheckboxContainer>
            <Checkbox
              type="checkbox"
              id="showHeader"
              checked={headerSettings.showHeader}
              onChange={(e) => handleSettingChange('showHeader', e.target.checked)}
            />
            <Label htmlFor="showHeader">Show header text</Label>
          </CheckboxContainer>
          <HelpText>Display a header above your reviews widget</HelpText>
        </FormGroup>

        {headerSettings.showHeader && (
          <>
            <FormGroup>
              <Label htmlFor="headerText">Header Text</Label>
              <Input
                id="headerText"
                type="text"
                value={headerSettings.headerText}
                onChange={(e) => handleSettingChange('headerText', e.target.value)}
                placeholder="Enter header text..."
              />
              <HelpText>The main title displayed above your reviews</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="headerSize">Header Size</Label>
              <Select
                id="headerSize"
                value={headerSettings.headerSize}
                onChange={(e) => handleSettingChange('headerSize', e.target.value)}
              >
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="headerColor">Header Color</Label>
              <ColorInput
                id="headerColor"
                type="color"
                value={headerSettings.headerColor}
                onChange={(e) => handleSettingChange('headerColor', e.target.value)}
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="headerAlign">Header Alignment</Label>
              <Select
                id="headerAlign"
                value={headerSettings.headerAlign}
                onChange={(e) => handleSettingChange('headerAlign', e.target.value)}
              >
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
              </Select>
            </FormGroup>
          </>
        )}
      </Section>

      <Section>
        <SectionTitle>Subheader Configuration</SectionTitle>

        <FormGroup>
          <CheckboxContainer>
            <Checkbox
              type="checkbox"
              id="showSubheader"
              checked={headerSettings.showSubheader}
              onChange={(e) => handleSettingChange('showSubheader', e.target.checked)}
            />
            <Label htmlFor="showSubheader">Show subheader text</Label>
          </CheckboxContainer>
          <HelpText>Display descriptive text below the main header</HelpText>
        </FormGroup>

        {headerSettings.showSubheader && (
          <>
            <FormGroup>
              <Label htmlFor="subheaderText">Subheader Text</Label>
              <TextArea
                id="subheaderText"
                value={headerSettings.subheaderText}
                onChange={(e) => handleSettingChange('subheaderText', e.target.value)}
                placeholder="Enter subheader text..."
              />
              <HelpText>Additional descriptive text (supports multiple lines)</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="subheaderColor">Subheader Color</Label>
              <ColorInput
                id="subheaderColor"
                type="color"
                value={headerSettings.subheaderColor}
                onChange={(e) => handleSettingChange('subheaderColor', e.target.value)}
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="subheaderAlign">Subheader Alignment</Label>
              <Select
                id="subheaderAlign"
                value={headerSettings.subheaderAlign}
                onChange={(e) => handleSettingChange('subheaderAlign', e.target.value)}
              >
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
              </Select>
            </FormGroup>
          </>
        )}
      </Section>

      <Section>
        <SectionTitle>Preview</SectionTitle>
        <PreviewSection>
          <PreviewHeader
            $showHeader={headerSettings.showHeader}
            $headerColor={headerSettings.headerColor}
            $headerSize={headerSettings.headerSize}
            $headerAlign={headerSettings.headerAlign}
          >
            {headerSettings.headerText}
          </PreviewHeader>
          <PreviewSubheader
            $showSubheader={headerSettings.showSubheader}
            $subheaderColor={headerSettings.subheaderColor}
            $subheaderAlign={headerSettings.subheaderAlign}
          >
            {headerSettings.subheaderText}
          </PreviewSubheader>
          <PreviewWidget>
            <TemplatePreview $template={config.template}>
              {renderReviews()}
            </TemplatePreview>
            <TemplateLabel>
              {config.template.charAt(0).toUpperCase() + config.template.slice(1).replace('-', ' ')} Template
            </TemplateLabel>
          </PreviewWidget>
        </PreviewSection>
      </Section>
    </Container>
  );
}
