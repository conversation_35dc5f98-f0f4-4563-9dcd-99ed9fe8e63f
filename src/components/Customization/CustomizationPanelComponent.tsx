import React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import styled from 'styled-components';
import { z } from 'zod';
import { useWidget } from '../../context/WidgetContext';
import { WidgetStyling, WidgetSettings } from '../../types/widget';

// Validation schema
const customizationSchema = z.object({
  styling: z.object({
    colors: z.object({
      primary: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
      secondary: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
      background: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
      text: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
      border: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
    }),
    fonts: z.object({
      family: z.string().min(1, 'Font family is required'),
      size: z.string().regex(/^\d+(px|rem|em)$/, 'Invalid font size format'),
      weight: z.enum(['300', '400', '500', '600', '700']),
    }),
    dimensions: z.object({
      width: z.string().regex(/^\d+(px|%|rem|em)$/, 'Invalid width format'),
      height: z.string().regex(/^\d+(px|%|rem|em)$/, 'Invalid height format'),
      borderRadius: z.string().regex(/^\d+(px|rem|em)$/, 'Invalid border radius format'),
    }),
    spacing: z.object({
      padding: z.string().regex(/^\d+(px|rem|em)$/, 'Invalid padding format'),
      margin: z.string().regex(/^\d+(px|rem|em)$/, 'Invalid margin format'),
      gap: z.string().regex(/^\d+(px|rem|em)$/, 'Invalid gap format'),
    }),
  }),
  settings: z.object({
    maxReviews: z.number().min(1, 'Must show at least 1 review').max(20, 'Cannot show more than 20 reviews'),
    minRating: z.number().min(1, 'Minimum rating must be at least 1').max(5, 'Maximum rating cannot exceed 5'),
    sortBy: z.enum(['newest', 'oldest', 'rating_high', 'rating_low']),
    showPhotos: z.boolean(),
    showDates: z.boolean(),
    autoRefresh: z.boolean(),
  }),
});

type CustomizationFormData = z.infer<typeof customizationSchema>;

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
`;

const Section = styled.div`
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #555555;
`;

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }

  &:invalid {
    border-color: #ea4335;
  }
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }
`;

const ColorInput = styled.input`
  width: 50px;
  height: 35px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  padding: 0;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  &::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  cursor: pointer;
`;

const ErrorMessage = styled.span`
  font-size: 12px;
  color: #ea4335;
  margin-top: 4px;
`;


export function CustomizationPanelComponent() {
  const { config, updateConfig } = useWidget();

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<CustomizationFormData>({
    resolver: zodResolver(customizationSchema),
    defaultValues: {
      styling: {
        ...config.styling,
        fonts: {
          ...config.styling.fonts,
          weight: config.styling.fonts.weight as '300' | '400' | '500' | '600' | '700',
        },
      },
      settings: config.settings,
    },
    mode: 'onChange',
  });

  // Watch all form values for real-time updates
  const watchedValues = watch();

  // Update widget config in real-time as form values change
  React.useEffect(() => {
    if (watchedValues.styling && watchedValues.settings) {
      // Use a timeout to debounce updates and prevent infinite loops
      const timeoutId = setTimeout(() => {
        updateConfig({
          styling: watchedValues.styling,
          settings: watchedValues.settings,
        });
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [watchedValues.styling?.colors?.primary, watchedValues.styling?.colors?.secondary, watchedValues.styling?.colors?.background, watchedValues.styling?.colors?.text, watchedValues.styling?.colors?.border, watchedValues.styling?.fonts?.family, watchedValues.styling?.fonts?.size, watchedValues.styling?.fonts?.weight, watchedValues.styling?.dimensions?.width, watchedValues.styling?.dimensions?.height, watchedValues.styling?.dimensions?.borderRadius, watchedValues.styling?.spacing?.padding, watchedValues.styling?.spacing?.margin, watchedValues.styling?.spacing?.gap, watchedValues.settings?.maxReviews, watchedValues.settings?.minRating, watchedValues.settings?.sortBy, watchedValues.settings?.showPhotos, watchedValues.settings?.showDates, watchedValues.settings?.autoRefresh, updateConfig]);

  const onSubmit = (data: CustomizationFormData) => {
    updateConfig({
      styling: data.styling,
      settings: data.settings,
    });
  };

  return (
    <Container>
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Colors Section */}
        <Section>
          <SectionTitle>Colors</SectionTitle>
          <FormGrid>
            <FormField>
              <Label htmlFor="primary-color">Primary Color</Label>
              <Controller
                name="styling.colors.primary"
                control={control}
                render={({ field }) => (
                  <ColorInput
                    {...field}
                    type="color"
                    id="primary-color"
                  />
                )}
              />
              {errors.styling?.colors?.primary && (
                <ErrorMessage>{errors.styling.colors.primary.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="secondary-color">Secondary Color</Label>
              <Controller
                name="styling.colors.secondary"
                control={control}
                render={({ field }) => (
                  <ColorInput
                    {...field}
                    type="color"
                    id="secondary-color"
                  />
                )}
              />
              {errors.styling?.colors?.secondary && (
                <ErrorMessage>{errors.styling.colors.secondary.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="background-color">Background Color</Label>
              <Controller
                name="styling.colors.background"
                control={control}
                render={({ field }) => (
                  <ColorInput
                    {...field}
                    type="color"
                    id="background-color"
                  />
                )}
              />
              {errors.styling?.colors?.background && (
                <ErrorMessage>{errors.styling.colors.background.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="text-color">Text Color</Label>
              <Controller
                name="styling.colors.text"
                control={control}
                render={({ field }) => (
                  <ColorInput
                    {...field}
                    type="color"
                    id="text-color"
                  />
                )}
              />
              {errors.styling?.colors?.text && (
                <ErrorMessage>{errors.styling.colors.text.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="border-color">Border Color</Label>
              <Controller
                name="styling.colors.border"
                control={control}
                render={({ field }) => (
                  <ColorInput
                    {...field}
                    type="color"
                    id="border-color"
                  />
                )}
              />
              {errors.styling?.colors?.border && (
                <ErrorMessage>{errors.styling.colors.border.message}</ErrorMessage>
              )}
            </FormField>
          </FormGrid>
        </Section>

        {/* Typography Section */}
        <Section>
          <SectionTitle>Typography</SectionTitle>
          <FormGrid>
            <FormField>
              <Label htmlFor="font-family">Font Family</Label>
              <Controller
                name="styling.fonts.family"
                control={control}
                render={({ field }) => (
                  <Select {...field} id="font-family">
                    <option value="Inter, sans-serif">Inter</option>
                    <option value="Arial, sans-serif">Arial</option>
                    <option value="Helvetica, sans-serif">Helvetica</option>
                    <option value="Georgia, serif">Georgia</option>
                    <option value="Times New Roman, serif">Times New Roman</option>
                    <option value="Roboto, sans-serif">Roboto</option>
                    <option value="Open Sans, sans-serif">Open Sans</option>
                  </Select>
                )}
              />
              {errors.styling?.fonts?.family && (
                <ErrorMessage>{errors.styling.fonts.family.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="font-size">Font Size</Label>
              <Controller
                name="styling.fonts.size"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="font-size"
                    placeholder="14px"
                  />
                )}
              />
              {errors.styling?.fonts?.size && (
                <ErrorMessage>{errors.styling.fonts.size.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="font-weight">Font Weight</Label>
              <Controller
                name="styling.fonts.weight"
                control={control}
                render={({ field }) => (
                  <Select {...field} id="font-weight">
                    <option value="300">Light (300)</option>
                    <option value="400">Regular (400)</option>
                    <option value="500">Medium (500)</option>
                    <option value="600">Semi Bold (600)</option>
                    <option value="700">Bold (700)</option>
                  </Select>
                )}
              />
              {errors.styling?.fonts?.weight && (
                <ErrorMessage>{errors.styling.fonts.weight.message}</ErrorMessage>
              )}
            </FormField>
          </FormGrid>
        </Section>

        {/* Dimensions Section */}
        <Section>
          <SectionTitle>Dimensions</SectionTitle>
          <FormGrid>
            <FormField>
              <Label htmlFor="width">Width</Label>
              <Controller
                name="styling.dimensions.width"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="width"
                    placeholder="400px"
                  />
                )}
              />
              {errors.styling?.dimensions?.width && (
                <ErrorMessage>{errors.styling.dimensions.width.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="height">Height</Label>
              <Controller
                name="styling.dimensions.height"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="height"
                    placeholder="300px"
                  />
                )}
              />
              {errors.styling?.dimensions?.height && (
                <ErrorMessage>{errors.styling.dimensions.height.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="border-radius">Border Radius</Label>
              <Controller
                name="styling.dimensions.borderRadius"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="border-radius"
                    placeholder="8px"
                  />
                )}
              />
              {errors.styling?.dimensions?.borderRadius && (
                <ErrorMessage>{errors.styling.dimensions.borderRadius.message}</ErrorMessage>
              )}
            </FormField>
          </FormGrid>
        </Section>

        {/* Spacing Section */}
        <Section>
          <SectionTitle>Spacing</SectionTitle>
          <FormGrid>
            <FormField>
              <Label htmlFor="padding">Padding</Label>
              <Controller
                name="styling.spacing.padding"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="padding"
                    placeholder="16px"
                  />
                )}
              />
              {errors.styling?.spacing?.padding && (
                <ErrorMessage>{errors.styling.spacing.padding.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="margin">Margin</Label>
              <Controller
                name="styling.spacing.margin"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="margin"
                    placeholder="0px"
                  />
                )}
              />
              {errors.styling?.spacing?.margin && (
                <ErrorMessage>{errors.styling.spacing.margin.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="gap">Gap</Label>
              <Controller
                name="styling.spacing.gap"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    id="gap"
                    placeholder="12px"
                  />
                )}
              />
              {errors.styling?.spacing?.gap && (
                <ErrorMessage>{errors.styling.spacing.gap.message}</ErrorMessage>
              )}
            </FormField>
          </FormGrid>
        </Section>

        {/* Widget Settings Section */}
        <Section>
          <SectionTitle>Widget Settings</SectionTitle>
          <FormGrid>
            <FormField>
              <Label htmlFor="max-reviews">Maximum Reviews</Label>
              <Controller
                name="settings.maxReviews"
                control={control}
                render={({ field: { onChange, value, ...field } }) => (
                  <Input
                    {...field}
                    type="number"
                    id="max-reviews"
                    min="1"
                    max="20"
                    value={value}
                    onChange={(e) => onChange(parseInt(e.target.value, 10))}
                  />
                )}
              />
              {errors.settings?.maxReviews && (
                <ErrorMessage>{errors.settings.maxReviews.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="min-rating">Minimum Rating</Label>
              <Controller
                name="settings.minRating"
                control={control}
                render={({ field: { onChange, value, ...field } }) => (
                  <Select
                    {...field}
                    id="min-rating"
                    value={value}
                    onChange={(e) => onChange(parseInt(e.target.value, 10))}
                  >
                    <option value={1}>1 Star</option>
                    <option value={2}>2 Stars</option>
                    <option value={3}>3 Stars</option>
                    <option value={4}>4 Stars</option>
                    <option value={5}>5 Stars</option>
                  </Select>
                )}
              />
              {errors.settings?.minRating && (
                <ErrorMessage>{errors.settings.minRating.message}</ErrorMessage>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="sort-by">Sort Reviews By</Label>
              <Controller
                name="settings.sortBy"
                control={control}
                render={({ field }) => (
                  <Select {...field} id="sort-by">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="rating_high">Highest Rating</option>
                    <option value="rating_low">Lowest Rating</option>
                  </Select>
                )}
              />
              {errors.settings?.sortBy && (
                <ErrorMessage>{errors.settings.sortBy.message}</ErrorMessage>
              )}
            </FormField>
          </FormGrid>

          <FormGrid style={{ marginTop: '16px' }}>
            <FormField>
              <CheckboxContainer>
                <Controller
                  name="settings.showPhotos"
                  control={control}
                  render={({ field: { onChange, value, ...field } }) => (
                    <Checkbox
                      {...field}
                      type="checkbox"
                      id="show-photos"
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                    />
                  )}
                />
                <Label htmlFor="show-photos">Show Review Photos</Label>
              </CheckboxContainer>
            </FormField>

            <FormField>
              <CheckboxContainer>
                <Controller
                  name="settings.showDates"
                  control={control}
                  render={({ field: { onChange, value, ...field } }) => (
                    <Checkbox
                      {...field}
                      type="checkbox"
                      id="show-dates"
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                    />
                  )}
                />
                <Label htmlFor="show-dates">Show Review Dates</Label>
              </CheckboxContainer>
            </FormField>

            <FormField>
              <CheckboxContainer>
                <Controller
                  name="settings.autoRefresh"
                  control={control}
                  render={({ field: { onChange, value, ...field } }) => (
                    <Checkbox
                      {...field}
                      type="checkbox"
                      id="auto-refresh"
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                    />
                  )}
                />
                <Label htmlFor="auto-refresh">Auto-refresh Reviews</Label>
              </CheckboxContainer>
            </FormField>
          </FormGrid>
        </Section>
      </form>
    </Container>
  );
}
