import React, { Component, ErrorInfo, ReactNode } from 'react';
import styled from 'styled-components';

interface Props {
  children: ReactNode;
  widgetStep?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

const WidgetErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 24px;
  text-align: center;
  background-color: #fef7f0;
  border: 1px solid #fed7aa;
  border-radius: 8px;
  margin: 16px 0;
`;

const ErrorIcon = styled.div`
  font-size: 32px;
  color: #ea580c;
  margin-bottom: 12px;
`;

const ErrorTitle = styled.h3`
  color: #ea580c;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
`;

const ErrorMessage = styled.p`
  color: #9a3412;
  font-size: 14px;
  margin: 0 0 16px 0;
  max-width: 400px;
  line-height: 1.4;
`;

const RetryButton = styled.button`
  padding: 8px 16px;
  background-color: #ea580c;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #dc2626;
  }

  &:disabled {
    background-color: #d1d5db;
    cursor: not-allowed;
  }
`;

class WidgetErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ error });

    // Log widget-specific error context
    const widgetContext = {
      step: this.props.widgetStep,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    };

    console.error('Widget error in step:', this.props.widgetStep, widgetContext);

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
    });

    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  private getErrorMessage(): string {
    const step = this.props.widgetStep;
    const error = this.state.error;

    if (step === 'business-search') {
      return 'Unable to search for businesses. Please check your internet connection and try again.';
    }
    
    if (step === 'template-selection') {
      return 'Failed to load widget templates. Please refresh the page and try again.';
    }
    
    if (step === 'customization') {
      return 'Error loading customization options. Your settings may not be saved properly.';
    }
    
    if (step === 'preview') {
      return 'Unable to generate widget preview. Please check your configuration and try again.';
    }
    
    if (step === 'embed-code') {
      return 'Failed to generate embed code. Please verify your widget configuration.';
    }

    // Generic message with error details if available
    if (error?.message) {
      return `An error occurred: ${error.message}`;
    }

    return 'An unexpected error occurred while creating your widget.';
  }

  render() {
    if (this.state.hasError) {
      return (
        <WidgetErrorContainer>
          <ErrorIcon>⚠️</ErrorIcon>
          <ErrorTitle>Widget Error</ErrorTitle>
          <ErrorMessage>{this.getErrorMessage()}</ErrorMessage>
          <RetryButton onClick={this.handleRetry}>
            Try Again
          </RetryButton>
        </WidgetErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default WidgetErrorBoundary;