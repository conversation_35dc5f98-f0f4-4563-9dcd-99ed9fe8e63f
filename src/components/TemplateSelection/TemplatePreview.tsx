import React from 'react';
import styled from 'styled-components';
import { TemplateType } from '../../types/widget';

const PreviewWrapper = styled.div`
  width: 100%;
  height: 100%;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666666;
`;

// Mock review data for previews
const mockReviews = [
  {
    id: '1',
    authorName: '<PERSON>',
    rating: 5,
    text: 'Excellent service and friendly staff. Highly recommend!',
    publishedDate: '2 days ago'
  },
  {
    id: '2',
    authorName: '<PERSON>',
    rating: 4,
    text: 'Great experience overall. Will definitely come back.',
    publishedDate: '1 week ago'
  },
  {
    id: '3',
    authorName: '<PERSON>',
    rating: 5,
    text: 'Outstanding quality and attention to detail.',
    publishedDate: '2 weeks ago'
  }
];

// Carousel Preview
const CarouselPreview = styled.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const CarouselHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const CarouselNav = styled.div`
  display: flex;
  gap: 4px;
`;

const NavDot = styled.div<{ $active?: boolean }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${props => props.$active ? '#4285f4' : '#e0e0e0'};
`;

const ReviewCard = styled.div`
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
`;

const ReviewHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
`;

const AuthorName = styled.span`
  font-weight: 500;
  font-size: 11px;
`;

const Rating = styled.div`
  color: #ffa500;
  font-size: 10px;
`;

const ReviewText = styled.p`
  margin: 0;
  font-size: 10px;
  line-height: 1.3;
  color: #666;
`;

// Badge Preview
const BadgePreview = styled.div`
  background: white;
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 11px;
`;

const BadgeRating = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  color: #ffa500;
  font-weight: 600;
`;

const BadgeText = styled.span`
  color: #666;
  font-size: 10px;
`;

// Grid Preview
const GridPreview = styled.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const GridHeader = styled.div`
  text-align: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
`;

const GridSummary = styled.div`
  background: #f8f9fa;
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 9px;
  color: #666;
  text-align: center;
`;

const GridReviews = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
`;

const GridReviewCard = styled.div`
  padding: 4px;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  font-size: 9px;
`;

// Simple Carousel Preview
const SimpleCarouselPreview = styled.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const SimpleCarouselNav = styled.div`
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
`;

// Slider Preview
const SliderPreview = styled.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const SliderTrack = styled.div`
  display: flex;
  gap: 6px;
  overflow: hidden;
`;

const SliderCard = styled.div`
  min-width: 60px;
  padding: 6px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  font-size: 9px;
`;

// Floating Badge Preview
const FloatingBadgePreview = styled.div`
  position: relative;
  width: 100%;
  height: 100px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 6px;
  overflow: hidden;
`;

const FloatingBadgeElement = styled.div`
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 8px;
`;

interface TemplatePreviewProps {
  templateType: TemplateType;
  isSelected: boolean;
}

export function TemplatePreview({ templateType, isSelected }: TemplatePreviewProps) {
  const renderPreview = () => {
    switch (templateType) {
      case 'carousel':
        return (
          <CarouselPreview>
            <CarouselHeader>
              <span style={{ fontSize: '11px', fontWeight: '600' }}>Customer Reviews</span>
              <CarouselNav>
                <NavDot $active />
                <NavDot />
                <NavDot />
              </CarouselNav>
            </CarouselHeader>
            <ReviewCard>
              <ReviewHeader>
                <AuthorName>{mockReviews[0].authorName}</AuthorName>
                <Rating>★★★★★</Rating>
              </ReviewHeader>
              <ReviewText>{mockReviews[0].text}</ReviewText>
            </ReviewCard>
          </CarouselPreview>
        );

      case 'badge':
        return (
          <BadgePreview>
            <BadgeRating>
              <span>★</span>
              <span>4.8</span>
            </BadgeRating>
            <BadgeText>Based on 127 reviews</BadgeText>
          </BadgePreview>
        );

      case 'grid':
        return (
          <GridPreview>
            <GridHeader>
              <div style={{ fontSize: '11px', fontWeight: '600' }}>★ 4.8 (127 reviews)</div>
            </GridHeader>
            <GridSummary>
              AI Summary: Customers love the excellent service and friendly staff
            </GridSummary>
            <GridReviews>
              {mockReviews.slice(0, 2).map((review, index) => (
                <GridReviewCard key={index}>
                  <div style={{ fontWeight: '500', marginBottom: '2px' }}>{review.authorName}</div>
                  <div style={{ color: '#ffa500', fontSize: '8px' }}>★★★★★</div>
                  <div style={{ marginTop: '2px' }}>{review.text.substring(0, 30)}...</div>
                </GridReviewCard>
              ))}
            </GridReviews>
          </GridPreview>
        );

      case 'simple-carousel':
        return (
          <SimpleCarouselPreview>
            <div style={{ fontSize: '11px', fontWeight: '600', marginBottom: '6px' }}>
              {mockReviews[0].authorName}
            </div>
            <div style={{ color: '#ffa500', fontSize: '10px', marginBottom: '4px' }}>★★★★★</div>
            <div style={{ fontSize: '9px', color: '#666' }}>
              "{mockReviews[0].text}"
            </div>
            <SimpleCarouselNav>
              <NavDot $active />
              <NavDot />
              <NavDot />
            </SimpleCarouselNav>
          </SimpleCarouselPreview>
        );

      case 'slider':
        return (
          <SliderPreview>
            <div style={{ fontSize: '11px', fontWeight: '600', marginBottom: '8px', textAlign: 'center' }}>
              Recent Reviews
            </div>
            <SliderTrack>
              {mockReviews.map((review, index) => (
                <SliderCard key={index}>
                  <div style={{ fontWeight: '500', marginBottom: '2px' }}>{review.authorName}</div>
                  <div style={{ color: '#ffa500', fontSize: '8px' }}>★★★★★</div>
                </SliderCard>
              ))}
            </SliderTrack>
          </SliderPreview>
        );

      case 'floating-badge':
        return (
          <FloatingBadgePreview>
            <div style={{ 
              position: 'absolute', 
              top: '8px', 
              left: '8px', 
              fontSize: '9px', 
              color: '#666',
              background: 'rgba(255,255,255,0.8)',
              padding: '2px 4px',
              borderRadius: '3px'
            }}>
              Your Website
            </div>
            <FloatingBadgeElement>
              <div style={{ color: '#ffa500', fontWeight: '600' }}>★ 4.8</div>
              <div style={{ color: '#666' }}>127</div>
            </FloatingBadgeElement>
          </FloatingBadgePreview>
        );

      default:
        return <div>Preview not available</div>;
    }
  };

  return (
    <PreviewWrapper>
      {renderPreview()}
    </PreviewWrapper>
  );
}