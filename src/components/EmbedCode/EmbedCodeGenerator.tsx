import React, { useState, useCallback } from 'react';
import { WidgetConfig } from '../../types/widget';
import SyntaxHighlighter from './SyntaxHighlighter';

interface EmbedCodeGeneratorProps {
  widgetConfig: WidgetConfig;
  onEmbedCodeGenerated?: (embedCode: string) => void;
  className?: string;
}

interface EmbedCodeResult {
  embedCode: string;
  widgetId: string;
  embedUrl: string;
  previewUrl: string;
}

export const EmbedCodeGenerator: React.FC<EmbedCodeGeneratorProps> = ({
  widgetConfig,
  onEmbedCodeGenerated,
  className = '',
}) => {
  const [embedResult, setEmbedResult] = useState<EmbedCodeResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [embedType, setEmbedType] = useState<'standard' | 'iframe'>('standard');

  const generateEmbedCode = useCallback(async () => {
    setIsGenerating(true);
    setError(null);

    try {
      // Generate a unique widget ID for this configuration
      const widgetId = `grw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Save configuration to backend (in a real implementation, this would save to a database)
      // For now, we'll simulate this by saving to localStorage with the widget ID
      localStorage.setItem(`widget-config-${widgetId}`, JSON.stringify(widgetConfig));

      let embedCode: string;
      let embedUrl: string;
      let previewUrl: string;

      if (embedType === 'iframe') {
        // Generate iframe embed code
        embedUrl = `${window.location.origin}/widget/${widgetId}`;
        previewUrl = embedUrl;

        embedCode = `<!-- Google Reviews Widget - iFrame Embed -->
<iframe 
  src="${embedUrl}" 
  width="${widgetConfig.styling.dimensions.width}" 
  height="${widgetConfig.styling.dimensions.height}"
  frameborder="0" 
  scrolling="no"
  style="border: none; border-radius: ${widgetConfig.styling.dimensions.borderRadius};">
</iframe>`;
      } else {
        // Generate compact standard embed code (similar to Elfsight)
        embedUrl = `${window.location.origin}/widget-platform.js`;
        previewUrl = `${window.location.origin}/widget/${widgetId}`;

        embedCode = `<!-- Google Reviews Widget -->
<script src="${embedUrl}" async></script>
<div class="grw-widget-${widgetId}" data-grw-widget-lazy></div>`;
      }

      const result: EmbedCodeResult = {
        embedCode,
        widgetId,
        embedUrl,
        previewUrl,
      };

      setEmbedResult(result);

      if (onEmbedCodeGenerated) {
        onEmbedCodeGenerated(result.embedCode);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  }, [widgetConfig, embedType, onEmbedCodeGenerated]);

  const copyToClipboard = useCallback(async () => {
    if (!embedResult?.embedCode) return;

    try {
      await navigator.clipboard.writeText(embedResult.embedCode);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = embedResult.embedCode;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch (fallbackErr) {
        setError('Failed to copy to clipboard');
      }

      document.body.removeChild(textArea);
    }
  }, [embedResult?.embedCode]);

  const downloadEmbedCode = useCallback(() => {
    if (!embedResult?.embedCode) return;

    const blob = new Blob([embedResult.embedCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `widget-${embedResult.widgetId}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [embedResult]);

  return (
    <div className={`embed-code-generator ${className}`}>
      <div className="embed-code-header">
        <h3>Generate Embed Code</h3>
        <p>Generate the HTML code to embed your widget on any website.</p>
      </div>

      <div className="embed-type-selector">
        <label className="embed-type-option">
          <input
            type="radio"
            name="embedType"
            value="standard"
            checked={embedType === 'standard'}
            onChange={(e) => setEmbedType(e.target.value as 'standard')}
          />
          <span>Standard Embed (Recommended)</span>
          <small>Loads faster and provides better customization</small>
        </label>

        <label className="embed-type-option">
          <input
            type="radio"
            name="embedType"
            value="iframe"
            checked={embedType === 'iframe'}
            onChange={(e) => setEmbedType(e.target.value as 'iframe')}
          />
          <span>iFrame Embed</span>
          <small>Better isolation but may load slower</small>
        </label>
      </div>

      <div className="embed-actions">
        <button
          onClick={generateEmbedCode}
          disabled={isGenerating}
          className="generate-button primary"
        >
          {isGenerating ? 'Generating...' : 'Generate Embed Code'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          {error}
        </div>
      )}

      {embedResult && (
        <div className="embed-result">
          <div className="embed-code-section">
            <div className="section-header">
              <h4>Your Embed Code</h4>
              <div className="embed-actions-inline">
                <button
                  onClick={copyToClipboard}
                  className={`copy-button ${copySuccess ? 'success' : ''}`}
                  title="Copy to clipboard"
                >
                  {copySuccess ? '✓ Copied!' : '📋 Copy'}
                </button>
                <button
                  onClick={downloadEmbedCode}
                  className="download-button"
                  title="Download as HTML file"
                >
                  💾 Download
                </button>
              </div>
            </div>

            <div className="embed-code-container">
              <SyntaxHighlighter
                code={embedResult.embedCode}
                language="html"
                className="embed-code-display"
              />
            </div>
          </div>

          <div className="embed-info">
            <div className="info-item">
              <label>Widget ID:</label>
              <code>{embedResult.widgetId}</code>
            </div>
            <div className="info-item">
              <label>Preview URL:</label>
              <a
                href={embedResult.previewUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="preview-link"
              >
                {embedResult.previewUrl}
              </a>
            </div>
          </div>

          <div className="embed-instructions">
            <h4>How to use this code:</h4>
            <ol>
              <li>Copy the embed code above</li>
              <li>Paste it into your website's HTML where you want the widget to appear</li>
              <li>The widget will automatically load and display your Google reviews</li>
            </ol>
          </div>
        </div>
      )}

      <style>{`
        .embed-code-generator {
          max-width: 800px;
          margin: 0 auto;
          padding: 24px;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .embed-code-header {
          margin-bottom: 24px;
        }

        .embed-code-header h3 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #333;
        }

        .embed-code-header p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }

        .embed-type-selector {
          margin-bottom: 24px;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 6px;
        }

        .embed-type-option {
          display: block;
          margin-bottom: 12px;
          cursor: pointer;
        }

        .embed-type-option:last-child {
          margin-bottom: 0;
        }

        .embed-type-option input {
          margin-right: 8px;
        }

        .embed-type-option span {
          font-weight: 500;
          color: #333;
        }

        .embed-type-option small {
          display: block;
          margin-left: 20px;
          color: #666;
          font-size: 12px;
        }

        .embed-actions {
          margin-bottom: 24px;
        }

        .generate-button {
          background: #4285f4;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .generate-button:hover:not(:disabled) {
          background: #3367d6;
        }

        .generate-button:disabled {
          background: #ccc;
          cursor: not-allowed;
        }

        .error-message {
          padding: 12px;
          background: #fee;
          border: 1px solid #fcc;
          border-radius: 6px;
          color: #c33;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .embed-result {
          border-top: 1px solid #eee;
          padding-top: 24px;
        }

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }

        .section-header h4 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        .embed-actions-inline {
          display: flex;
          gap: 8px;
        }

        .copy-button, .download-button {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .copy-button:hover, .download-button:hover {
          background: #e9ecef;
        }

        .copy-button.success {
          background: #d4edda;
          border-color: #c3e6cb;
          color: #155724;
        }

        .embed-code-container {
          margin-bottom: 24px;
        }

        .embed-info {
          margin-bottom: 24px;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 6px;
        }

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          gap: 12px;
        }

        .info-item:last-child {
          margin-bottom: 0;
        }

        .info-item label {
          font-weight: 500;
          color: #333;
          min-width: 100px;
        }

        .info-item code {
          background: #e9ecef;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: monospace;
          font-size: 12px;
        }

        .preview-link {
          color: #4285f4;
          text-decoration: none;
          font-size: 14px;
        }

        .preview-link:hover {
          text-decoration: underline;
        }

        .embed-instructions {
          padding: 16px;
          background: #e8f4fd;
          border-radius: 6px;
          border-left: 4px solid #4285f4;
        }

        .embed-instructions h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .embed-instructions ol {
          margin: 0;
          padding-left: 20px;
        }

        .embed-instructions li {
          margin-bottom: 4px;
          color: #555;
        }

        .embed-instructions li:last-child {
          margin-bottom: 0;
        }
      `}</style>
    </div>
  );
};

export default EmbedCodeGenerator;
