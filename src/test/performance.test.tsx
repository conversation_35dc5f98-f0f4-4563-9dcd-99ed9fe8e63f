// Performance tests for frontend application
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { performanceMonitor } from '../utils/performance-monitor';

// Mock components for testing
const MockComponent = ({ name }: { name: string }) => {
  const startTime = performance.now();
  
  // Simulate some work
  for (let i = 0; i < 1000; i++) {
    Math.random();
  }
  
  const endTime = performance.now();
  const renderTime = endTime - startTime;
  
  return <div data-testid={name} data-render-time={renderTime}>{name}</div>;
};

describe('Frontend Performance Tests', () => {
  beforeEach(() => {
    // Mock performance API
    global.performance = {
      ...global.performance,
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByType: vi.fn(() => []),
      getEntriesByName: vi.fn(() => [])
    } as any;
  });

  describe('Component Rendering Performance', () => {
    it('should render components within acceptable time limits', async () => {
      const startTime = performance.now();
      
      render(
        <BrowserRouter>
          <MockComponent name="test-component" />
        </BrowserRouter>
      );
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      expect(renderTime).toBeLessThan(100); // Should render in under 100ms
    });

    it('should handle lazy loading efficiently', async () => {
      // Mock React.lazy
      const LazyComponent = vi.fn(() => Promise.resolve({
        default: () => <div data-testid="lazy-component">Lazy Component</div>
      }));

      const startTime = performance.now();
      
      // Simulate lazy component loading
      const component = await LazyComponent();
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      expect(loadTime).toBeLessThan(50); // Should load quickly
      expect(LazyComponent).toHaveBeenCalled();
    });
  });

  describe('Bundle Analysis', () => {
    it('should track bundle sizes', async () => {
      const bundleAnalysis = await performanceMonitor.getBundleAnalysis();
      
      expect(bundleAnalysis).toHaveProperty('totalSize');
      expect(bundleAnalysis).toHaveProperty('jsSize');
      expect(bundleAnalysis).toHaveProperty('cssSize');
      
      // Frontend bundle should be reasonable size
      expect(bundleAnalysis.jsSize).toBeLessThan(500 * 1024); // Under 500KB
      expect(bundleAnalysis.cssSize).toBeLessThan(50 * 1024); // Under 50KB
    });

    it('should identify large dependencies', async () => {
      const mockResources = [
        { name: 'vendor.js', size: 200000, type: 'js' },
        { name: 'main.js', size: 100000, type: 'js' },
        { name: 'styles.css', size: 30000, type: 'css' }
      ];

      // Find largest resources
      const largestJs = mockResources
        .filter(r => r.type === 'js')
        .sort((a, b) => b.size - a.size)[0];

      expect(largestJs.size).toBeLessThan(300 * 1024); // Largest JS chunk under 300KB
    });
  });

  describe('Memory Usage', () => {
    it('should monitor component memory usage', () => {
      const componentMetrics = performanceMonitor.getComponentMetrics();
      
      // Should track component performance
      expect(Array.isArray(componentMetrics)).toBe(true);
    });

    it('should detect memory leaks', async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Render and unmount components multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<MockComponent name={`component-${i}`} />);
        unmount();
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryGrowth = finalMemory - initialMemory;
      
      // Memory growth should be minimal
      expect(memoryGrowth).toBeLessThan(1024 * 1024); // Less than 1MB growth
    });
  });

  describe('Network Performance', () => {
    it('should cache API responses', async () => {
      const mockFetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: 'test' }),
        headers: new Map([['etag', 'test-etag']])
      });

      global.fetch = mockFetch;

      // First request
      await fetch('/api/test');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Subsequent requests should be cached (in real implementation)
    });

    it('should handle slow network gracefully', async () => {
      const slowFetch = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ data: 'slow response' })
        }), 2000))
      );

      global.fetch = slowFetch;

      const startTime = performance.now();
      
      try {
        await Promise.race([
          fetch('/api/slow'),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 1000))
        ]);
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        expect(duration).toBeLessThan(1100); // Should timeout within 1.1s
        expect((error as Error).message).toBe('Timeout');
      }
    });
  });

  describe('User Interaction Performance', () => {
    it('should respond to clicks quickly', async () => {
      let clickTime = 0;
      const handleClick = () => {
        clickTime = performance.now();
      };

      render(
        <button onClick={handleClick} data-testid="test-button">
          Click me
        </button>
      );

      const button = screen.getByTestId('test-button');
      const startTime = performance.now();
      
      button.click();
      
      await waitFor(() => {
        expect(clickTime).toBeGreaterThan(startTime);
      });

      const responseTime = clickTime - startTime;
      expect(responseTime).toBeLessThan(16); // Should respond within one frame (16ms)
    });

    it('should handle rapid interactions', async () => {
      let clickCount = 0;
      const handleClick = () => {
        clickCount++;
      };

      render(
        <button onClick={handleClick} data-testid="rapid-click-button">
          Rapid Click
        </button>
      );

      const button = screen.getByTestId('rapid-click-button');
      
      // Simulate rapid clicks
      const startTime = performance.now();
      for (let i = 0; i < 10; i++) {
        button.click();
      }
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      expect(totalTime).toBeLessThan(100); // Should handle 10 clicks in under 100ms
      expect(clickCount).toBe(10);
    });
  });

  describe('Code Splitting Performance', () => {
    it('should load route chunks efficiently', async () => {
      // Mock dynamic import
      const mockImport = vi.fn().mockResolvedValue({
        default: () => <div>Lazy Route</div>
      });

      const startTime = performance.now();
      const module = await mockImport();
      const endTime = performance.now();
      
      const loadTime = endTime - startTime;
      expect(loadTime).toBeLessThan(100); // Should load route chunk quickly
      expect(mockImport).toHaveBeenCalled();
    });

    it('should preload critical chunks', () => {
      // Create mock preload links
      const preloadLink = document.createElement('link');
      preloadLink.rel = 'preload';
      preloadLink.href = '/assets/vendor.js';
      preloadLink.as = 'script';
      document.head.appendChild(preloadLink);
      
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      
      // Should have preload links for critical resources
      expect(preloadLinks.length).toBeGreaterThan(0);
      
      // Clean up
      document.head.removeChild(preloadLink);
    });
  });

  describe('Accessibility Performance', () => {
    it('should maintain performance with screen readers', async () => {
      const startTime = performance.now();
      
      render(
        <div role="main" aria-label="Test content">
          <h1>Accessible Content</h1>
          <p>This content should be accessible and performant</p>
        </div>
      );
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Accessibility attributes shouldn't significantly impact performance
      expect(renderTime).toBeLessThan(50);
    });
  });
});

describe('Performance Monitoring Integration', () => {
  it('should track page load metrics', () => {
    const metrics = performanceMonitor.getMetrics();
    expect(Array.isArray(metrics)).toBe(true);
  });

  it('should measure async operations', async () => {
    const result = await performanceMonitor.measureAsync('test-operation', async () => {
      await new Promise(resolve => setTimeout(resolve, 50));
      return 'success';
    });

    expect(result).toBe('success');
  });

  it('should measure synchronous operations', () => {
    const result = performanceMonitor.measureSync('sync-operation', () => {
      return 'sync-success';
    });

    expect(result).toBe('sync-success');
  });
});