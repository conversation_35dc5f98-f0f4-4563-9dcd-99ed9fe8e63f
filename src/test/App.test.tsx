import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import App from '../App';

// Mock all styled-components
vi.mock('styled-components', () => {
  const styled = (tag: any) => (styles: any) => {
    return React.forwardRef((props: any, ref: any) => {
      return React.createElement(tag, { ...props, ref, 'data-styled': true });
    });
  };
  
  // Add properties for all HTML tags
  styled.div = styled('div');
  styled.nav = styled('nav');
  styled.header = styled('header');
  styled.main = styled('main');
  styled.h1 = styled('h1');
  styled.h2 = styled('h2');
  styled.p = styled('p');
  styled.button = styled('button');
  styled.form = styled('form');
  styled.input = styled('input');
  styled.label = styled('label');
  styled.span = styled('span');
  styled.ul = styled('ul');
  styled.li = styled('li');
  styled.img = styled('img');
  styled.section = styled('section');
  styled.h3 = styled('h3');
  
  return { default: styled };
});

describe('App', () => {
  it('renders without crashing', () => {
    render(<App />);
    expect(screen.getByText('Google Reviews Widget Generator')).toBeInTheDocument();
  });

  it('renders the source page by default', () => {
    render(<App />);
    expect(screen.getByText('Find Your Business')).toBeInTheDocument();
    expect(screen.getByText('Search for your business by name and address, or paste a Google Maps link to get started.')).toBeInTheDocument();
  });

  it('renders step navigation', () => {
    render(<App />);
    expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByText('Layout')).toBeInTheDocument();
    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('Reviews')).toBeInTheDocument();
    expect(screen.getByText('Style')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('renders header with reset and save buttons', () => {
    render(<App />);
    expect(screen.getByText('Reset')).toBeInTheDocument();
    expect(screen.getByText('Save Configuration')).toBeInTheDocument();
  });
});