import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { WidgetProvider, useWidget } from '../../context/WidgetContext';
import { vi } from 'vitest';

// Test component to interact with the context
function TestComponent() {
  const { config, updateConfig, resetConfig, currentStep, setCurrentStep } = useWidget();

  return (
    <div>
      <div data-testid="current-step">{currentStep}</div>
      <div data-testid="template">{config.template}</div>
      <div data-testid="max-reviews">{config.settings.maxReviews}</div>
      <div data-testid="primary-color">{config.styling.colors.primary}</div>
      
      <button onClick={() => setCurrentStep(2)}>Set Step 2</button>
      <button onClick={() => updateConfig({ template: 'grid' })}>Change Template</button>
      <button onClick={() => updateConfig({ 
        settings: { ...config.settings, maxReviews: 10 } 
      })}>Change Max Reviews</button>
      <button onClick={resetConfig}>Reset</button>
    </div>
  );
}

const renderWithProvider = () => {
  return render(
    <WidgetProvider>
      <TestComponent />
    </WidgetProvider>
  );
};

describe('WidgetContext', () => {
  it('provides default configuration values', () => {
    renderWithProvider();
    
    expect(screen.getByTestId('current-step')).toHaveTextContent('0');
    expect(screen.getByTestId('template')).toHaveTextContent('carousel');
    expect(screen.getByTestId('max-reviews')).toHaveTextContent('5');
    expect(screen.getByTestId('primary-color')).toHaveTextContent('#4285f4');
  });

  it('updates current step', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByText('Set Step 2'));
    expect(screen.getByTestId('current-step')).toHaveTextContent('2');
  });

  it('updates widget configuration', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByText('Change Template'));
    expect(screen.getByTestId('template')).toHaveTextContent('grid');
  });

  it('updates nested configuration properties', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByText('Change Max Reviews'));
    expect(screen.getByTestId('max-reviews')).toHaveTextContent('10');
  });

  it('resets configuration to defaults', () => {
    renderWithProvider();
    
    // Make some changes
    fireEvent.click(screen.getByText('Set Step 2'));
    fireEvent.click(screen.getByText('Change Template'));
    fireEvent.click(screen.getByText('Change Max Reviews'));
    
    // Verify changes
    expect(screen.getByTestId('current-step')).toHaveTextContent('2');
    expect(screen.getByTestId('template')).toHaveTextContent('grid');
    expect(screen.getByTestId('max-reviews')).toHaveTextContent('10');
    
    // Reset
    fireEvent.click(screen.getByText('Reset'));
    
    // Verify reset
    expect(screen.getByTestId('current-step')).toHaveTextContent('0');
    expect(screen.getByTestId('template')).toHaveTextContent('carousel');
    expect(screen.getByTestId('max-reviews')).toHaveTextContent('5');
  });

  it('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useWidget must be used within a WidgetProvider');
    
    consoleSpy.mockRestore();
  });
});