import { rest } from 'msw';

const API_BASE_URL = 'http://localhost:3001/api';

export const handlers = [
  // Business search endpoint
  rest.post(`${API_BASE_URL}/business/search`, (req, res, ctx) => {
    return res(
      ctx.json({
        businesses: [
          {
            id: '550e8400-e29b-41d4-a716-446655440000',
            placeId: 'ChIJ123456789',
            name: 'Test Business',
            address: '123 Test St, Test City, TC 12345',
            rating: 4.5,
            reviewCount: 100,
            photoUrl: 'https://example.com/photo.jpg',
          },
        ],
        totalResults: 1,
      })
    );
  }),

  // Reviews endpoint
  rest.get(`${API_BASE_URL}/reviews/:businessId`, (req, res, ctx) => {
    return res(
      ctx.json({
        reviews: [
          {
            id: 'review-1',
            authorName: '<PERSON>',
            authorPhotoUrl: 'https://example.com/john.jpg',
            rating: 5,
            text: 'Great service!',
            publishedDate: '2023-01-01T00:00:00.000Z',
            isVerified: true,
          },
          {
            id: 'review-2',
            authorName: '<PERSON> <PERSON>',
            rating: 4,
            text: 'Good experience overall.',
            publishedDate: '2023-01-02T00:00:00.000Z',
            isVerified: false,
          },
        ],
        businessInfo: {
          name: 'Test Business',
          rating: 4.5,
          reviewCount: 100,
        },
        totalReviews: 100,
        averageRating: 4.5,
        lastUpdated: '2023-01-01T00:00:00.000Z',
      })
    );
  }),

  // Widget configuration endpoints
  rest.post(`${API_BASE_URL}/widget/config`, (req, res, ctx) => {
    return res(
      ctx.json({
        id: 'widget-123',
        businessId: '550e8400-e29b-41d4-a716-446655440000',
        template: 'carousel',
        styling: {
          colors: {
            primary: '#007bff',
            secondary: '#6c757d',
            background: '#ffffff',
            text: '#212529',
          },
          fonts: {
            family: 'Arial, sans-serif',
            size: '14px',
          },
          dimensions: {
            width: '100%',
            height: 'auto',
          },
          spacing: {
            padding: '16px',
            margin: '8px',
          },
        },
        settings: {
          maxReviews: 5,
          minRating: 1,
          sortBy: 'newest',
          showPhotos: true,
          showDates: true,
          autoRefresh: false,
        },
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      })
    );
  }),

  rest.get(`${API_BASE_URL}/widget/config/:widgetId`, (req, res, ctx) => {
    return res(
      ctx.json({
        id: req.params.widgetId,
        businessId: '550e8400-e29b-41d4-a716-446655440000',
        template: 'carousel',
        styling: {
          colors: {
            primary: '#007bff',
            secondary: '#6c757d',
            background: '#ffffff',
            text: '#212529',
          },
          fonts: {
            family: 'Arial, sans-serif',
            size: '14px',
          },
          dimensions: {
            width: '100%',
            height: 'auto',
          },
          spacing: {
            padding: '16px',
            margin: '8px',
          },
        },
        settings: {
          maxReviews: 5,
          minRating: 1,
          sortBy: 'newest',
          showPhotos: true,
          showDates: true,
          autoRefresh: false,
        },
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      })
    );
  }),

  // Embed code generation
  rest.post(`${API_BASE_URL}/embed/generate`, (req, res, ctx) => {
    return res(
      ctx.json({
        embedCode: `<script src="https://cdn.example.com/widget.js" data-widget-id="widget-123"></script>`,
        widgetUrl: 'https://cdn.example.com/widget.js',
        previewUrl: 'https://example.com/preview/widget-123',
      })
    );
  }),

  // Widget data endpoint (for embedded widgets)
  rest.get(`${API_BASE_URL}/widget/:widgetId/data`, (req, res, ctx) => {
    return res(
      ctx.json({
        reviews: [
          {
            id: 'review-1',
            authorName: 'John Doe',
            authorPhotoUrl: 'https://example.com/john.jpg',
            rating: 5,
            text: 'Great service!',
            publishedDate: '2023-01-01T00:00:00.000Z',
            isVerified: true,
          },
        ],
        businessInfo: {
          name: 'Test Business',
          rating: 4.5,
          reviewCount: 100,
        },
        totalReviews: 100,
        averageRating: 4.5,
        lastUpdated: '2023-01-01T00:00:00.000Z',
      })
    );
  }),

  // Error logging endpoint
  rest.post(`${API_BASE_URL}/error-log`, (req, res, ctx) => {
    return res(ctx.status(200));
  }),
];