import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { CustomizationPanelComponent } from '../../components/Customization/CustomizationPanelComponent';
import { WidgetProvider } from '../../context/WidgetContext';

// Mock the useWidget hook for isolated testing
const mockUpdateConfig = vi.fn();
const mockConfig = {
  template: 'carousel' as const,
  styling: {
    colors: {
      primary: '#4285f4',
      secondary: '#34a853',
      background: '#ffffff',
      text: '#333333',
      border: '#e0e0e0',
    },
    fonts: {
      family: 'Inter, sans-serif',
      size: '14px',
      weight: '400' as const,
    },
    dimensions: {
      width: '400px',
      height: '300px',
      borderRadius: '8px',
    },
    spacing: {
      padding: '16px',
      margin: '0px',
      gap: '12px',
    },
  },
  settings: {
    maxReviews: 5,
    minRating: 1,
    sortBy: 'newest' as const,
    showPhotos: true,
    showDates: true,
    autoRefresh: true,
  },
};

vi.mock('../../context/WidgetContext', async () => {
  const actual = await vi.importActual('../../context/WidgetContext') as any;
  return {
    ...actual,
    useWidget: () => ({
      config: mockConfig,
      updateConfig: mockUpdateConfig,
      resetConfig: vi.fn(),
      currentStep: 0,
      setCurrentStep: vi.fn(),
    }),
  };
});

// Helper function to render component with provider
function renderWithProvider(component: React.ReactElement) {
  return render(
    <WidgetProvider>
      {component}
    </WidgetProvider>
  );
}

describe('CustomizationPanelComponent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders all customization sections', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByText('Colors')).toBeInTheDocument();
      expect(screen.getByText('Typography')).toBeInTheDocument();
      expect(screen.getByText('Dimensions')).toBeInTheDocument();
      expect(screen.getByText('Spacing')).toBeInTheDocument();
      expect(screen.getByText('Widget Settings')).toBeInTheDocument();
    });

    it('renders all color input fields', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByLabelText('Primary Color')).toBeInTheDocument();
      expect(screen.getByLabelText('Secondary Color')).toBeInTheDocument();
      expect(screen.getByLabelText('Background Color')).toBeInTheDocument();
      expect(screen.getByLabelText('Text Color')).toBeInTheDocument();
      expect(screen.getByLabelText('Border Color')).toBeInTheDocument();
    });

    it('renders typography controls', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByLabelText('Font Family')).toBeInTheDocument();
      expect(screen.getByLabelText('Font Size')).toBeInTheDocument();
      expect(screen.getByLabelText('Font Weight')).toBeInTheDocument();
    });

    it('renders dimension controls', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByLabelText('Width')).toBeInTheDocument();
      expect(screen.getByLabelText('Height')).toBeInTheDocument();
      expect(screen.getByLabelText('Border Radius')).toBeInTheDocument();
    });

    it('renders spacing controls', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByLabelText('Padding')).toBeInTheDocument();
      expect(screen.getByLabelText('Margin')).toBeInTheDocument();
      expect(screen.getByLabelText('Gap')).toBeInTheDocument();
    });

    it('renders widget settings controls', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByLabelText('Maximum Reviews')).toBeInTheDocument();
      expect(screen.getByLabelText('Minimum Rating')).toBeInTheDocument();
      expect(screen.getByLabelText('Sort Reviews By')).toBeInTheDocument();
      expect(screen.getByLabelText('Show Review Photos')).toBeInTheDocument();
      expect(screen.getByLabelText('Show Review Dates')).toBeInTheDocument();
      expect(screen.getByLabelText('Auto-refresh Reviews')).toBeInTheDocument();
    });

    it('displays real-time preview indicator', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      expect(screen.getByText('Preview updates in real-time')).toBeInTheDocument();
    });
  });

  describe('Form Initialization', () => {
    it('initializes form with current widget config values', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      const primaryColorInput = screen.getByLabelText('Primary Color') as HTMLInputElement;
      expect(primaryColorInput.value).toBe('#4285f4');

      const fontFamilySelect = screen.getByLabelText('Font Family') as HTMLSelectElement;
      expect(fontFamilySelect.value).toBe('Inter, sans-serif');

      const maxReviewsInput = screen.getByLabelText('Maximum Reviews') as HTMLInputElement;
      expect(maxReviewsInput.value).toBe('5');

      const showPhotosCheckbox = screen.getByLabelText('Show Review Photos') as HTMLInputElement;
      expect(showPhotosCheckbox.checked).toBe(true);
    });
  });

  describe('Color Controls', () => {
    it('updates primary color when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const primaryColorInput = screen.getByLabelText('Primary Color') as HTMLInputElement;
      fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } });

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              colors: expect.objectContaining({
                primary: '#ff0000',
              }),
            }),
          })
        );
      });
    });

    it('updates all color fields independently', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const secondaryColorInput = screen.getByLabelText('Secondary Color') as HTMLInputElement;
      fireEvent.change(secondaryColorInput, { target: { value: '#00ff00' } });

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              colors: expect.objectContaining({
                secondary: '#00ff00',
              }),
            }),
          })
        );
      });
    });
  });

  describe('Typography Controls', () => {
    it('updates font family when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const fontFamilySelect = screen.getByLabelText('Font Family');
      await user.selectOptions(fontFamilySelect, 'Arial, sans-serif');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              fonts: expect.objectContaining({
                family: 'Arial, sans-serif',
              }),
            }),
          })
        );
      });
    });

    it('updates font size when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const fontSizeInput = screen.getByLabelText('Font Size');
      await user.clear(fontSizeInput);
      await user.type(fontSizeInput, '16px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              fonts: expect.objectContaining({
                size: '16px',
              }),
            }),
          })
        );
      });
    });

    it('updates font weight when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const fontWeightSelect = screen.getByLabelText('Font Weight');
      await user.selectOptions(fontWeightSelect, '600');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              fonts: expect.objectContaining({
                weight: '600',
              }),
            }),
          })
        );
      });
    });
  });

  describe('Dimension Controls', () => {
    it('updates width when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const widthInput = screen.getByLabelText('Width');
      await user.clear(widthInput);
      await user.type(widthInput, '500px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              dimensions: expect.objectContaining({
                width: '500px',
              }),
            }),
          })
        );
      });
    });

    it('updates height when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const heightInput = screen.getByLabelText('Height');
      await user.clear(heightInput);
      await user.type(heightInput, '400px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              dimensions: expect.objectContaining({
                height: '400px',
              }),
            }),
          })
        );
      });
    });

    it('updates border radius when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const borderRadiusInput = screen.getByLabelText('Border Radius');
      await user.clear(borderRadiusInput);
      await user.type(borderRadiusInput, '12px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              dimensions: expect.objectContaining({
                borderRadius: '12px',
              }),
            }),
          })
        );
      });
    });
  });

  describe('Spacing Controls', () => {
    it('updates padding when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const paddingInput = screen.getByLabelText('Padding');
      await user.clear(paddingInput);
      await user.type(paddingInput, '20px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              spacing: expect.objectContaining({
                padding: '20px',
              }),
            }),
          })
        );
      });
    });

    it('updates margin when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const marginInput = screen.getByLabelText('Margin');
      await user.clear(marginInput);
      await user.type(marginInput, '10px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              spacing: expect.objectContaining({
                margin: '10px',
              }),
            }),
          })
        );
      });
    });

    it('updates gap when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const gapInput = screen.getByLabelText('Gap');
      await user.clear(gapInput);
      await user.type(gapInput, '16px');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            styling: expect.objectContaining({
              spacing: expect.objectContaining({
                gap: '16px',
              }),
            }),
          })
        );
      });
    });
  });

  describe('Widget Settings Controls', () => {
    it('updates maximum reviews when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const maxReviewsInput = screen.getByLabelText('Maximum Reviews');
      await user.clear(maxReviewsInput);
      await user.type(maxReviewsInput, '10');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            settings: expect.objectContaining({
              maxReviews: 10,
            }),
          })
        );
      });
    });

    it('updates minimum rating when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const minRatingSelect = screen.getByLabelText('Minimum Rating');
      await user.selectOptions(minRatingSelect, '4');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            settings: expect.objectContaining({
              minRating: 4,
            }),
          })
        );
      });
    });

    it('updates sort order when changed', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const sortBySelect = screen.getByLabelText('Sort Reviews By');
      await user.selectOptions(sortBySelect, 'rating_high');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            settings: expect.objectContaining({
              sortBy: 'rating_high',
            }),
          })
        );
      });
    });

    it('toggles show photos checkbox', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const showPhotosCheckbox = screen.getByLabelText('Show Review Photos');
      await user.click(showPhotosCheckbox);

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            settings: expect.objectContaining({
              showPhotos: false,
            }),
          })
        );
      });
    });

    it('toggles show dates checkbox', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const showDatesCheckbox = screen.getByLabelText('Show Review Dates');
      await user.click(showDatesCheckbox);

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            settings: expect.objectContaining({
              showDates: false,
            }),
          })
        );
      });
    });

    it('toggles auto refresh checkbox', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const autoRefreshCheckbox = screen.getByLabelText('Auto-refresh Reviews');
      await user.click(autoRefreshCheckbox);

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalledWith(
          expect.objectContaining({
            settings: expect.objectContaining({
              autoRefresh: false,
            }),
          })
        );
      });
    });
  });

  describe('Form Validation', () => {
    it('validates color inputs properly', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      // Color inputs automatically validate and convert values
      const primaryColorInput = screen.getByLabelText('Primary Color') as HTMLInputElement;
      expect(primaryColorInput.type).toBe('color');
      expect(primaryColorInput.value).toMatch(/^#[0-9a-f]{6}$/i);
    });

    it('shows error for invalid font size format', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const fontSizeInput = screen.getByLabelText('Font Size');
      await user.clear(fontSizeInput);
      await user.type(fontSizeInput, 'invalid-size');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Invalid font size format')).toBeInTheDocument();
      });
    });

    it('shows error for invalid width format', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const widthInput = screen.getByLabelText('Width');
      await user.clear(widthInput);
      await user.type(widthInput, 'invalid-width');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Invalid width format')).toBeInTheDocument();
      });
    });

    it('shows error for invalid padding format', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const paddingInput = screen.getByLabelText('Padding');
      await user.clear(paddingInput);
      await user.type(paddingInput, 'invalid-padding');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Invalid padding format')).toBeInTheDocument();
      });
    });

    it('shows error for maximum reviews out of range', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const maxReviewsInput = screen.getByLabelText('Maximum Reviews');
      await user.clear(maxReviewsInput);
      await user.type(maxReviewsInput, '25');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Cannot show more than 20 reviews')).toBeInTheDocument();
      });
    });

    it('shows error for minimum reviews below 1', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const maxReviewsInput = screen.getByLabelText('Maximum Reviews');
      await user.clear(maxReviewsInput);
      await user.type(maxReviewsInput, '0');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Must show at least 1 review')).toBeInTheDocument();
      });
    });

    it('accepts valid hex colors', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const primaryColorInput = screen.getByLabelText('Primary Color') as HTMLInputElement;
      fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } });
      fireEvent.blur(primaryColorInput);

      await waitFor(() => {
        expect(screen.queryByText('Invalid hex color')).not.toBeInTheDocument();
      });
    });

    it('accepts valid font size formats', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const fontSizeInput = screen.getByLabelText('Font Size');
      
      // Clear and set valid px format
      await user.clear(fontSizeInput);
      await user.type(fontSizeInput, '16px');
      
      // Wait for validation to clear any previous errors
      await waitFor(() => {
        const fontSizeValue = (fontSizeInput as HTMLInputElement).value;
        expect(fontSizeValue).toBe('16px');
      });

      // Test that no error is shown for valid format
      expect(screen.queryByText('Invalid font size format')).not.toBeInTheDocument();
    });

    it('accepts valid dimension formats', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const widthInput = screen.getByLabelText('Width');
      
      // Test percentage format
      await user.clear(widthInput);
      await user.type(widthInput, '100%');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText('Invalid width format')).not.toBeInTheDocument();
      });
    });
  });

  describe('Real-time Updates', () => {
    it('calls updateConfig when form values change', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      const primaryColorInput = screen.getByLabelText('Primary Color') as HTMLInputElement;
      fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } });

      // Should be called for real-time updates
      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalled();
      });
    });

    it('updates multiple fields independently', async () => {
      const user = userEvent.setup();
      renderWithProvider(<CustomizationPanelComponent />);

      // Change color
      const primaryColorInput = screen.getByLabelText('Primary Color') as HTMLInputElement;
      fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } });

      // Change font size
      const fontSizeInput = screen.getByLabelText('Font Size');
      await user.clear(fontSizeInput);
      await user.type(fontSizeInput, '18px');

      // Change max reviews
      const maxReviewsInput = screen.getByLabelText('Maximum Reviews');
      await user.clear(maxReviewsInput);
      await user.type(maxReviewsInput, '8');

      await waitFor(() => {
        expect(mockUpdateConfig).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper labels for all form controls', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      // Check that all inputs have associated labels
      const inputs = screen.getAllByRole('textbox');
      inputs.forEach(input => {
        expect(input).toHaveAccessibleName();
      });

      const selects = screen.getAllByRole('combobox');
      selects.forEach(select => {
        expect(select).toHaveAccessibleName();
      });

      const checkboxes = screen.getAllByRole('checkbox');
      checkboxes.forEach(checkbox => {
        expect(checkbox).toHaveAccessibleName();
      });
    });

    it('has proper form structure with fieldsets', () => {
      renderWithProvider(<CustomizationPanelComponent />);

      // Check that sections are properly structured
      expect(screen.getByText('Colors')).toBeInTheDocument();
      expect(screen.getByText('Typography')).toBeInTheDocument();
      expect(screen.getByText('Dimensions')).toBeInTheDocument();
      expect(screen.getByText('Spacing')).toBeInTheDocument();
      expect(screen.getByText('Widget Settings')).toBeInTheDocument();
    });
  });
});