import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import WidgetErrorBoundary from '../../components/ErrorBoundary/WidgetErrorBoundary';

// Mock console methods to avoid noise in tests
const originalConsoleError = console.error;

beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
});

// Component that throws an error for testing
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test widget error');
  }
  return <div>Widget content</div>;
};

describe('WidgetErrorBoundary', () => {
  it('should render children when there is no error', () => {
    render(
      <WidgetErrorBoundary>
        <div>Widget content</div>
      </WidgetErrorBoundary>
    );

    expect(screen.getByText('Widget content')).toBeInTheDocument();
  });

  it('should render widget error UI when child component throws', () => {
    render(
      <WidgetErrorBoundary>
        <ThrowError shouldThrow={true} />
      </WidgetErrorBoundary>
    );

    expect(screen.getByText('Widget Error')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('should display step-specific error messages', () => {
    const testCases = [
      {
        step: 'business-search',
        expectedMessage: 'Unable to search for businesses. Please check your internet connection and try again.'
      },
      {
        step: 'template-selection',
        expectedMessage: 'Failed to load widget templates. Please refresh the page and try again.'
      },
      {
        step: 'customization',
        expectedMessage: 'Error loading customization options. Your settings may not be saved properly.'
      },
      {
        step: 'preview',
        expectedMessage: 'Unable to generate widget preview. Please check your configuration and try again.'
      },
      {
        step: 'embed-code',
        expectedMessage: 'Failed to generate embed code. Please verify your widget configuration.'
      }
    ];

    testCases.forEach(({ step, expectedMessage }) => {
      const { unmount } = render(
        <WidgetErrorBoundary widgetStep={step}>
          <ThrowError shouldThrow={true} />
        </WidgetErrorBoundary>
      );

      expect(screen.getByText(expectedMessage)).toBeInTheDocument();
      unmount();
    });
  });

  it('should display generic error message for unknown steps', () => {
    // Create a component that throws without a specific message
    const GenericError = () => {
      const error = new Error();
      error.message = ''; // Empty message to trigger generic handling
      throw error;
    };

    render(
      <WidgetErrorBoundary widgetStep="unknown-step">
        <GenericError />
      </WidgetErrorBoundary>
    );

    expect(screen.getByText('An unexpected error occurred while creating your widget.')).toBeInTheDocument();
  });

  it('should display error message from thrown error', () => {
    const CustomError = () => {
      throw new Error('Custom error message');
    };

    render(
      <WidgetErrorBoundary>
        <CustomError />
      </WidgetErrorBoundary>
    );

    expect(screen.getByText('An error occurred: Custom error message')).toBeInTheDocument();
  });

  it('should call onError callback when error occurs', () => {
    const onError = vi.fn();
    
    render(
      <WidgetErrorBoundary onError={onError} widgetStep="business-search">
        <ThrowError shouldThrow={true} />
      </WidgetErrorBoundary>
    );

    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    );
  });

  it('should call onRetry callback when Try Again is clicked', () => {
    const onRetry = vi.fn();
    
    render(
      <WidgetErrorBoundary onRetry={onRetry}>
        <ThrowError shouldThrow={true} />
      </WidgetErrorBoundary>
    );

    fireEvent.click(screen.getByText('Try Again'));
    expect(onRetry).toHaveBeenCalled();
  });

  it('should reset error state when Try Again is clicked', () => {
    // Create a component that can be controlled to throw or not throw
    let shouldThrow = true;
    const ControlledError = () => {
      if (shouldThrow) {
        throw new Error('Test widget error');
      }
      return <div>Widget content</div>;
    };

    render(
      <WidgetErrorBoundary>
        <ControlledError />
      </WidgetErrorBoundary>
    );

    expect(screen.getByText('Widget Error')).toBeInTheDocument();

    // Change the error condition and click Try Again
    shouldThrow = false;
    fireEvent.click(screen.getByText('Try Again'));

    // After clicking Try Again, the error boundary should reset and re-render
    expect(screen.getByText('Widget content')).toBeInTheDocument();
    expect(screen.queryByText('Widget Error')).not.toBeInTheDocument();
  });

  it('should log error context including widget step', () => {
    const consoleSpy = vi.spyOn(console, 'error');
    
    render(
      <WidgetErrorBoundary widgetStep="business-search">
        <ThrowError shouldThrow={true} />
      </WidgetErrorBoundary>
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      'Widget error in step:',
      'business-search',
      expect.objectContaining({
        step: 'business-search',
        error: 'Test widget error',
        stack: expect.any(String),
        componentStack: expect.any(String),
        timestamp: expect.any(String),
      })
    );
  });
});