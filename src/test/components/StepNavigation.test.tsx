import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { StepNavigation } from '../../components/Layout/StepNavigation';
import { WidgetProvider } from '../../context/WidgetContext';
import { vi } from 'vitest';

// Mock all styled-components
vi.mock('styled-components', () => {
  const styled = (tag: any) => (styles: any) => {
    return React.forwardRef((props: any, ref: any) => {
      return React.createElement(tag, { ...props, ref, 'data-styled': true });
    });
  };
  
  // Add properties for all HTML tags
  styled.div = styled('div');
  styled.nav = styled('nav');
  styled.header = styled('header');
  styled.main = styled('main');
  styled.h1 = styled('h1');
  styled.h2 = styled('h2');
  styled.p = styled('p');
  styled.button = styled('button');
  
  return { default: styled };
});

const renderWithProviders = (initialEntries = ['/']) => {
  return render(
    <WidgetProvider>
      <MemoryRouter initialEntries={initialEntries}>
        <StepNavigation />
      </MemoryRouter>
    </WidgetProvider>
  );
};

describe('StepNavigation', () => {
  it('renders all navigation steps', () => {
    renderWithProviders();
    
    expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByText('Layout')).toBeInTheDocument();
    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('Reviews')).toBeInTheDocument();
    expect(screen.getByText('Style')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('shows step descriptions', () => {
    renderWithProviders();
    
    expect(screen.getByText('Find your business')).toBeInTheDocument();
    expect(screen.getByText('Choose template')).toBeInTheDocument();
    expect(screen.getByText('Configure header')).toBeInTheDocument();
    expect(screen.getByText('Review settings')).toBeInTheDocument();
    expect(screen.getByText('Customize appearance')).toBeInTheDocument();
    expect(screen.getByText('Final configuration')).toBeInTheDocument();
  });

  it('highlights the current step based on route', () => {
    renderWithProviders(['/layout']);
    
    // The layout step should be active (step 2)
    const layoutStep = screen.getByText('Layout').closest('div');
    expect(layoutStep).toBeInTheDocument();
  });

  it('shows step numbers correctly', () => {
    renderWithProviders();
    
    // Check that step numbers are displayed (1-6)
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('6')).toBeInTheDocument();
  });
});