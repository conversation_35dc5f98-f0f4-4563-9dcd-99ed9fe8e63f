import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import SyntaxHighlighter from '../../components/EmbedCode/SyntaxHighlighter';

describe('SyntaxHighlighter', () => {
  const htmlCode = `<div class="widget" data-id="123">
  <h1>Hello World</h1>
  <!-- This is a comment -->
  <script>
    var test = "value";
    function init() {
      console.log("initialized");
    }
  </script>
</div>`;

  const jsCode = `function greet(name) {
  var message = "Hello, " + name;
  return message;
}

// This is a comment
var result = greet("World");
console.log(result);`;

  const cssCode = `.widget {
  color: #333;
  background: white;
  /* This is a comment */
  font-size: 14px;
}

.widget h1 {
  margin: 0;
}`;

  it('should render HTML code with syntax highlighting', () => {
    render(<SyntaxHighlighter code={htmlCode} language="html" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
    expect(codeElement).toHaveClass('language-html');
  });

  it('should render JavaScript code with syntax highlighting', () => {
    render(<SyntaxHighlighter code={jsCode} language="javascript" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
    expect(codeElement).toHaveClass('language-javascript');
  });

  it('should render CSS code with syntax highlighting', () => {
    render(<SyntaxHighlighter code={cssCode} language="css" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
    expect(codeElement).toHaveClass('language-css');
  });

  it('should default to HTML language when no language specified', () => {
    render(<SyntaxHighlighter code={htmlCode} />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toHaveClass('language-html');
  });

  it('should apply custom className', () => {
    render(<SyntaxHighlighter code={htmlCode} className="custom-highlighter" />);

    const container = document.querySelector('.syntax-highlighter');
    expect(container).toHaveClass('custom-highlighter');
  });

  it('should escape HTML entities in code', () => {
    const codeWithEntities = '<div>&lt;script&gt;alert("xss")&lt;/script&gt;</div>';
    render(<SyntaxHighlighter code={codeWithEntities} language="html" />);

    const codeElement = document.querySelector('code');
    expect(codeElement?.innerHTML).toContain('&lt;');
    expect(codeElement?.innerHTML).toContain('&gt;');
  });

  it('should handle empty code', () => {
    render(<SyntaxHighlighter code="" language="html" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
    expect(codeElement?.innerHTML).toBe('');
  });

  it('should handle code with special characters', () => {
    const specialCode = 'const message = "Hello & welcome to <our> site!";';
    render(<SyntaxHighlighter code={specialCode} language="javascript" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
    // Should escape HTML entities
    expect(codeElement?.innerHTML).toContain('&amp;');
    expect(codeElement?.innerHTML).toContain('&lt;');
    expect(codeElement?.innerHTML).toContain('&gt;');
  });

  it('should preserve whitespace and line breaks', () => {
    const multilineCode = `line 1
  indented line 2
    more indented line 3`;
    
    render(<SyntaxHighlighter code={multilineCode} language="html" />);

    const preElement = document.querySelector('pre');
    expect(preElement).toHaveClass('code-block');
  });

  it('should handle very long lines', () => {
    const longCode = 'const veryLongVariableName = "this is a very long string that might cause wrapping issues in the code display";';
    
    render(<SyntaxHighlighter code={longCode} language="javascript" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
  });

  it('should handle mixed content in HTML', () => {
    const mixedHtml = `<div>
  <style>
    .test { color: red; }
  </style>
  <script>
    var x = 1;
  </script>
  <p>Regular content</p>
</div>`;

    render(<SyntaxHighlighter code={mixedHtml} language="html" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
  });

  it('should handle code with quotes and apostrophes', () => {
    const quotedCode = `const message = 'He said "Hello" to me';
const template = \`Template with \${variable}\`;`;

    render(<SyntaxHighlighter code={quotedCode} language="javascript" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
  });

  it('should handle CSS with various selectors', () => {
    const complexCss = `#id-selector {
  color: blue;
}

.class-selector:hover {
  background: rgba(0, 0, 0, 0.1);
}

element[attribute="value"] {
  border: 1px solid #ccc;
}`;

    render(<SyntaxHighlighter code={complexCss} language="css" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
  });

  it('should handle JavaScript with different comment styles', () => {
    const jsWithComments = `// Single line comment
function test() {
  /* Multi-line
     comment */
  return true;
}

/**
 * JSDoc comment
 * @param {string} name
 */
function greet(name) {
  return "Hello " + name;
}`;

    render(<SyntaxHighlighter code={jsWithComments} language="javascript" />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
  });

  it('should handle unsupported language gracefully', () => {
    render(<SyntaxHighlighter code="some code" language={"unsupported" as any} />);

    const codeElement = document.querySelector('code');
    expect(codeElement).toBeInTheDocument();
    expect(codeElement).toHaveClass('language-unsupported');
  });
});