import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { TemplateSelectionComponent } from '../../components/TemplateSelection/TemplateSelectionComponent';
import { WidgetProvider } from '../../context/WidgetContext';
import { TemplateType } from '../../types/widget';

// Mock the WidgetContext
const mockUpdateConfig = vi.fn();
const mockUseWidget = vi.fn();

vi.mock('../../context/WidgetContext', async () => {
  const actual = await vi.importActual('../../context/WidgetContext') as any;
  return {
    ...actual,
    useWidget: () => mockUseWidget(),
  };
});

// Helper function to render component with context
const renderWithProvider = (initialTemplate: TemplateType = 'carousel') => {
  mockUseWidget.mockReturnValue({
    config: {
      template: initialTemplate,
      styling: {
        colors: {
          primary: '#4285f4',
          secondary: '#34a853',
          background: '#ffffff',
          text: '#333333',
          border: '#e0e0e0',
        },
        fonts: {
          family: 'Inter, sans-serif',
          size: '14px',
          weight: '400',
        },
        dimensions: {
          width: '400px',
          height: '300px',
          borderRadius: '8px',
        },
        spacing: {
          padding: '16px',
          margin: '0px',
          gap: '12px',
        },
      },
      settings: {
        maxReviews: 5,
        minRating: 1,
        sortBy: 'newest' as const,
        showPhotos: true,
        showDates: true,
        autoRefresh: true,
      },
    },
    updateConfig: mockUpdateConfig,
    resetConfig: vi.fn(),
    currentStep: 1,
    setCurrentStep: vi.fn(),
  });

  return render(<TemplateSelectionComponent />);
};

describe('TemplateSelectionComponent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with title and description', () => {
    renderWithProvider();
    
    expect(screen.getByText('Choose Your Template')).toBeInTheDocument();
    expect(screen.getByText(/Select from our collection of professionally designed widget templates/)).toBeInTheDocument();
  });

  it('displays all 6 template options', () => {
    renderWithProvider();
    
    expect(screen.getByText('Carousel Widget')).toBeInTheDocument();
    expect(screen.getByText('Badge')).toBeInTheDocument();
    expect(screen.getByText('Grid with AI Summary')).toBeInTheDocument();
    expect(screen.getByText('Simple Carousel')).toBeInTheDocument();
    expect(screen.getByText('Slider')).toBeInTheDocument();
    expect(screen.getByText('Floating Badge')).toBeInTheDocument();
  });

  it('shows template descriptions', () => {
    renderWithProvider();
    
    expect(screen.getByText(/Interactive carousel with navigation arrows/)).toBeInTheDocument();
    expect(screen.getByText(/Compact badge showing overall rating/)).toBeInTheDocument();
    expect(screen.getByText(/Grid layout with AI-generated summary/)).toBeInTheDocument();
    expect(screen.getByText(/Clean, minimal carousel design/)).toBeInTheDocument();
    expect(screen.getByText(/Horizontal slider with smooth auto-play/)).toBeInTheDocument();
    expect(screen.getByText(/Floating badge that can be positioned anywhere/)).toBeInTheDocument();
  });

  it('highlights the currently selected template', () => {
    renderWithProvider('badge');
    
    const badgeCard = screen.getByText('Badge').closest('div');
    expect(badgeCard).toHaveStyle('border: 2px solid #4285f4');
    expect(screen.getByText('Selected')).toBeInTheDocument();
  });

  it('calls updateConfig when a template is selected', () => {
    renderWithProvider();
    
    const gridTemplate = screen.getByText('Grid with AI Summary');
    fireEvent.click(gridTemplate);
    
    expect(mockUpdateConfig).toHaveBeenCalledWith({ template: 'grid' });
  });

  it('updates selection when different templates are clicked', () => {
    renderWithProvider();
    
    // Click on slider template
    const sliderTemplate = screen.getByText('Slider');
    fireEvent.click(sliderTemplate);
    expect(mockUpdateConfig).toHaveBeenCalledWith({ template: 'slider' });
    
    // Click on floating badge template
    const floatingBadgeTemplate = screen.getByText('Floating Badge');
    fireEvent.click(floatingBadgeTemplate);
    expect(mockUpdateConfig).toHaveBeenCalledWith({ template: 'floating-badge' });
  });

  it('shows visual feedback on hover', () => {
    renderWithProvider();
    
    const carouselCard = screen.getByText('Carousel Widget').closest('div');
    
    // Test hover styles are applied (this tests the CSS is structured correctly)
    expect(carouselCard).toHaveStyle('cursor: pointer');
    expect(carouselCard).toHaveStyle('transition: all 0.2s ease');
  });

  it('renders template previews for each template type', () => {
    renderWithProvider();
    
    // Check that preview components are rendered
    // We can't easily test the exact preview content without more complex setup,
    // but we can verify the structure is there
    const templateCards = screen.getAllByText(/Widget|Badge|Grid|Carousel|Slider/);
    expect(templateCards.length).toBeGreaterThan(6); // Template names + preview content
  });

  it('maintains responsive grid layout structure', () => {
    renderWithProvider();
    
    // Find the grid container by looking for the parent of template cards
    const carouselCard = screen.getByText('Carousel Widget').closest('div');
    const gridContainer = carouselCard?.parentElement;
    
    expect(gridContainer).toHaveStyle('display: grid');
  });

  it('handles template selection with keyboard navigation', () => {
    renderWithProvider();
    
    const badgeTemplate = screen.getByText('Badge').closest('div');
    
    // Simulate keyboard interaction
    if (badgeTemplate) {
      fireEvent.keyDown(badgeTemplate, { key: 'Enter' });
      // Since we're using onClick, we need to simulate the click
      fireEvent.click(badgeTemplate);
    }
    
    expect(mockUpdateConfig).toHaveBeenCalledWith({ template: 'badge' });
  });

  it('renders correctly with different initial template selections', () => {
    // Test with different initial templates
    const templates: TemplateType[] = ['carousel', 'badge', 'grid', 'simple-carousel', 'slider', 'floating-badge'];
    
    templates.forEach((template) => {
      vi.clearAllMocks();
      const { unmount } = renderWithProvider(template);
      
      // Should show the selected badge for the current template
      expect(screen.getByText('Selected')).toBeInTheDocument();
      
      // Clean up before next iteration
      unmount();
    });
  });

  it('displays template cards in a responsive grid', () => {
    renderWithProvider();
    
    // Verify all template names are present (which means all cards are rendered)
    expect(screen.getByText('Carousel Widget')).toBeInTheDocument();
    expect(screen.getByText('Badge')).toBeInTheDocument();
    expect(screen.getByText('Grid with AI Summary')).toBeInTheDocument();
    expect(screen.getByText('Simple Carousel')).toBeInTheDocument();
    expect(screen.getByText('Slider')).toBeInTheDocument();
    expect(screen.getByText('Floating Badge')).toBeInTheDocument();
  });
});

describe('TemplateSelectionComponent Integration', () => {
  it('works correctly within WidgetProvider context', () => {
    const TestWrapper = () => (
      <WidgetProvider>
        <TemplateSelectionComponent />
      </WidgetProvider>
    );
    
    render(<TestWrapper />);
    
    expect(screen.getByText('Choose Your Template')).toBeInTheDocument();
    expect(screen.getByText('Carousel Widget')).toBeInTheDocument();
    
    // Test that clicking works with real context
    const badgeTemplate = screen.getByText('Badge');
    fireEvent.click(badgeTemplate);
    
    // The component should re-render with the new selection
    // In a real integration, this would show the "Selected" badge
  });
});