# Production Environment Configuration
NODE_ENV=production
APP_VERSION=1.0.0

# Supabase Database Configuration
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_URL=redis://redis:6379

# Google API Configuration
GOOGLE_PLACES_API_KEY=your_google_places_api_key
GOOGLE_MY_BUSINESS_API_KEY=your_google_my_business_api_key

# Application Configuration
API_BASE_URL=https://api.yourdomain.com
FRONTEND_URL=https://app.yourdomain.com
WIDGET_CDN_URL=https://cdn.yourdomain.com

# Security Configuration
JWT_SECRET=your_jwt_secret_key
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Monitoring Configuration
LOG_LEVEL=info
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
CACHE_TTL_REVIEWS=3600
CACHE_TTL_BUSINESS=86400
CACHE_TTL_WIDGET_CONFIG=43200

# Email Configuration (for alerts)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_smtp_password

# Monitoring URLs
GRAFANA_PASSWORD=your_grafana_password
SLACK_WEBHOOK=your_slack_webhook_url