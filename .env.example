# Google API Configuration
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
GOOGLE_MY_BUSINESS_API_KEY=your_google_my_business_api_key_here

# Supabase Database Configuration
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=development
PORT=3001

# Frontend Configuration
VITE_API_URL=http://localhost:3001