import{j as e,u as i}from"./index-ad9d0ad0.js";import{d as t}from"./chunk-69735360.js";import"./chunk-0fa44877.js";const r=t.div`
  width: 100%;
  height: 100%;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666666;
`,n=[{id:"1",authorName:"<PERSON>",rating:5,text:"Excellent service and friendly staff. Highly recommend!",publishedDate:"2 days ago"},{id:"2",authorName:"<PERSON>",rating:4,text:"Great experience overall. Will definitely come back.",publishedDate:"1 week ago"},{id:"3",authorName:"<PERSON>",rating:5,text:"Outstanding quality and attention to detail.",publishedDate:"2 weeks ago"}],o=t.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`,d=t.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`,a=t.div`
  display: flex;
  gap: 4px;
`,s=t.div`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${e=>e.$active?"#4285f4":"#e0e0e0"};
`,p=t.div`
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
`,l=t.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
`,x=t.span`
  font-weight: 500;
  font-size: 11px;
`,c=t.div`
  color: #ffa500;
  font-size: 10px;
`,h=t.p`
  margin: 0;
  font-size: 10px;
  line-height: 1.3;
  color: #666;
`,g=t.div`
  background: white;
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 11px;
`,f=t.div`
  display: flex;
  align-items: center;
  gap: 2px;
  color: #ffa500;
  font-weight: 600;
`,m=t.span`
  color: #666;
  font-size: 10px;
`,u=t.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`,b=t.div`
  text-align: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
`,v=t.div`
  background: #f8f9fa;
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 9px;
  color: #666;
  text-align: center;
`,j=t.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
`,y=t.div`
  padding: 4px;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  font-size: 9px;
`,w=t.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`,z=t.div`
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
`,S=t.div`
  width: 100%;
  background: white;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`,k=t.div`
  display: flex;
  gap: 6px;
  overflow: hidden;
`,$=t.div`
  min-width: 60px;
  padding: 6px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  font-size: 9px;
`,C=t.div`
  position: relative;
  width: 100%;
  height: 100px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 6px;
  overflow: hidden;
`,W=t.div`
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 8px;
`;function B({templateType:i,isSelected:t}){return e.jsx(r,{children:(()=>{switch(i){case"carousel":return e.jsxs(o,{children:[e.jsxs(d,{children:[e.jsx("span",{style:{fontSize:"11px",fontWeight:"600"},children:"Customer Reviews"}),e.jsxs(a,{children:[e.jsx(s,{$active:!0}),e.jsx(s,{}),e.jsx(s,{})]})]}),e.jsxs(p,{children:[e.jsxs(l,{children:[e.jsx(x,{children:n[0].authorName}),e.jsx(c,{children:"★★★★★"})]}),e.jsx(h,{children:n[0].text})]})]});case"badge":return e.jsxs(g,{children:[e.jsxs(f,{children:[e.jsx("span",{children:"★"}),e.jsx("span",{children:"4.8"})]}),e.jsx(m,{children:"Based on 127 reviews"})]});case"grid":return e.jsxs(u,{children:[e.jsx(b,{children:e.jsx("div",{style:{fontSize:"11px",fontWeight:"600"},children:"★ 4.8 (127 reviews)"})}),e.jsx(v,{children:"AI Summary: Customers love the excellent service and friendly staff"}),e.jsx(j,{children:n.slice(0,2).map((i,t)=>e.jsxs(y,{children:[e.jsx("div",{style:{fontWeight:"500",marginBottom:"2px"},children:i.authorName}),e.jsx("div",{style:{color:"#ffa500",fontSize:"8px"},children:"★★★★★"}),e.jsxs("div",{style:{marginTop:"2px"},children:[i.text.substring(0,30),"..."]})]},t))})]});case"simple-carousel":return e.jsxs(w,{children:[e.jsx("div",{style:{fontSize:"11px",fontWeight:"600",marginBottom:"6px"},children:n[0].authorName}),e.jsx("div",{style:{color:"#ffa500",fontSize:"10px",marginBottom:"4px"},children:"★★★★★"}),e.jsxs("div",{style:{fontSize:"9px",color:"#666"},children:['"',n[0].text,'"']}),e.jsxs(z,{children:[e.jsx(s,{$active:!0}),e.jsx(s,{}),e.jsx(s,{})]})]});case"slider":return e.jsxs(S,{children:[e.jsx("div",{style:{fontSize:"11px",fontWeight:"600",marginBottom:"8px",textAlign:"center"},children:"Recent Reviews"}),e.jsx(k,{children:n.map((i,t)=>e.jsxs($,{children:[e.jsx("div",{style:{fontWeight:"500",marginBottom:"2px"},children:i.authorName}),e.jsx("div",{style:{color:"#ffa500",fontSize:"8px"},children:"★★★★★"})]},t))})]});case"floating-badge":return e.jsxs(C,{children:[e.jsx("div",{style:{position:"absolute",top:"8px",left:"8px",fontSize:"9px",color:"#666",background:"rgba(255,255,255,0.8)",padding:"2px 4px",borderRadius:"3px"},children:"Your Website"}),e.jsxs(W,{children:[e.jsx("div",{style:{color:"#ffa500",fontWeight:"600"},children:"★ 4.8"}),e.jsx("div",{style:{color:"#666"},children:"127"})]})]});default:return e.jsx("div",{children:"Preview not available"})}})()})}const N=t.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`,A=t.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
  text-align: center;
`,D=t.p`
  font-size: 16px;
  color: #666666;
  margin: 0 0 32px 0;
  text-align: center;
  line-height: 1.6;
`,I=t.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`,R=t.div`
  background: #ffffff;
  border: 2px solid ${e=>e.$isSelected?"#4285f4":"#e0e0e0"};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    border-color: ${e=>e.$isSelected?"#4285f4":"#999999"};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  ${e=>e.$isSelected&&"\n    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.2);\n  "}
`,T=t.div`
  position: absolute;
  top: 12px;
  right: 12px;
  background: #4285f4;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
`,G=t.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
`,E=t.p`
  font-size: 14px;
  color: #666666;
  margin: 0 0 16px 0;
  line-height: 1.4;
`,F=t.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
`,H=[{type:"carousel",name:"Carousel Widget",description:"Interactive carousel with navigation arrows and smooth transitions between reviews."},{type:"badge",name:"Badge",description:"Compact badge showing overall rating and review count, perfect for headers or sidebars."},{type:"grid",name:"Grid with AI Summary",description:"Grid layout with AI-generated summary highlighting key themes from customer reviews."},{type:"simple-carousel",name:"Simple Carousel",description:"Clean, minimal carousel design focusing on review content without distractions."},{type:"slider",name:"Slider",description:"Horizontal slider with smooth auto-play and manual navigation controls."},{type:"floating-badge",name:"Floating Badge",description:"Floating badge that can be positioned anywhere on your page with customizable placement."}];function Y(){const{config:t,updateConfig:r}=i();return e.jsxs(N,{children:[e.jsx(A,{children:"Choose Your Template"}),e.jsx(D,{children:"Select from our collection of professionally designed widget templates to match your website's style."}),e.jsx(I,{children:H.map(i=>e.jsxs(R,{$isSelected:t.template===i.type,onClick:()=>{return e=i.type,void r({template:e});var e},children:[t.template===i.type&&e.jsx(T,{children:"Selected"}),e.jsx(G,{children:i.name}),e.jsx(E,{children:i.description}),e.jsx(F,{children:e.jsx(B,{templateType:i.type,isSelected:t.template===i.type})})]},i.type))})]})}export{Y as TemplateSelectionComponent};
//# sourceMappingURL=TemplateSelectionComponent-e63c46c2.js.map
