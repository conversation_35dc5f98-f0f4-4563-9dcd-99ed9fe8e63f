{"version": 3, "file": "SourcePage-948a5ddb.js", "sources": ["../../src/services/api.ts", "../../src/components/BusinessSearch/BusinessSearchComponent.tsx", "../../src/pages/SourcePage.tsx"], "sourcesContent": ["// API service for making HTTP requests to the backend\n\nconst API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';\n\n// Error types\nexport interface ApiError {\n  code: string;\n  message: string;\n  details?: any;\n  timestamp: string;\n}\n\nexport class ApiException extends Error {\n  constructor(\n    public error: ApiError,\n    public status: number\n  ) {\n    super(error.message);\n    this.name = 'ApiException';\n  }\n}\n\n// Request/Response types\nexport interface BusinessSearchRequest {\n  query: string;\n  location?: string;\n  googleMapsUrl?: string;\n}\n\nexport interface Business {\n  id: string;\n  placeId: string;\n  name: string;\n  address?: string;\n  rating?: number;\n  reviewCount?: number;\n  photoUrl?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface BusinessSearchResponse {\n  businesses: Business[];\n  totalResults: number;\n}\n\n// HTTP client utility\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE_URL}${endpoint}`;\n  \n  const config: RequestInit = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n    \n    if (!response.ok) {\n      let errorData: ApiError;\n      \n      try {\n        errorData = await response.json();\n      } catch {\n        // If we can't parse the error response, create a generic error\n        errorData = {\n          code: 'UNKNOWN_ERROR',\n          message: `HTTP ${response.status}: ${response.statusText}`,\n          timestamp: new Date().toISOString(),\n        };\n      }\n      \n      throw new ApiException(errorData, response.status);\n    }\n\n    return await response.json();\n  } catch (error) {\n    if (error instanceof ApiException) {\n      throw error;\n    }\n    \n    // Handle network errors or other fetch failures\n    if (error instanceof TypeError && error.message.includes('fetch')) {\n      throw new ApiException({\n        code: 'NETWORK_ERROR',\n        message: 'Unable to connect to the server. Please check your internet connection.',\n        timestamp: new Date().toISOString(),\n      }, 0);\n    }\n    \n    // Generic error fallback\n    throw new ApiException({\n      code: 'UNKNOWN_ERROR',\n      message: error instanceof Error ? error.message : 'An unexpected error occurred',\n      timestamp: new Date().toISOString(),\n    }, 0);\n  }\n}\n\n// Business search API\nexport async function searchBusinesses(\n  request: BusinessSearchRequest\n): Promise<BusinessSearchResponse> {\n  return apiRequest<BusinessSearchResponse>('/business/search', {\n    method: 'POST',\n    body: JSON.stringify(request),\n  });\n}\n\n// Business details API (for future use)\nexport async function getBusinessDetails(businessId: string): Promise<Business> {\n  return apiRequest<Business>(`/business/${businessId}`);\n}\n\n// Embed code types and API\nexport interface EmbedCodeResult {\n  embedCode: string;\n  widgetId: string;\n  embedUrl: string;\n  previewUrl: string;\n}\n\nexport interface GenerateEmbedCodeRequest {\n  configId: string;\n  baseUrl?: string;\n}\n\nexport interface ValidateEmbedCodeRequest {\n  embedCode: string;\n}\n\nexport interface ValidateEmbedCodeResponse {\n  isValid: boolean;\n  widgetId: string | null;\n  hasWidgetId: boolean;\n}\n\n// Generate embed code for widget configuration\nexport async function generateEmbedCode(\n  request: GenerateEmbedCodeRequest\n): Promise<EmbedCodeResult> {\n  return apiRequest<EmbedCodeResult>('/embed-code/generate', {\n    method: 'POST',\n    body: JSON.stringify(request),\n  });\n}\n\n// Generate iframe embed code\nexport async function generateIframeEmbedCode(\n  request: GenerateEmbedCodeRequest\n): Promise<{ embedCode: string; widgetId: string; type: string }> {\n  return apiRequest<{ embedCode: string; widgetId: string; type: string }>('/embed-code/generate/iframe', {\n    method: 'POST',\n    body: JSON.stringify(request),\n  });\n}\n\n// Get existing embed code for widget\nexport async function getEmbedCode(\n  widgetId: string,\n  baseUrl?: string\n): Promise<EmbedCodeResult> {\n  const params = baseUrl ? `?baseUrl=${encodeURIComponent(baseUrl)}` : '';\n  return apiRequest<EmbedCodeResult>(`/embed-code/${widgetId}${params}`);\n}\n\n// Validate embed code\nexport async function validateEmbedCode(\n  request: ValidateEmbedCodeRequest\n): Promise<ValidateEmbedCodeResponse> {\n  return apiRequest<ValidateEmbedCodeResponse>('/embed-code/validate', {\n    method: 'POST',\n    body: JSON.stringify(request),\n  });\n}", "import React, { useState, useEffect, useCallback } from 'react';\nimport styled from 'styled-components';\nimport { Business } from '../../types/widget';\nimport { useWidget } from '../../context/WidgetContext';\nimport { searchBusinesses, ApiException } from '../../services/api';\n\n// Styled components\nconst SearchContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n\nconst SearchForm = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n`;\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-size: 14px;\n  font-weight: 500;\n  color: #333333;\n`;\n\nconst Input = styled.input`\n  padding: 12px 16px;\n  border: 2px solid #e0e0e0;\n  border-radius: 8px;\n  font-size: 16px;\n  transition: border-color 0.2s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #4285f4;\n  }\n\n  &:disabled {\n    background-color: #f5f5f5;\n    cursor: not-allowed;\n  }\n`;\n\nconst SearchButton = styled.button`\n  padding: 12px 24px;\n  background-color: #4285f4;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n\n  &:hover:not(:disabled) {\n    background-color: #3367d6;\n  }\n\n  &:disabled {\n    background-color: #cccccc;\n    cursor: not-allowed;\n  }\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 20px;\n  height: 20px;\n  border: 2px solid #ffffff;\n  border-top: 2px solid transparent;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst ResultsContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n`;\n\nconst BusinessCard = styled.div<{ selected?: boolean }>`\n  padding: 16px;\n  border: 2px solid ${props => props.selected ? '#4285f4' : '#e0e0e0'};\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background-color: ${props => props.selected ? '#f8f9ff' : '#ffffff'};\n\n  &:hover {\n    border-color: #4285f4;\n    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);\n  }\n`;\n\nconst BusinessInfo = styled.div`\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n`;\n\nconst BusinessPhoto = styled.img`\n  width: 60px;\n  height: 60px;\n  border-radius: 8px;\n  object-fit: cover;\n  flex-shrink: 0;\n`;\n\nconst BusinessDetails = styled.div`\n  flex: 1;\n`;\n\nconst BusinessName = styled.h3`\n  font-size: 18px;\n  font-weight: 600;\n  color: #333333;\n  margin: 0 0 4px 0;\n`;\n\nconst BusinessAddress = styled.p`\n  font-size: 14px;\n  color: #666666;\n  margin: 0 0 8px 0;\n`;\n\nconst BusinessRating = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: #666666;\n`;\n\nconst StarRating = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 2px;\n`;\n\nconst Star = styled.span<{ filled: boolean }>`\n  color: ${props => props.filled ? '#ffa500' : '#e0e0e0'};\n  font-size: 16px;\n`;\n\nconst ErrorMessage = styled.div`\n  padding: 16px;\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 8px;\n  color: #dc2626;\n  font-size: 14px;\n`;\n\nconst EmptyState = styled.div`\n  padding: 32px;\n  text-align: center;\n  color: #666666;\n  font-size: 16px;\n`;\n\nconst HelpText = styled.p`\n  font-size: 14px;\n  color: #666666;\n  margin: 8px 0 0 0;\n  line-height: 1.4;\n`;\n\n// Interfaces\ninterface BusinessSearchComponentProps {\n  onBusinessSelect?: (business: Business) => void;\n}\n\n// Debounce hook\nfunction useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n\nexport function BusinessSearchComponent({ onBusinessSelect }: BusinessSearchComponentProps) {\n  const { config, updateConfig } = useWidget();\n  const [query, setQuery] = useState('');\n  const [location, setLocation] = useState('');\n  const [googleMapsUrl, setGoogleMapsUrl] = useState('');\n  const [businesses, setBusinesses] = useState<Business[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(config.business || null);\n  const [hasSearched, setHasSearched] = useState(false);\n\n  // Debounced search for real-time functionality\n  const debouncedQuery = useDebounce(query, 500);\n  const debouncedLocation = useDebounce(location, 500);\n\n  // Auto-search when debounced values change (but not on initial load)\n  useEffect(() => {\n    if (debouncedQuery.trim() && hasSearched) {\n      handleSearch();\n    }\n  }, [debouncedQuery, debouncedLocation]);\n\n  const handleSearch = useCallback(async () => {\n    if (!query.trim()) {\n      setError('Please enter a business name to search');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setHasSearched(true);\n\n    try {\n      const searchRequest = {\n        query: query.trim(),\n        ...(location.trim() && { location: location.trim() }),\n        ...(googleMapsUrl.trim() && { googleMapsUrl: googleMapsUrl.trim() }),\n      };\n\n      const response = await searchBusinesses(searchRequest);\n      setBusinesses(response.businesses);\n\n      if (response.businesses.length === 0) {\n        setError('No businesses found. Try adjusting your search terms or adding a location.');\n      }\n    } catch (err) {\n      console.error('Search error:', err);\n      if (err instanceof ApiException && err.error) {\n        setError(err.error.message);\n      } else if (err instanceof Error) {\n        setError(err.message);\n      } else {\n        setError('Failed to search for businesses. Please try again.');\n      }\n      setBusinesses([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [query, location, googleMapsUrl]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    handleSearch();\n  };\n\n  const handleBusinessSelect = (business: Business) => {\n    setSelectedBusiness(business);\n    updateConfig({ business });\n    onBusinessSelect?.(business);\n  };\n\n  const renderStars = (rating: number) => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 >= 0.5;\n\n    for (let i = 0; i < 5; i++) {\n      if (i < fullStars) {\n        stars.push(<Star key={i} filled={true}>★</Star>);\n      } else if (i === fullStars && hasHalfStar) {\n        stars.push(<Star key={i} filled={true}>☆</Star>);\n      } else {\n        stars.push(<Star key={i} filled={false}>☆</Star>);\n      }\n    }\n\n    return stars;\n  };\n\n  return (\n    <SearchContainer>\n      <SearchForm onSubmit={handleSubmit}>\n        <InputGroup>\n          <Label htmlFor=\"business-query\">Business Name *</Label>\n          <Input\n            id=\"business-query\"\n            type=\"text\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            placeholder=\"Enter your business name...\"\n            disabled={loading}\n            required\n          />\n          <HelpText>\n            Enter the name of your business as it appears on Google\n          </HelpText>\n        </InputGroup>\n\n        <InputGroup>\n          <Label htmlFor=\"business-location\">Location (Optional)</Label>\n          <Input\n            id=\"business-location\"\n            type=\"text\"\n            value={location}\n            onChange={(e) => setLocation(e.target.value)}\n            placeholder=\"City, State or Full Address...\"\n            disabled={loading}\n          />\n          <HelpText>\n            Add a location to help narrow down search results\n          </HelpText>\n        </InputGroup>\n\n        <InputGroup>\n          <Label htmlFor=\"google-maps-url\">Google Maps URL (Alternative)</Label>\n          <Input\n            id=\"google-maps-url\"\n            type=\"url\"\n            value={googleMapsUrl}\n            onChange={(e) => setGoogleMapsUrl(e.target.value)}\n            placeholder=\"https://maps.google.com/...\"\n            disabled={loading}\n          />\n          <HelpText>\n            Alternatively, paste a Google Maps link to your business\n          </HelpText>\n        </InputGroup>\n\n        <SearchButton type=\"submit\" disabled={loading || !query.trim()}>\n          {loading && <LoadingSpinner />}\n          {loading ? 'Searching...' : 'Search Business'}\n        </SearchButton>\n      </SearchForm>\n\n      {error && (\n        <ErrorMessage>\n          {error}\n        </ErrorMessage>\n      )}\n\n      {businesses.length > 0 && (\n        <ResultsContainer>\n          {businesses.map((business) => (\n            <BusinessCard\n              key={business.placeId}\n              selected={selectedBusiness?.placeId === business.placeId}\n              onClick={() => handleBusinessSelect(business)}\n            >\n              <BusinessInfo>\n                {business.photoUrl && (\n                  <BusinessPhoto\n                    src={business.photoUrl}\n                    alt={business.name}\n                    onError={(e) => {\n                      e.currentTarget.style.display = 'none';\n                    }}\n                  />\n                )}\n                <BusinessDetails>\n                  <BusinessName>{business.name}</BusinessName>\n                  {business.address && (\n                    <BusinessAddress>{business.address}</BusinessAddress>\n                  )}\n                  <BusinessRating>\n                    <StarRating>\n                      {renderStars(business.rating || 0)}\n                    </StarRating>\n                    <span>\n                      {business.rating?.toFixed(1) || 'No rating'} \n                      {business.reviewCount && ` (${business.reviewCount} reviews)`}\n                    </span>\n                  </BusinessRating>\n                </BusinessDetails>\n              </BusinessInfo>\n            </BusinessCard>\n          ))}\n        </ResultsContainer>\n      )}\n\n      {hasSearched && businesses.length === 0 && !loading && !error && (\n        <EmptyState>\n          No businesses found for your search. Try different keywords or add a location.\n        </EmptyState>\n      )}\n    </SearchContainer>\n  );\n}", "import React from 'react';\nimport styled from 'styled-components';\nimport { BusinessSearchComponent } from '../components/BusinessSearch/BusinessSearchComponent';\nimport { Business } from '../types/widget';\nimport WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';\n\nconst PageContainer = styled.div`\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n\nconst Title = styled.h2`\n  font-size: 28px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 16px;\n  text-align: center;\n`;\n\nconst Description = styled.p`\n  font-size: 16px;\n  color: #666666;\n  margin-bottom: 32px;\n  line-height: 1.6;\n  text-align: center;\n`;\n\nconst SelectedBusinessInfo = styled.div`\n  margin-top: 24px;\n  padding: 16px;\n  background-color: #f0f8ff;\n  border: 1px solid #4285f4;\n  border-radius: 8px;\n`;\n\nconst SelectedBusinessTitle = styled.h3`\n  font-size: 16px;\n  font-weight: 600;\n  color: #333333;\n  margin: 0 0 8px 0;\n`;\n\nconst SelectedBusinessText = styled.p`\n  font-size: 14px;\n  color: #666666;\n  margin: 0;\n`;\n\nexport function SourcePage() {\n  const handleBusinessSelect = (business: Business) => {\n    console.log('Business selected:', business);\n  };\n\n  return (\n    <PageContainer>\n      <Title>Find Your Business</Title>\n      <Description>\n        Search for your business by name and address, or paste a Google Maps link to get started.\n      </Description>\n      <WidgetErrorBoundary widgetStep=\"business-search\">\n        <BusinessSearchComponent onBusinessSelect={handleBusinessSelect} />\n      </WidgetErrorBoundary>\n    </PageContainer>\n  );\n}"], "names": ["API_BASE_URL", "VITE_API_URL", "ApiException", "Error", "constructor", "error", "status", "super", "message", "this", "name", "async", "searchBusinesses", "request", "endpoint", "options", "url", "config", "headers", "response", "fetch", "ok", "errorData", "json", "code", "statusText", "timestamp", "Date", "toISOString", "TypeError", "includes", "apiRequest", "method", "body", "JSON", "stringify", "SearchContainer", "styled", "div", "SearchForm", "form", "InputGroup", "Label", "label", "Input", "input", "SearchButton", "button", "LoadingSpinner", "ResultsContainer", "BusinessCard", "props", "selected", "BusinessInfo", "BusinessPhoto", "img", "BusinessDetails", "BusinessName", "h3", "BusinessAddress", "p", "BusinessRating", "StarRating", "Star", "span", "filled", "ErrorMessage", "EmptyState", "HelpText", "useDebounce", "value", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "useState", "useEffect", "handler", "setTimeout", "clearTimeout", "BusinessSearchComponent", "onBusinessSelect", "updateConfig", "useWidget", "query", "<PERSON><PERSON><PERSON><PERSON>", "location", "setLocation", "googleMapsUrl", "setGoogleMapsUrl", "businesses", "setBusinesses", "loading", "setLoading", "setError", "selectedBusiness", "setSelectedBusiness", "business", "hasSearched", "setHasSearched", "deboun<PERSON><PERSON><PERSON><PERSON>", "debouncedLocation", "trim", "handleSearch", "useCallback", "searchRequest", "length", "err", "renderStars", "rating", "stars", "fullStars", "Math", "floor", "hasHalfStar", "i", "push", "children", "jsxs", "onSubmit", "e", "preventDefault", "jsx", "htmlFor", "jsxRuntimeExports", "id", "type", "onChange", "target", "placeholder", "disabled", "required", "map", "placeId", "onClick", "handleBusinessSelect", "photoUrl", "src", "alt", "onError", "currentTarget", "style", "display", "address", "_a", "toFixed", "reviewCount", "<PERSON><PERSON><PERSON><PERSON>", "Title", "h2", "Description", "SourcePage", "WidgetErrorBoundary", "widgetStep"], "mappings": "uKAEA,MAAMA,EAA+B,CAAA,EAAAC,cAAgB,4BAU9C,MAAMC,UAAqBC,MAChC,WAAAC,CACSC,EACAC,GAEPC,MAAMF,EAAMG,SAHLC,KAAAJ,MAAAA,EACAI,KAAAH,OAAAA,EAGPG,KAAKC,KAAO,cACd,EAuFFC,eAAsBC,EACpBC,GAEA,OA9DFF,eACEG,EACAC,EAAuB,IAEvB,MAAMC,EAAM,GAAGhB,IAAec,IAExBG,EAAsB,CAC1BC,QAAS,CACP,eAAgB,sBACbH,EAAQG,YAEVH,GAGD,IACF,MAAMI,QAAiBC,MAAMJ,EAAKC,GAE9B,IAACE,EAASE,GAAI,CACZ,IAAAC,EAEA,IACUA,QAAMH,EAASI,MAAK,CAC1B,MAEMD,EAAA,CACVE,KAAM,gBACNhB,QAAS,QAAQW,EAASb,WAAWa,EAASM,aAC9CC,WAAW,IAAIC,MAAOC,cAE1B,CAEA,MAAM,IAAI1B,EAAaoB,EAAWH,EAASb,OAC7C,CAEO,aAAMa,EAASI,aACflB,GACP,GAAIA,aAAiBH,EACb,MAAAG,EAIR,GAAIA,aAAiBwB,WAAaxB,EAAMG,QAAQsB,SAAS,SACvD,MAAM,IAAI5B,EAAa,CACrBsB,KAAM,gBACNhB,QAAS,0EACTkB,WAAW,IAAIC,MAAOC,eACrB,GAIL,MAAM,IAAI1B,EAAa,CACrBsB,KAAM,gBACNhB,QAASH,aAAiBF,MAAQE,EAAMG,QAAU,+BAClDkB,WAAW,IAAIC,MAAOC,eACrB,EACL,CACF,CAMSG,CAAmC,mBAAoB,CAC5DC,OAAQ,OACRC,KAAMC,KAAKC,UAAUtB,IAEzB,CC1GA,MAAMuB,EAAkBC,EAAOC,GAAA;;;;EAMzBC,EAAaF,EAAOG,IAAA;;;;EAMpBC,EAAaJ,EAAOC,GAAA;;;;EAMpBI,EAAQL,EAAOM,KAAA;;;;EAMfC,EAAQP,EAAOQ,KAAA;;;;;;;;;;;;;;;;EAkBfC,EAAeT,EAAOU,MAAA;;;;;;;;;;;;;;;;;;;;;;;EAyBtBC,EAAiBX,EAAOC,GAAA;;;;;;;;;;;;EAcxBW,EAAmBZ,EAAOC,GAAA;;;;EAM1BY,EAAeb,EAAOC,GAAA;;sBAEGa,GAAAA,EAAMC,SAAW,UAAY;;;;sBAI7BD,GAAAA,EAAMC,SAAW,UAAY;;;;;;EAQtDC,EAAehB,EAAOC,GAAA;;;;EAMtBgB,EAAgBjB,EAAOkB,GAAA;;;;;;EAQvBC,EAAkBnB,EAAOC,GAAA;;EAIzBmB,EAAepB,EAAOqB,EAAA;;;;;EAOtBC,EAAkBtB,EAAOuB,CAAA;;;;EAMzBC,EAAiBxB,EAAOC,GAAA;;;;;;EAQxBwB,EAAazB,EAAOC,GAAA;;;;EAMpByB,EAAO1B,EAAO2B,IAAA;WACAb,GAAAA,EAAMc,OAAS,UAAY;;EAIzCC,EAAe7B,EAAOC,GAAA;;;;;;;EAStB6B,EAAa9B,EAAOC,GAAA;;;;;EAOpB8B,EAAW/B,EAAOuB,CAAA;;;;;EAaxB,SAASS,EAAeC,EAAUC,GAChC,MAAOC,EAAgBC,GAAqBC,WAAYJ,GAYjD,OAVPK,EAAAA,UAAU,KACF,MAAAC,EAAUC,WAAW,KACzBJ,EAAkBH,IACjBC,GAEH,MAAO,KACLO,aAAaF,KAEd,CAACN,EAAOC,IAEJC,CACT,CAEgB,SAAAO,GAAwBC,iBAAEA,IACxC,MAAM/D,OAAEA,EAAAgE,aAAQA,GAAiBC,KAC1BC,EAAOC,GAAYV,WAAS,KAC5BW,EAAUC,GAAeZ,WAAS,KAClCa,EAAeC,GAAoBd,WAAS,KAC5Ce,EAAYC,GAAiBhB,EAAAA,SAAqB,KAClDiB,EAASC,GAAclB,YAAS,IAChCrE,EAAOwF,GAAYnB,WAAwB,OAC3CoB,EAAkBC,GAAuBrB,EAA0BA,SAAAzD,EAAO+E,UAAY,OACtFC,EAAaC,GAAkBxB,YAAS,GAGzCyB,EAAiB9B,EAAYc,EAAO,KACpCiB,EAAoB/B,EAAYgB,EAAU,KAGhDV,EAAAA,UAAU,KACJwB,EAAeE,QAAUJ,QAG5B,CAACE,EAAgBC,IAEd,MAAAE,EAAeC,EAAAA,YAAY5F,UAC3B,GAACwE,EAAMkB,OAAP,CAKJT,GAAW,GACXC,EAAS,MACTK,GAAe,GAEX,IACF,MAAMM,EAAgB,CACpBrB,MAAOA,EAAMkB,UACThB,EAASgB,QAAU,CAAEhB,SAAUA,EAASgB,WACxCd,EAAcc,QAAU,CAAEd,cAAeA,EAAcc,SAGvDlF,QAAiBP,EAAiB4F,GACxCd,EAAcvE,EAASsE,YAEY,IAA/BtE,EAASsE,WAAWgB,QACtBZ,EAAS,oFAEJa,GAEHA,aAAexG,GAAgBwG,EAAIrG,MAC5BwF,EAAAa,EAAIrG,MAAMG,SACVkG,aAAevG,MACxB0F,EAASa,EAAIlG,SAEbqF,EAAS,sDAEXH,EAAc,GAAE,CAChB,QACAE,GAAW,EACb,CA/BA,MAFEC,EAAS,2CAkCV,CAACV,EAAOE,EAAUE,IAafoB,EAAeC,IACnB,MAAMC,EAAQ,GACRC,EAAYC,KAAKC,MAAMJ,GACvBK,EAAcL,EAAS,GAAK,GAElC,IAAA,IAASM,EAAI,EAAGA,EAAI,EAAGA,IACjBA,EAAIJ,EACAD,EAAAM,WAAMpD,EAAA,CAAaE,QAAQ,EAAMmD,SAAA,KAAjBF,IACbA,IAAMJ,GAAaG,EACtBJ,EAAAM,WAAMpD,EAAA,CAAaE,QAAQ,EAAMmD,SAAA,KAAjBF,IAEhBL,EAAAM,WAAMpD,EAAA,CAAaE,QAAQ,EAAOmD,SAAA,KAAlBF,IAInB,OAAAL,GAGT,cACGzE,EACC,CAAAgF,SAAA,GAACC,KAAA9E,EAAA,CAAW+E,SA/BMC,IACpBA,EAAEC,sBA+BEJ,SAAA,QAAC3E,EACC,CAAA2E,SAAA,CAACK,EAAAA,IAAA/E,EAAA,CAAMgF,QAAQ,iBAAiBN,SAAe,oBAC/CO,EAAAF,IAAC7E,EAAA,CACCgF,GAAG,iBACHC,KAAK,OACLvD,MAAOa,EACP2C,SAAWP,GAAMnC,EAASmC,EAAEQ,OAAOzD,OACnC0D,YAAY,8BACZC,SAAUtC,EACVuC,UAAQ,MAEVT,IAACrD,GAASgD,SAEV,sEAGD3E,EACC,CAAA2E,SAAA,CAACK,EAAAA,IAAA/E,EAAA,CAAMgF,QAAQ,oBAAoBN,SAAmB,wBACtDO,EAAAF,IAAC7E,EAAA,CACCgF,GAAG,oBACHC,KAAK,OACLvD,MAAOe,EACPyC,SAAWP,GAAMjC,EAAYiC,EAAEQ,OAAOzD,OACtC0D,YAAY,iCACZC,SAAUtC,MAEZ8B,IAACrD,GAASgD,SAEV,gEAGD3E,EACC,CAAA2E,SAAA,CAACK,EAAAA,IAAA/E,EAAA,CAAMgF,QAAQ,kBAAkBN,SAA6B,kCAC9DO,EAAAF,IAAC7E,EAAA,CACCgF,GAAG,kBACHC,KAAK,MACLvD,MAAOiB,EACPuC,SAAWP,GAAM/B,EAAiB+B,EAAEQ,OAAOzD,OAC3C0D,YAAY,8BACZC,SAAUtC,MAEZ8B,IAACrD,GAASgD,SAEV,gEAGFC,EAAAA,KAACvE,GAAa+E,KAAK,SAASI,SAAUtC,IAAYR,EAAMkB,OACrDe,SAAA,CAAAzB,SAAY3C,EAAe,IAC3B2C,EAAU,eAAiB,wBAI/BtF,GACEoH,EAAAA,IAAAvD,EAAA,CACEkD,SACH/G,IAGDoF,EAAWgB,OAAS,GACnBgB,EAAAA,IAACxE,GACEmE,SAAW3B,EAAA0C,IAAKnC,UACf,OAAA2B,EAAAF,IAACvE,EAAA,CAECE,UAA4B,MAAlB0C,OAAkB,EAAAA,EAAAsC,WAAYpC,EAASoC,QACjDC,QAAS,IA3FQ,CAACrC,IAC5BD,EAAoBC,GACPf,EAAA,CAAEe,aACI,MAAAhB,GAAAA,EAAAgB,IAwFMsC,CAAqBtC,GAEpCoB,gBAAC/D,EACE,CAAA+D,SAAA,CAAApB,EAASuC,UACRZ,EAAAF,IAACnE,EAAA,CACCkF,IAAKxC,EAASuC,SACdE,IAAKzC,EAAStF,KACdgI,QAAUnB,IACNA,EAAAoB,cAAcC,MAAMC,QAAU,iBAIrCrF,EACC,CAAA4D,SAAA,GAACK,IAAAhE,EAAA,CAAc2D,WAAS1G,OACvBsF,EAAS8C,eACPnF,EAAA,CAAiByD,WAAS0B,iBAE5BjF,EACC,CAAAuD,SAAA,CAAAK,MAAC3D,EACE,CAAAsD,SAAAT,EAAYX,EAASY,QAAU,YAEjC,OACE,CAAAQ,SAAA,EAAS,OAAA2B,EAAA/C,EAAAY,aAAQ,EAAAmC,EAAAC,QAAQ,KAAM,YAC/BhD,EAASiD,aAAe,KAAKjD,EAASiD,oCAzB1CjD,EAASoC,aAmCrBnC,GAAqC,IAAtBR,EAAWgB,SAAiBd,IAAYtF,GACrDoH,EAAAA,IAAAtD,EAAA,CAAWiD,SAEZ,qFAIR,CCzYA,MAAM8B,EAAgB7G,EAAOC,GAAA;;;;;EAOvB6G,EAAQ9G,EAAO+G,EAAA;;;;;;EAQfC,EAAchH,EAAOuB,CAAA;;;;;;EA6BpB,SAAS0F,IAKd,cACGJ,EACC,CAAA9B,SAAA,GAAAK,IAAC0B,GAAM/B,SAAkB,yBACzBK,IAAC4B,GAAYjC,SAEb,8FACAK,EAAAA,IAAC8B,GAAoBC,WAAW,kBAC9BpC,eAACrC,EAAwB,CAAAC,iBAXDgB,YAehC,CArC6B3D,EAAOC,GAAA;;;;;;EAQND,EAAOqB,EAAA;;;;;EAORrB,EAAOuB,CAAA;;;;"}