import{j as e}from"./index-ad9d0ad0.js";import{d as i}from"./chunk-69735360.js";import{W as o}from"./chunk-9d331b18.js";import"./chunk-0fa44877.js";const r=i.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
`,n=i.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
`,t=i.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
`,d=i.div`
  padding: 48px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e0e0e0;
`,s=i.p`
  font-size: 14px;
  color: #999999;
  margin: 0;
`;function p(){return e.jsxs(r,{children:[e.jsx(n,{children:"Review Settings"}),e.jsx(t,{children:"Configure how reviews are displayed, filtered, and sorted in your widget."}),e.jsx(o,{widgetStep:"customization",children:e.jsx(d,{children:e.jsx(s,{children:"Review settings component will be implemented in task 10"})})})]})}export{p as ReviewsPage};
//# sourceMappingURL=ReviewsPage-c8984000.js.map
