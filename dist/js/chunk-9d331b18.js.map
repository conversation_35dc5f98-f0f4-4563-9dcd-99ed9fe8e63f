{"version": 3, "file": "chunk-9d331b18.js", "sources": ["../../src/components/ErrorBoundary/WidgetErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport styled from 'styled-components';\n\ninterface Props {\n  children: ReactNode;\n  widgetStep?: string;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  onRetry?: () => void;\n}\n\ninterface State {\n  hasError: boolean;\n  error: Error | null;\n}\n\nconst WidgetErrorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n  padding: 24px;\n  text-align: center;\n  background-color: #fef7f0;\n  border: 1px solid #fed7aa;\n  border-radius: 8px;\n  margin: 16px 0;\n`;\n\nconst ErrorIcon = styled.div`\n  font-size: 32px;\n  color: #ea580c;\n  margin-bottom: 12px;\n`;\n\nconst ErrorTitle = styled.h3`\n  color: #ea580c;\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n`;\n\nconst ErrorMessage = styled.p`\n  color: #9a3412;\n  font-size: 14px;\n  margin: 0 0 16px 0;\n  max-width: 400px;\n  line-height: 1.4;\n`;\n\nconst RetryButton = styled.button`\n  padding: 8px 16px;\n  background-color: #ea580c;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n\n  &:hover {\n    background-color: #dc2626;\n  }\n\n  &:disabled {\n    background-color: #d1d5db;\n    cursor: not-allowed;\n  }\n`;\n\nclass WidgetErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<State> {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({ error });\n\n    // Log widget-specific error context\n    const widgetContext = {\n      step: this.props.widgetStep,\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      timestamp: new Date().toISOString(),\n    };\n\n    console.error('Widget error in step:', this.props.widgetStep, widgetContext);\n\n    // Call custom error handler\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n  }\n\n  private handleRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n    });\n\n    if (this.props.onRetry) {\n      this.props.onRetry();\n    }\n  };\n\n  private getErrorMessage(): string {\n    const step = this.props.widgetStep;\n    const error = this.state.error;\n\n    if (step === 'business-search') {\n      return 'Unable to search for businesses. Please check your internet connection and try again.';\n    }\n    \n    if (step === 'template-selection') {\n      return 'Failed to load widget templates. Please refresh the page and try again.';\n    }\n    \n    if (step === 'customization') {\n      return 'Error loading customization options. Your settings may not be saved properly.';\n    }\n    \n    if (step === 'preview') {\n      return 'Unable to generate widget preview. Please check your configuration and try again.';\n    }\n    \n    if (step === 'embed-code') {\n      return 'Failed to generate embed code. Please verify your widget configuration.';\n    }\n\n    // Generic message with error details if available\n    if (error?.message) {\n      return `An error occurred: ${error.message}`;\n    }\n\n    return 'An unexpected error occurred while creating your widget.';\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <WidgetErrorContainer>\n          <ErrorIcon>⚠️</ErrorIcon>\n          <ErrorTitle>Widget Error</ErrorTitle>\n          <ErrorMessage>{this.getErrorMessage()}</ErrorMessage>\n          <RetryButton onClick={this.handleRetry}>\n            Try Again\n          </RetryButton>\n        </WidgetErrorContainer>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default WidgetErrorBoundary;"], "names": ["WidgetError<PERSON><PERSON><PERSON>", "styled", "div", "ErrorIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h3", "ErrorMessage", "p", "RetryButton", "button", "WidgetErrorBoundary", "Component", "constructor", "props", "super", "__publicField", "this", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "onRetry", "state", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "widgetStep", "message", "stack", "componentStack", "Date", "toISOString", "onError", "getErrorMessage", "step", "render", "children", "jsx", "onClick", "handleRetry"], "mappings": "wRAeA,MAAMA,EAAuBC,EAAOC,GAAA;;;;;;;;;;;;EAc9BC,EAAYF,EAAOC,GAAA;;;;EAMnBE,EAAaH,EAAOI,EAAA;;;;;EAOpBC,EAAeL,EAAOM,CAAA;;;;;;EAQtBC,EAAcP,EAAOQ,MAAA;;;;;;;;;;;;;;;;;;;EAqB3B,MAAMC,UAA4BC,EAAAA,UAChC,WAAAC,CAAYC,GACVC,MAAMD,GAkCAE,EAAAC,KAAA,cAAc,KACpBA,KAAKC,SAAS,CACZC,UAAU,EACVC,MAAO,OAGLH,KAAKH,MAAMO,SACbJ,KAAKH,MAAMO,YAxCbJ,KAAKK,MAAQ,CACXH,UAAU,EACVC,MAAO,KAEX,CAEA,+BAAOG,CAAyBH,GACvB,MAAA,CACLD,UAAU,EACVC,QAEJ,CAEA,iBAAAI,CAAkBJ,EAAcK,GACzBR,KAAAC,SAAS,CAAEE,UAIRH,KAAKH,MAAMY,WACVN,EAAMO,QACNP,EAAMQ,MACGH,EAAUI,gBACf,IAAIC,MAAOC,cAMpBd,KAAKH,MAAMkB,SACRf,KAAAH,MAAMkB,QAAQZ,EAAOK,EAE9B,CAaQ,eAAAQ,GACA,MAAAC,EAAOjB,KAAKH,MAAMY,WAClBN,EAAQH,KAAKK,MAAMF,MAEzB,MAAa,oBAATc,EACK,wFAGI,uBAATA,EACK,0EAGI,kBAATA,EACK,gFAGI,YAATA,EACK,oFAGI,eAATA,EACK,iFAILd,WAAOO,SACF,sBAAsBP,EAAMO,UAG9B,0DACT,CAEA,MAAAQ,GACM,OAAAlB,KAAKK,MAAMH,gBAEVlB,EACC,CAAAmC,SAAA,GAAAC,IAACjC,GAAUgC,SAAE,SACbC,IAAChC,GAAW+B,SAAY,iBACvBC,EAAAA,IAAA9B,EAAA,CAAc6B,SAAKnB,KAAAgB,oBACnBI,EAAAA,IAAA5B,EAAA,CAAY6B,QAASrB,KAAKsB,YAAaH,SAExC,iBAKCnB,KAAKH,MAAMsB,QACpB"}