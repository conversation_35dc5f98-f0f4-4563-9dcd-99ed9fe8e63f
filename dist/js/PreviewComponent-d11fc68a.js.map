{"version": 3, "file": "PreviewComponent-d11fc68a.js", "sources": ["../../src/components/Preview/WidgetRenderer.tsx", "../../src/components/Preview/PreviewComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { WidgetConfig } from '../../types/widget';\n\n// Mock review data for preview\nconst mockReviews = [\n  {\n    id: '1',\n    authorName: '<PERSON>',\n    authorPhotoUrl: 'https://via.placeholder.com/40x40/4285f4/ffffff?text=SJ',\n    rating: 5,\n    text: 'Excellent service and friendly staff. The team went above and beyond to ensure our satisfaction. Highly recommend to anyone looking for quality service!',\n    publishedDate: '2 days ago',\n    isVerified: true,\n  },\n  {\n    id: '2',\n    authorName: '<PERSON>',\n    authorPhotoUrl: 'https://via.placeholder.com/40x40/34a853/ffffff?text=MC',\n    rating: 4,\n    text: 'Great experience overall. Professional service and reasonable pricing. Will definitely come back for future needs.',\n    publishedDate: '1 week ago',\n    isVerified: true,\n  },\n  {\n    id: '3',\n    authorName: '<PERSON>',\n    authorPhotoUrl: 'https://via.placeholder.com/40x40/ea4335/ffffff?text=ED',\n    rating: 5,\n    text: 'Outstanding quality and attention to detail. The results exceeded our expectations.',\n    publishedDate: '2 weeks ago',\n    isVerified: false,\n  },\n  {\n    id: '4',\n    authorName: '<PERSON>',\n    authorPhotoUrl: 'https://via.placeholder.com/40x40/fbbc04/ffffff?text=JW',\n    rating: 5,\n    text: 'Fantastic service from start to finish. Very professional and efficient.',\n    publishedDate: '3 weeks ago',\n    isVerified: true,\n  },\n  {\n    id: '5',\n    authorName: 'Lisa Rodriguez',\n    authorPhotoUrl: 'https://via.placeholder.com/40x40/9c27b0/ffffff?text=LR',\n    rating: 4,\n    text: 'Good value for money and reliable service. Would recommend to others.',\n    publishedDate: '1 month ago',\n    isVerified: true,\n  },\n];\n\n// Base widget container\nconst WidgetContainer = styled.div<{ \n  $config: WidgetConfig; \n  $viewport: string;\n}>`\n  width: ${props => props.$viewport === 'mobile' ? '100%' : props.$config.styling.dimensions.width};\n  max-width: ${props => props.$viewport === 'mobile' ? '320px' : '100%'};\n  height: ${props => props.$config.styling.dimensions.height};\n  background: ${props => props.$config.styling.colors.background};\n  border-radius: ${props => props.$config.styling.dimensions.borderRadius};\n  padding: ${props => props.$config.styling.spacing.padding};\n  margin: ${props => props.$config.styling.spacing.margin};\n  font-family: ${props => props.$config.styling.fonts.family};\n  font-size: ${props => props.$viewport === 'mobile' ? '14px' : props.$config.styling.fonts.size};\n  color: ${props => props.$config.styling.colors.text};\n  border: 1px solid ${props => props.$config.styling.colors.border};\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  position: relative;\n`;\n\n// Carousel Widget\nconst CarouselContainer = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst CarouselHeader = styled.div<{ $config: WidgetConfig }>`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.$config.styling.spacing.gap};\n  padding-bottom: 8px;\n  border-bottom: 1px solid ${props => props.$config.styling.colors.border};\n`;\n\nconst CarouselTitle = styled.h3<{ $config: WidgetConfig }>`\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: ${props => props.$config.styling.colors.text};\n`;\n\nconst CarouselNavigation = styled.div`\n  display: flex;\n  gap: 6px;\n  align-items: center;\n`;\n\nconst NavDot = styled.button<{ $active: boolean; $config: WidgetConfig }>`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  border: none;\n  background: ${props => props.$active ? props.$config.styling.colors.primary : props.$config.styling.colors.border};\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${props => props.$config.styling.colors.primary};\n  }\n`;\n\nconst ReviewCard = styled.div<{ $config: WidgetConfig }>`\n  flex: 1;\n  padding: 12px;\n  border: 1px solid ${props => props.$config.styling.colors.border};\n  border-radius: 8px;\n  background: ${props => props.$config.styling.colors.background};\n`;\n\nconst ReviewHeader = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 8px;\n`;\n\nconst AuthorPhoto = styled.img`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  object-fit: cover;\n`;\n\nconst AuthorInfo = styled.div`\n  flex: 1;\n`;\n\nconst AuthorName = styled.div<{ $config: WidgetConfig }>`\n  font-weight: 600;\n  font-size: 14px;\n  color: ${props => props.$config.styling.colors.text};\n`;\n\nconst ReviewDate = styled.div<{ $config: WidgetConfig }>`\n  font-size: 12px;\n  color: ${props => props.$config.styling.colors.text}80;\n`;\n\nconst Rating = styled.div<{ $config: WidgetConfig }>`\n  color: ${props => props.$config.styling.colors.secondary};\n  font-size: 14px;\n`;\n\nconst ReviewText = styled.p<{ $config: WidgetConfig }>`\n  margin: 0;\n  font-size: 13px;\n  line-height: 1.4;\n  color: ${props => props.$config.styling.colors.text};\n`;\n\n// Badge Widget\nconst BadgeContainer = styled.div<{ $config: WidgetConfig }>`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 16px;\n  background: ${props => props.$config.styling.colors.background};\n  border-radius: 24px;\n  border: 1px solid ${props => props.$config.styling.colors.border};\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  width: fit-content;\n`;\n\nconst BadgeRating = styled.div<{ $config: WidgetConfig }>`\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-weight: 600;\n  color: ${props => props.$config.styling.colors.secondary};\n`;\n\nconst BadgeText = styled.span<{ $config: WidgetConfig }>`\n  color: ${props => props.$config.styling.colors.text};\n  font-size: 14px;\n`;\n\n// Grid Widget\nconst GridContainer = styled.div`\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst GridHeader = styled.div<{ $config: WidgetConfig }>`\n  text-align: center;\n  margin-bottom: ${props => props.$config.styling.spacing.gap};\n  padding-bottom: 12px;\n  border-bottom: 1px solid ${props => props.$config.styling.colors.border};\n`;\n\nconst GridSummary = styled.div<{ $config: WidgetConfig }>`\n  background: ${props => props.$config.styling.colors.border}40;\n  padding: 8px 12px;\n  border-radius: 6px;\n  margin-bottom: ${props => props.$config.styling.spacing.gap};\n  font-size: 12px;\n  color: ${props => props.$config.styling.colors.text};\n  text-align: center;\n`;\n\nconst GridReviews = styled.div<{ $viewport: string }>`\n  display: grid;\n  grid-template-columns: ${props => props.$viewport === 'mobile' ? '1fr' : '1fr 1fr'};\n  gap: 8px;\n  flex: 1;\n`;\n\nconst GridReviewCard = styled.div<{ $config: WidgetConfig }>`\n  padding: 8px;\n  border: 1px solid ${props => props.$config.styling.colors.border};\n  border-radius: 6px;\n  background: ${props => props.$config.styling.colors.background};\n`;\n\n// Utility function to render stars\nconst renderStars = (rating: number) => {\n  return '★'.repeat(rating) + '☆'.repeat(5 - rating);\n};\n\ninterface WidgetRendererProps {\n  config: WidgetConfig;\n  viewport: string;\n}\n\nexport function WidgetRenderer({ config, viewport }: WidgetRendererProps) {\n  const [currentReviewIndex, setCurrentReviewIndex] = React.useState(0);\n  \n  // Filter reviews based on settings\n  const filteredReviews = mockReviews\n    .filter(review => review.rating >= config.settings.minRating)\n    .slice(0, config.settings.maxReviews);\n\n  // Sort reviews based on settings\n  const sortedReviews = [...filteredReviews].sort((a, b) => {\n    switch (config.settings.sortBy) {\n      case 'rating_high':\n        return b.rating - a.rating;\n      case 'rating_low':\n        return a.rating - b.rating;\n      case 'oldest':\n        return new Date(a.publishedDate).getTime() - new Date(b.publishedDate).getTime();\n      case 'newest':\n      default:\n        return new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime();\n    }\n  });\n\n  const currentReview = sortedReviews[currentReviewIndex] || sortedReviews[0];\n  const averageRating = sortedReviews.reduce((sum, review) => sum + review.rating, 0) / sortedReviews.length;\n\n  const renderWidget = () => {\n    switch (config.template) {\n      case 'carousel':\n        return (\n          <CarouselContainer>\n            <CarouselHeader $config={config}>\n              <CarouselTitle $config={config}>\n                {config.business?.name || 'Customer Reviews'}\n              </CarouselTitle>\n              <CarouselNavigation>\n                {sortedReviews.map((_, index) => (\n                  <NavDot\n                    key={index}\n                    $active={index === currentReviewIndex}\n                    $config={config}\n                    onClick={() => setCurrentReviewIndex(index)}\n                  />\n                ))}\n              </CarouselNavigation>\n            </CarouselHeader>\n            {currentReview && (\n              <ReviewCard $config={config}>\n                <ReviewHeader>\n                  {config.settings.showPhotos && (\n                    <AuthorPhoto src={currentReview.authorPhotoUrl} alt={currentReview.authorName} />\n                  )}\n                  <AuthorInfo>\n                    <AuthorName $config={config}>{currentReview.authorName}</AuthorName>\n                    {config.settings.showDates && (\n                      <ReviewDate $config={config}>{currentReview.publishedDate}</ReviewDate>\n                    )}\n                  </AuthorInfo>\n                  <Rating $config={config}>{renderStars(currentReview.rating)}</Rating>\n                </ReviewHeader>\n                <ReviewText $config={config}>{currentReview.text}</ReviewText>\n              </ReviewCard>\n            )}\n          </CarouselContainer>\n        );\n\n      case 'badge':\n        return (\n          <BadgeContainer $config={config}>\n            <BadgeRating $config={config}>\n              <span>★</span>\n              <span>{averageRating.toFixed(1)}</span>\n            </BadgeRating>\n            <BadgeText $config={config}>\n              Based on {sortedReviews.length} reviews\n            </BadgeText>\n          </BadgeContainer>\n        );\n\n      case 'grid':\n        return (\n          <GridContainer>\n            <GridHeader $config={config}>\n              <div style={{ fontSize: '16px', fontWeight: '600', marginBottom: '4px' }}>\n                ★ {averageRating.toFixed(1)} ({sortedReviews.length} reviews)\n              </div>\n              <div style={{ fontSize: '14px', color: config.styling.colors.text + '80' }}>\n                {config.business?.name || 'Business Reviews'}\n              </div>\n            </GridHeader>\n            <GridSummary $config={config}>\n              AI Summary: Customers consistently praise the excellent service and professional staff\n            </GridSummary>\n            <GridReviews $viewport={viewport}>\n              {sortedReviews.slice(0, viewport === 'mobile' ? 2 : 4).map((review) => (\n                <GridReviewCard key={review.id} $config={config}>\n                  <ReviewHeader style={{ marginBottom: '6px' }}>\n                    {config.settings.showPhotos && (\n                      <AuthorPhoto \n                        src={review.authorPhotoUrl} \n                        alt={review.authorName}\n                        style={{ width: '24px', height: '24px' }}\n                      />\n                    )}\n                    <AuthorInfo>\n                      <AuthorName $config={config} style={{ fontSize: '12px' }}>\n                        {review.authorName}\n                      </AuthorName>\n                      {config.settings.showDates && (\n                        <ReviewDate $config={config} style={{ fontSize: '10px' }}>\n                          {review.publishedDate}\n                        </ReviewDate>\n                      )}\n                    </AuthorInfo>\n                    <Rating $config={config} style={{ fontSize: '12px' }}>\n                      {renderStars(review.rating)}\n                    </Rating>\n                  </ReviewHeader>\n                  <ReviewText $config={config} style={{ fontSize: '11px' }}>\n                    {review.text.length > 80 ? review.text.substring(0, 80) + '...' : review.text}\n                  </ReviewText>\n                </GridReviewCard>\n              ))}\n            </GridReviews>\n          </GridContainer>\n        );\n\n      case 'simple-carousel':\n        return (\n          <div style={{ textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n            {currentReview && (\n              <>\n                <AuthorName $config={config} style={{ marginBottom: '8px', fontSize: '16px' }}>\n                  {currentReview.authorName}\n                </AuthorName>\n                <Rating $config={config} style={{ marginBottom: '12px', fontSize: '18px' }}>\n                  {renderStars(currentReview.rating)}\n                </Rating>\n                <ReviewText $config={config} style={{ fontStyle: 'italic', marginBottom: '16px' }}>\n                  \"{currentReview.text}\"\n                </ReviewText>\n                <CarouselNavigation style={{ justifyContent: 'center' }}>\n                  {sortedReviews.map((_, index) => (\n                    <NavDot\n                      key={index}\n                      $active={index === currentReviewIndex}\n                      $config={config}\n                      onClick={() => setCurrentReviewIndex(index)}\n                    />\n                  ))}\n                </CarouselNavigation>\n              </>\n            )}\n          </div>\n        );\n\n      case 'slider':\n        return (\n          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <div style={{ textAlign: 'center', marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>\n              Recent Reviews\n            </div>\n            <div style={{ \n              display: 'flex', \n              gap: '12px', \n              overflowX: 'auto', \n              paddingBottom: '8px',\n              flex: 1\n            }}>\n              {sortedReviews.map((review) => (\n                <div\n                  key={review.id}\n                  style={{\n                    minWidth: viewport === 'mobile' ? '200px' : '150px',\n                    padding: '12px',\n                    border: `1px solid ${config.styling.colors.border}`,\n                    borderRadius: '8px',\n                    background: config.styling.colors.background,\n                  }}\n                >\n                  <AuthorName $config={config} style={{ marginBottom: '4px', fontSize: '12px' }}>\n                    {review.authorName}\n                  </AuthorName>\n                  <Rating $config={config} style={{ marginBottom: '6px', fontSize: '12px' }}>\n                    {renderStars(review.rating)}\n                  </Rating>\n                  <ReviewText $config={config} style={{ fontSize: '10px' }}>\n                    {review.text.substring(0, 60)}...\n                  </ReviewText>\n                </div>\n              ))}\n            </div>\n          </div>\n        );\n\n      case 'floating-badge':\n        return (\n          <div style={{ \n            position: 'relative', \n            width: '100%', \n            height: '100%',\n            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n            borderRadius: config.styling.dimensions.borderRadius,\n            overflow: 'hidden'\n          }}>\n            <div style={{ \n              position: 'absolute', \n              top: '12px', \n              left: '12px', \n              fontSize: '12px', \n              color: '#666',\n              background: 'rgba(255,255,255,0.9)',\n              padding: '4px 8px',\n              borderRadius: '4px'\n            }}>\n              Your Website Content\n            </div>\n            <div style={{\n              position: 'absolute',\n              bottom: '16px',\n              right: '16px',\n              background: config.styling.colors.background,\n              borderRadius: '50%',\n              width: '60px',\n              height: '60px',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n              cursor: 'pointer',\n              transition: 'transform 0.2s ease',\n            }}>\n              <div style={{ \n                color: config.styling.colors.secondary, \n                fontWeight: '600', \n                fontSize: '14px' \n              }}>\n                ★ {averageRating.toFixed(1)}\n              </div>\n              <div style={{ \n                color: config.styling.colors.text, \n                fontSize: '10px' \n              }}>\n                {sortedReviews.length} reviews\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Template not found</div>;\n    }\n  };\n\n  return (\n    <WidgetContainer $config={config} $viewport={viewport}>\n      {renderWidget()}\n    </WidgetContainer>\n  );\n}", "import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useWidget } from '../../context/WidgetContext';\nimport { WidgetRenderer } from './WidgetRenderer';\n\n// Preview container types\ntype ViewportSize = 'desktop' | 'tablet' | 'mobile';\n\ninterface ViewportDimensions {\n  width: number;\n  height: number;\n}\n\nconst viewportSizes: Record<ViewportSize, ViewportDimensions> = {\n  desktop: { width: 1200, height: 800 },\n  tablet: { width: 768, height: 1024 },\n  mobile: { width: 375, height: 667 },\n};\n\n// Styled components\nconst PreviewContainer = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst PreviewHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  gap: 12px;\n`;\n\nconst PreviewTitle = styled.h3`\n  font-size: 18px;\n  font-weight: 600;\n  color: #333333;\n  margin: 0;\n`;\n\nconst PreviewControls = styled.div`\n  display: flex;\n  gap: 8px;\n  align-items: center;\n`;\n\nconst ViewportButton = styled.button<{ $active: boolean }>`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.$active ? '#4285f4' : '#e0e0e0'};\n  background: ${props => props.$active ? '#4285f4' : '#ffffff'};\n  color: ${props => props.$active ? '#ffffff' : '#666666'};\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n\n  &:hover {\n    border-color: #4285f4;\n    background: ${props => props.$active ? '#3367d6' : '#f8f9ff'};\n  }\n\n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);\n  }\n`;\n\nconst PreviewViewport = styled.div<{ \n  $width: number; \n  $height: number; \n  $currentViewport: ViewportSize;\n}>`\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: flex-start;\n  padding: 20px;\n  overflow: auto;\n  \n  /* Create a device frame effect */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    ${props => props.$currentViewport === 'mobile' && `\n      background: linear-gradient(to bottom, \n        #333 0%, #333 20px, \n        transparent 20px, transparent calc(100% - 20px),\n        #333 calc(100% - 20px), #333 100%);\n      border-radius: 20px;\n    `}\n  }\n`;\n\nconst PreviewFrame = styled.div<{ \n  $width: number; \n  $height: number; \n  $viewport: ViewportSize;\n}>`\n  width: ${props => Math.min(props.$width, 800)}px;\n  max-width: 100%;\n  min-height: ${props => Math.min(props.$height * 0.6, 500)}px;\n  background: #ffffff;\n  border-radius: ${props => props.$viewport === 'mobile' ? '20px' : '8px'};\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  position: relative;\n  transition: all 0.3s ease;\n  \n  ${props => props.$viewport === 'mobile' && `\n    border: 8px solid #333333;\n    &::before {\n      content: '';\n      position: absolute;\n      top: -4px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n      z-index: 1;\n    }\n  `}\n  \n  ${props => props.$viewport === 'tablet' && `\n    border: 4px solid #666666;\n    border-radius: 12px;\n  `}\n`;\n\nconst PreviewContent = styled.div`\n  padding: 20px;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #fafafa;\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #666666;\n  font-size: 14px;\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #dc3545;\n  font-size: 14px;\n  text-align: center;\n  \n  &::before {\n    content: '⚠️';\n    font-size: 24px;\n    margin-bottom: 8px;\n  }\n`;\n\n// Icons for viewport buttons\nconst DesktopIcon = () => (\n  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n    <path d=\"M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7l-2 3v1h8v-1l-2-3h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H3V4h18v10z\"/>\n  </svg>\n);\n\nconst TabletIcon = () => (\n  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n    <path d=\"M19 1H5c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 18H5V5h14v14z\"/>\n  </svg>\n);\n\nconst MobileIcon = () => (\n  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n    <path d=\"M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 18H7V5h10v14z\"/>\n  </svg>\n);\n\ninterface PreviewComponentProps {\n  className?: string;\n  showControls?: boolean;\n  defaultViewport?: ViewportSize;\n}\n\nexport function PreviewComponent({ \n  className, \n  showControls = true, \n  defaultViewport = 'desktop' \n}: PreviewComponentProps) {\n  const { config } = useWidget();\n  const [currentViewport, setCurrentViewport] = useState<ViewportSize>(defaultViewport);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Simulate loading when config changes\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n    \n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [config]);\n\n  const handleViewportChange = (viewport: ViewportSize) => {\n    setCurrentViewport(viewport);\n  };\n\n  const renderPreviewContent = () => {\n    if (isLoading) {\n      return <LoadingState>Updating preview...</LoadingState>;\n    }\n\n    if (error) {\n      return <ErrorState>{error}</ErrorState>;\n    }\n\n    if (!config.business) {\n      return (\n        <LoadingState>\n          Select a business to see the widget preview\n        </LoadingState>\n      );\n    }\n\n    return <WidgetRenderer config={config} viewport={currentViewport} />;\n  };\n\n  const currentDimensions = viewportSizes[currentViewport];\n\n  return (\n    <PreviewContainer className={className}>\n      {showControls && (\n        <PreviewHeader>\n          <PreviewTitle>Live Preview</PreviewTitle>\n          <PreviewControls>\n            <ViewportButton\n              $active={currentViewport === 'desktop'}\n              onClick={() => handleViewportChange('desktop')}\n              title=\"Desktop view\"\n            >\n              <DesktopIcon />\n              Desktop\n            </ViewportButton>\n            <ViewportButton\n              $active={currentViewport === 'tablet'}\n              onClick={() => handleViewportChange('tablet')}\n              title=\"Tablet view\"\n            >\n              <TabletIcon />\n              Tablet\n            </ViewportButton>\n            <ViewportButton\n              $active={currentViewport === 'mobile'}\n              onClick={() => handleViewportChange('mobile')}\n              title=\"Mobile view\"\n            >\n              <MobileIcon />\n              Mobile\n            </ViewportButton>\n          </PreviewControls>\n        </PreviewHeader>\n      )}\n      \n      <PreviewViewport\n        $width={currentDimensions.width}\n        $height={currentDimensions.height}\n        $currentViewport={currentViewport}\n      >\n        <PreviewFrame\n          $width={currentDimensions.width}\n          $height={currentDimensions.height}\n          $viewport={currentViewport}\n        >\n          <PreviewContent>\n            {renderPreviewContent()}\n          </PreviewContent>\n        </PreviewFrame>\n      </PreviewViewport>\n    </PreviewContainer>\n  );\n}"], "names": ["mockReviews", "id", "<PERSON><PERSON><PERSON>", "authorPhotoUrl", "rating", "text", "publishedDate", "isVerified", "WidgetContainer", "styled", "div", "props", "$viewport", "$config", "styling", "dimensions", "width", "height", "colors", "background", "borderRadius", "spacing", "padding", "margin", "fonts", "family", "size", "border", "CarouselContainer", "CarouselHeader", "gap", "CarouselTitle", "h3", "CarouselNavigation", "NavDot", "button", "$active", "primary", "ReviewCard", "ReviewHeader", "Author<PERSON><PERSON><PERSON>", "img", "AuthorInfo", "<PERSON><PERSON><PERSON>", "ReviewDate", "Rating", "secondary", "ReviewText", "p", "BadgeContainer", "BadgeRating", "BadgeText", "span", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Grid<PERSON>ummary", "GridReviews", "GridReviewCard", "renderStars", "repeat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "viewport", "currentReviewIndex", "setCurrentReviewIndex", "React", "useState", "sortedReviews", "filter", "review", "settings", "minRating", "slice", "maxReviews", "sort", "a", "b", "sortBy", "Date", "getTime", "currentReview", "averageRating", "reduce", "sum", "length", "children", "template", "jsxs", "_a", "business", "name", "map", "_", "index", "jsxRuntimeExports", "jsx", "onClick", "showPhotos", "src", "alt", "showDates", "toFixed", "style", "fontSize", "fontWeight", "marginBottom", "color", "_b", "substring", "textAlign", "display", "flexDirection", "justifyContent", "Fragment", "fontStyle", "overflowX", "paddingBottom", "flex", "min<PERSON><PERSON><PERSON>", "position", "overflow", "top", "left", "bottom", "right", "alignItems", "boxShadow", "cursor", "transition", "viewportSizes", "desktop", "tablet", "mobile", "PreviewContainer", "PreviewHeader", "PreviewTitle", "PreviewControls", "ViewportButton", "PreviewViewport", "$currentViewport", "PreviewFrame", "Math", "min", "$width", "$height", "PreviewContent", "LoadingState", "ErrorState", "DesktopIcon", "viewBox", "fill", "d", "TabletIcon", "MobileIcon", "PreviewComponent", "className", "showControls", "defaultViewport", "useWidget", "currentViewport", "setCurrentViewport", "isLoading", "setIsLoading", "error", "setError", "useEffect", "timer", "setTimeout", "clearTimeout", "handleViewportChange", "currentDimensions", "title"], "mappings": "sIAKA,MAAMA,EAAc,CAClB,CACEC,GAAI,IACJC,WAAY,gBACZC,eAAgB,0DAChBC,OAAQ,EACRC,KAAM,2JACNC,cAAe,aACfC,YAAY,GAEd,CACEN,GAAI,IACJC,WAAY,YACZC,eAAgB,0DAChBC,OAAQ,EACRC,KAAM,qHACNC,cAAe,aACfC,YAAY,GAEd,CACEN,GAAI,IACJC,WAAY,aACZC,eAAgB,0DAChBC,OAAQ,EACRC,KAAM,sFACNC,cAAe,cACfC,YAAY,GAEd,CACEN,GAAI,IACJC,WAAY,eACZC,eAAgB,0DAChBC,OAAQ,EACRC,KAAM,2EACNC,cAAe,cACfC,YAAY,GAEd,CACEN,GAAI,IACJC,WAAY,iBACZC,eAAgB,0DAChBC,OAAQ,EACRC,KAAM,wEACNC,cAAe,cACfC,YAAY,IAKVC,EAAkBC,EAAOC,GAAA;WAIpBC,GAA6B,WAApBA,EAAMC,UAAyB,OAASD,EAAME,QAAQC,QAAQC,WAAWC;eACrEL,GAAoB,WAApBA,EAAMC,UAAyB,QAAU;YAC5CD,GAAAA,EAAME,QAAQC,QAAQC,WAAWE;gBAC7BN,GAAAA,EAAME,QAAQC,QAAQI,OAAOC;mBAC1BR,GAAAA,EAAME,QAAQC,QAAQC,WAAWK;aACvCT,GAAAA,EAAME,QAAQC,QAAQO,QAAQC;YAC/BX,GAAAA,EAAME,QAAQC,QAAQO,QAAQE;iBACzBZ,GAAAA,EAAME,QAAQC,QAAQU,MAAMC;eACvCd,GAA6B,WAApBA,EAAMC,UAAyB,OAASD,EAAME,QAAQC,QAAQU,MAAME;WACxEf,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;sBAClBM,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;;;;EAOtDC,EAAoBnB,EAAOC,GAAA;;;;EAM3BmB,EAAiBpB,EAAOC,GAAA;;;;mBAIFC,GAAAA,EAAME,QAAQC,QAAQO,QAAQS;;6BAEpBnB,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;EAG7DI,EAAgBtB,EAAOuB,EAAA;;;;WAITrB,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;EAG3C4B,EAAqBxB,EAAOC,GAAA;;;;EAM5BwB,EAASzB,EAAO0B,MAAA;;;;;gBAKGxB,GAAAA,EAAMyB,QAAUzB,EAAME,QAAQC,QAAQI,OAAOmB,QAAU1B,EAAME,QAAQC,QAAQI,OAAOS;;;;;kBAKlFhB,GAAAA,EAAME,QAAQC,QAAQI,OAAOmB;;EAIlDC,EAAa7B,EAAOC,GAAA;;;sBAGKC,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;;gBAEnChB,GAAAA,EAAME,QAAQC,QAAQI,OAAOC;EAGhDoB,EAAe9B,EAAOC,GAAA;;;;;EAOtB8B,EAAc/B,EAAOgC,GAAA;;;;;EAOrBC,EAAajC,EAAOC,GAAA;;EAIpBiC,EAAalC,EAAOC,GAAA;;;WAGNC,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;EAG3CuC,EAAanC,EAAOC,GAAA;;WAENC,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;EAG3CwC,EAASpC,EAAOC,GAAA;WACFC,GAAAA,EAAME,QAAQC,QAAQI,OAAO4B;;EAI3CC,EAAatC,EAAOuC,CAAA;;;;WAINrC,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;EAI3C4C,EAAiBxC,EAAOC,GAAA;;;;;gBAKLC,GAAAA,EAAME,QAAQC,QAAQI,OAAOC;;sBAEvBR,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;;;EAKtDuB,EAAczC,EAAOC,GAAA;;;;;WAKPC,GAAAA,EAAME,QAAQC,QAAQI,OAAO4B;EAG3CK,EAAY1C,EAAO2C,IAAA;WACLzC,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;;EAK3CgD,EAAgB5C,EAAOC,GAAA;;;;EAMvB4C,EAAa7C,EAAOC,GAAA;;mBAEEC,GAAAA,EAAME,QAAQC,QAAQO,QAAQS;;6BAEpBnB,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;EAG7D4B,EAAc9C,EAAOC,GAAA;gBACFC,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;;;mBAG1BhB,GAAAA,EAAME,QAAQC,QAAQO,QAAQS;;WAEtCnB,GAAAA,EAAME,QAAQC,QAAQI,OAAOb;;EAI3CmD,EAAc/C,EAAOC,GAAA;;2BAESC,GAAoB,WAApBA,EAAMC,UAAyB,MAAQ;;;EAKrE6C,EAAiBhD,EAAOC,GAAA;;sBAECC,GAAAA,EAAME,QAAQC,QAAQI,OAAOS;;gBAEnChB,GAAAA,EAAME,QAAQC,QAAQI,OAAOC;EAIhDuC,EAAetD,GACZ,IAAIuD,OAAOvD,GAAU,IAAIuD,OAAO,EAAIvD,GAQtC,SAASwD,GAAeC,OAAEA,EAAQC,SAAAA,IACvC,MAAOC,EAAoBC,GAAyBC,EAAMC,SAAS,GAQ7DC,EAAgB,IALEnE,EACrBoE,OAAOC,GAAUA,EAAOjE,QAAUyD,EAAOS,SAASC,WAClDC,MAAM,EAAGX,EAAOS,SAASG,aAGeC,KAAK,CAACC,EAAGC,KAC1C,OAAAf,EAAOS,SAASO,QACtB,IAAK,cACI,OAAAD,EAAExE,OAASuE,EAAEvE,OACtB,IAAK,aACI,OAAAuE,EAAEvE,OAASwE,EAAExE,OACtB,IAAK,SACH,OAAO,IAAI0E,KAAKH,EAAErE,eAAeyE,UAAY,IAAID,KAAKF,EAAEtE,eAAeyE,UAEzE,QACE,OAAO,IAAID,KAAKF,EAAEtE,eAAeyE,UAAY,IAAID,KAAKH,EAAErE,eAAeyE,aAIvEC,EAAgBb,EAAcJ,IAAuBI,EAAc,GACnEc,EAAgBd,EAAce,OAAO,CAACC,EAAKd,IAAWc,EAAMd,EAAOjE,OAAQ,GAAK+D,EAAciB,oBAwOjG5E,EAAgB,CAAAK,QAASgD,EAAQjD,UAAWkD,EAC1CuB,SAvOgB,cACnB,OAAQxB,EAAOyB,UACb,IAAK,WACH,cACG1D,EACC,CAAAyD,SAAA,GAACE,KAAA1D,EAAA,CAAehB,QAASgD,EACvBwB,SAAA,OAACtD,GAAclB,QAASgD,EACrBwB,UAAO,OAAAG,EAAA3B,EAAA4B,eAAA,EAAAD,EAAUE,OAAQ,2BAE3BzD,EACE,CAAAoD,SAAAlB,EAAcwB,IAAI,CAACC,EAAGC,IACrBC,EAAAC,IAAC7D,EAAA,CAECE,QAASyD,IAAU9B,EACnBlD,QAASgD,EACTmC,QAAS,IAAMhC,EAAsB6B,IAHhCA,SAQZb,GACCc,EAAAP,KAACjD,EAAW,CAAAzB,QAASgD,EACnBwB,SAAA,QAAC9C,EACE,CAAA8C,SAAA,CAAOxB,EAAAS,SAAS2B,YACdF,EAAAA,IAAAvD,EAAA,CAAY0D,IAAKlB,EAAc7E,eAAgBgG,IAAKnB,EAAc9E,oBAEpEwC,EACC,CAAA2C,SAAA,CAAAU,EAAAA,IAACpD,EAAW,CAAA9B,QAASgD,EAASwB,SAAAL,EAAc9E,aAC3C2D,EAAOS,SAAS8B,WACfL,EAAAA,IAACnD,GAAW/B,QAASgD,EAASwB,WAAc/E,yBAG/CuC,EAAO,CAAAhC,QAASgD,EAASwB,SAAY3B,EAAAsB,EAAc5E,aAErD2F,EAAAA,IAAAhD,EAAA,CAAWlC,QAASgD,EAASwB,WAAchF,aAMtD,IAAK,QAED,SAAAkF,KAACtC,EAAe,CAAApC,QAASgD,EACvBwB,SAAA,GAACE,KAAArC,EAAA,CAAYrC,QAASgD,EACpBwB,SAAA,GAAAU,IAAC,QAAKV,SAAC,MACNU,EAAAA,IAAA,OAAA,CAAMV,SAAcJ,EAAAoB,QAAQ,UAE/Bd,KAACpC,EAAU,CAAAtC,QAASgD,EAAQwB,SAAA,CAAA,YAChBlB,EAAciB,OAAO,iBAKvC,IAAK,OACH,cACG/B,EACC,CAAAgC,SAAA,GAACE,KAAAjC,EAAA,CAAWzC,QAASgD,EACnBwB,SAAA,CAACE,EAAAA,KAAA,MAAA,CAAIe,MAAO,CAAEC,SAAU,OAAQC,WAAY,MAAOC,aAAc,OAASpB,SAAA,CAAA,KACrEJ,EAAcoB,QAAQ,GAAG,KAAGlC,EAAciB,OAAO,qBAErD,MAAI,CAAAkB,MAAO,CAAEC,SAAU,OAAQG,MAAO7C,EAAO/C,QAAQI,OAAOb,KAAO,MACjEgF,UAAO,OAAAsB,IAAAlB,eAAA,EAAAkB,EAAUjB,OAAQ,wBAG7BK,EAAAA,IAAAxC,EAAA,CAAY1C,QAASgD,EAAQwB,SAE9B,iGACC7B,EAAY,CAAA5C,UAAWkD,EACrBuB,SAAclB,EAAAK,MAAM,EAAgB,WAAbV,EAAwB,EAAI,GAAG6B,IAAKtB,GACzDkB,OAAA9B,EAAA,CAA+B5C,QAASgD,EACvCwB,SAAA,CAAAE,EAAAA,KAAChD,EAAa,CAAA+D,MAAO,CAAEG,aAAc,OAClCpB,SAAA,CAAAxB,EAAOS,SAAS2B,YACfH,EAAAC,IAACvD,EAAA,CACC0D,IAAK7B,EAAOlE,eACZgG,IAAK9B,EAAOnE,WACZoG,MAAO,CAAEtF,MAAO,OAAQC,OAAQ,iBAGnCyB,EACC,CAAA2C,SAAA,CAACU,EAAAA,IAAApD,EAAA,CAAW9B,QAASgD,EAAQyC,MAAO,CAAEC,SAAU,QAC7ClB,SAAAhB,EAAOnE,aAET2D,EAAOS,SAAS8B,WACfN,EAAAC,IAACnD,EAAW,CAAA/B,QAASgD,EAAQyC,MAAO,CAAEC,SAAU,QAC7ClB,WAAO/E,qBAIbyF,IAAAlD,EAAA,CAAOhC,QAASgD,EAAQyC,MAAO,CAAEC,SAAU,QACzClB,SAAA3B,EAAYW,EAAOjE,aAGxB2F,EAAAA,IAAChD,GAAWlC,QAASgD,EAAQyC,MAAO,CAAEC,SAAU,QAC7ClB,SAAAhB,EAAOhE,KAAK+E,OAAS,GAAKf,EAAOhE,KAAKuG,UAAU,EAAG,IAAM,MAAQvC,EAAOhE,SAxBxDgE,EAAOpE,UAgCtC,IAAK,+BAEA,MAAI,CAAAqG,MAAO,CAAEO,UAAW,SAAU5F,OAAQ,OAAQ6F,QAAS,OAAQC,cAAe,SAAUC,eAAgB,UAC1G3B,cAEGE,KAAA0B,WAAA,CAAA5B,SAAA,GAACU,IAAApD,EAAA,CAAW9B,QAASgD,EAAQyC,MAAO,CAAEG,aAAc,MAAOF,SAAU,QAClElB,SAAAL,EAAc9E,eAEhB6F,IAAAlD,EAAA,CAAOhC,QAASgD,EAAQyC,MAAO,CAAEG,aAAc,OAAQF,SAAU,QAC/DlB,SAAY3B,EAAAsB,EAAc5E,UAE7BmF,EAAAA,KAACxC,EAAW,CAAAlC,QAASgD,EAAQyC,MAAO,CAAEY,UAAW,SAAUT,aAAc,QAAUpB,SAAA,CAAA,IAC/EL,EAAc3E,KAAK,OAEvB0F,EAAAA,IAAC9D,EAAmB,CAAAqE,MAAO,CAAEU,eAAgB,UAC1C3B,SAAclB,EAAAwB,IAAI,CAACC,EAAGC,IACrBC,EAAAC,IAAC7D,EAAA,CAECE,QAASyD,IAAU9B,EACnBlD,QAASgD,EACTmC,QAAS,IAAMhC,EAAsB6B,IAHhCA,WAYrB,IAAK,SAED,cAAC,MAAI,CAAAS,MAAO,CAAErF,OAAQ,OAAQ6F,QAAS,OAAQC,cAAe,UAC5D1B,SAAA,GAAAU,IAAC,MAAI,CAAAO,MAAO,CAAEO,UAAW,SAAUJ,aAAc,OAAQF,SAAU,OAAQC,WAAY,OAASnB,SAEhG,yBACC,OAAIiB,MAAO,CACVQ,QAAS,OACThF,IAAK,OACLqF,UAAW,OACXC,cAAe,MACfC,KAAM,GAELhC,SAAAlB,EAAcwB,IAAKtB,GAClByB,EAAAP,KAAC,MAAA,CAECe,MAAO,CACLgB,SAAuB,WAAbxD,EAAwB,QAAU,QAC5CxC,QAAS,OACTK,OAAQ,aAAakC,EAAO/C,QAAQI,OAAOS,SAC3CP,aAAc,MACdD,WAAY0C,EAAO/C,QAAQI,OAAOC,YAGpCkE,SAAA,GAACU,IAAApD,EAAA,CAAW9B,QAASgD,EAAQyC,MAAO,CAAEG,aAAc,MAAOF,SAAU,QAClElB,SAAAhB,EAAOnE,eAET6F,IAAAlD,EAAA,CAAOhC,QAASgD,EAAQyC,MAAO,CAAEG,aAAc,MAAOF,SAAU,QAC9DlB,SAAY3B,EAAAW,EAAOjE,UAEtBmF,OAACxC,GAAWlC,QAASgD,EAAQyC,MAAO,CAAEC,SAAU,QAC7ClB,SAAA,CAAOhB,EAAAhE,KAAKuG,UAAU,EAAG,IAAI,WAhB3BvC,EAAOpE,UAwBxB,IAAK,iBAED,OAAA6F,EAAAP,KAAC,OAAIe,MAAO,CACViB,SAAU,WACVvG,MAAO,OACPC,OAAQ,OACRE,WAAY,oDACZC,aAAcyC,EAAO/C,QAAQC,WAAWK,aACxCoG,SAAU,UAEVnC,SAAA,OAAC,OAAIiB,MAAO,CACViB,SAAU,WACVE,IAAK,OACLC,KAAM,OACNnB,SAAU,OACVG,MAAO,OACPvF,WAAY,wBACZG,QAAS,UACTF,aAAc,OACbiE,SAEH,gCACC,OAAIiB,MAAO,CACViB,SAAU,WACVI,OAAQ,OACRC,MAAO,OACPzG,WAAY0C,EAAO/C,QAAQI,OAAOC,WAClCC,aAAc,MACdJ,MAAO,OACPC,OAAQ,OACR6F,QAAS,OACTC,cAAe,SACfc,WAAY,SACZb,eAAgB,SAChBc,UAAW,iCACXC,OAAQ,UACRC,WAAY,uBAEZ3C,SAAA,QAAC,OAAIiB,MAAO,CACVI,MAAO7C,EAAO/C,QAAQI,OAAO4B,UAC7B0D,WAAY,MACZD,SAAU,QACTlB,SAAA,CAAA,KACEJ,EAAcoB,QAAQ,aAE1B,OAAIC,MAAO,CACVI,MAAO7C,EAAO/C,QAAQI,OAAOb,KAC7BkG,SAAU,QAETlB,SAAA,CAAclB,EAAAiB,OAAO,oBAMhC,QACS,SAAAW,IAAC,OAAIV,SAAkB,6BAStC,CCveA,MAAM4C,EAA0D,CAC9DC,QAAS,CAAElH,MAAO,KAAMC,OAAQ,KAChCkH,OAAQ,CAAEnH,MAAO,IAAKC,OAAQ,MAC9BmH,OAAQ,CAAEpH,MAAO,IAAKC,OAAQ,MAI1BoH,EAAmB5H,EAAOC,GAAA;;;;;;;;EAU1B4H,EAAgB7H,EAAOC,GAAA;;;;;;;EASvB6H,EAAe9H,EAAOuB,EAAA;;;;;EAOtBwG,EAAkB/H,EAAOC,GAAA;;;;EAMzB+H,EAAiBhI,EAAO0B,MAAA;;sBAECxB,GAAAA,EAAMyB,QAAU,UAAY;gBAClCzB,GAAAA,EAAMyB,QAAU,UAAY;WACjCzB,GAAAA,EAAMyB,QAAU,UAAY;;;;;;;;;;;;kBAYrBzB,GAAAA,EAAMyB,QAAU,UAAY;;;;;;;EASjDsG,EAAkBjI,EAAOC,GAAA;;;;;;;;;;;;;;;;;MAqBzBC,GAAoC,WAA3BA,EAAMgI,kBAAiC;;EAUhDC,EAAenI,EAAOC,GAAA;cAKRmI,KAAKC,IAAInI,EAAMoI,OAAQ;;mBAElBF,KAAKC,IAAoB,GAAhBnI,EAAMqI,QAAe;;mBAE3BrI,GAAoB,WAApBA,EAAMC,UAAyB,OAAS;;;;;;IAMhED,GAA6B,WAApBA,EAAMC,WAA0B;;IAgBzCD,GAA6B,WAApBA,EAAMC,WAA0B;EAMvCqI,EAAiBxI,EAAOC,GAAA;;;;;;;EASxBwI,EAAezI,EAAOC,GAAA;;;;;;;EAStByI,EAAa1I,EAAOC,GAAA;;;;;;;;;;;;;;;EAkBpB0I,EAAc,IAClBtD,EAAAC,IAAC,MAAI,CAAA/E,MAAM,KAAKC,OAAO,KAAKoI,QAAQ,YAAYC,KAAK,eACnDjE,SAAAU,MAAC,OAAK,CAAAwD,EAAE,qHAINC,EAAa,IACjB1D,EAAAC,IAAC,MAAI,CAAA/E,MAAM,KAAKC,OAAO,KAAKoI,QAAQ,YAAYC,KAAK,eACnDjE,SAAAU,MAAC,OAAK,CAAAwD,EAAE,mGAINE,EAAa,IACjB3D,EAAAC,IAAC,MAAI,CAAA/E,MAAM,KAAKC,OAAO,KAAKoI,QAAQ,YAAYC,KAAK,eACnDjE,SAAAU,MAAC,OAAK,CAAAwD,EAAE,mGAUL,SAASG,GAAiBC,UAC/BA,EAAAC,aACAA,GAAe,EAAAC,gBACfA,EAAkB,YAEZ,MAAAhG,OAAEA,GAAWiG,KACZC,EAAiBC,GAAsB9F,WAAuB2F,IAC9DI,EAAWC,GAAgBhG,YAAS,IACpCiG,EAAOC,GAAYlG,WAAwB,MAGlDmG,EAAAA,UAAU,KACRH,GAAa,GACbE,EAAS,MAEH,MAAAE,EAAQC,WAAW,KACvBL,GAAa,IACZ,KAEI,MAAA,IAAMM,aAAaF,IACzB,CAACzG,IAEE,MAAA4G,EAAwB3G,IAC5BkG,EAAmBlG,IAuBf4G,EAAoBzC,EAAc8B,GAGtC,cAAC1B,GAAiBsB,YACftE,SAAA,CAAAuE,UACEtB,EACC,CAAAjD,SAAA,GAAAU,IAACwC,GAAalD,SAAY,wBACzBmD,EACC,CAAAnD,SAAA,CAAAS,EAAAP,KAACkD,EAAA,CACCrG,QAA6B,YAApB2H,EACT/D,QAAS,IAAMyE,EAAqB,WACpCE,MAAM,eAENtF,SAAA,CAAAS,EAAAC,IAACqD,EAAY,IAAE,aAGjBtD,EAAAP,KAACkD,EAAA,CACCrG,QAA6B,WAApB2H,EACT/D,QAAS,IAAMyE,EAAqB,UACpCE,MAAM,cAENtF,SAAA,CAAAS,EAAAC,IAACyD,EAAW,IAAE,YAGhB1D,EAAAP,KAACkD,EAAA,CACCrG,QAA6B,WAApB2H,EACT/D,QAAS,IAAMyE,EAAqB,UACpCE,MAAM,cAENtF,SAAA,CAAAS,EAAAC,IAAC0D,EAAW,IAAE,kBAOtB3D,EAAAC,IAAC2C,EAAA,CACCK,OAAQ2B,EAAkB1J,MAC1BgI,QAAS0B,EAAkBzJ,OAC3B0H,iBAAkBoB,EAElB1E,SAAAS,EAAAC,IAAC6C,EAAA,CACCG,OAAQ2B,EAAkB1J,MAC1BgI,QAAS0B,EAAkBzJ,OAC3BL,UAAWmJ,EAEX1E,WAAAU,IAACkD,EACE,CAAA5D,SAlEL4E,IACKlE,IAACmD,GAAa7D,SAAmB,wBAGtC8E,IACKpE,IAACoD,GAAY9D,SAAM8E,IAGvBtG,EAAO4B,WAQJM,IAAAnC,EAAA,CAAeC,SAAgBC,SAAUiG,MAN7ChE,IAACmD,GAAa7D,SAEd,wDA4DR"}