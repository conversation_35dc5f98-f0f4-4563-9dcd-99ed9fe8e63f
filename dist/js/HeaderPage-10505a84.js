import{j as e}from"./index-ad9d0ad0.js";import{d as o}from"./chunk-69735360.js";import{W as r}from"./chunk-9d331b18.js";import"./chunk-0fa44877.js";const i=o.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
`,n=o.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
`,t=o.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
`,d=o.div`
  padding: 48px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e0e0e0;
`,a=o.p`
  font-size: 14px;
  color: #999999;
  margin: 0;
`;function p(){return e.jsxs(i,{children:[e.jsx(n,{children:"Configure Header"}),e.jsx(t,{children:"Customize the header text and appearance for your reviews widget."}),e.jsx(r,{widgetStep:"customization",children:e.jsx(d,{children:e.jsx(a,{children:"Header configuration component will be implemented in task 10"})})})]})}export{p as HeaderPage};
//# sourceMappingURL=HeaderPage-10505a84.js.map
