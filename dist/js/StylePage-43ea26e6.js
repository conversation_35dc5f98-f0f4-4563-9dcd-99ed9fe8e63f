import{u as e,j as i}from"./index-ad9d0ad0.js";import{d as n}from"./chunk-69735360.js";import{R as s}from"./chunk-0fa44877.js";import{o as r,r as o,a as t,b as l,s as d,e as a,n as c,c as h,u as x,C as g}from"./chunk-9ab7d155.js";import{PreviewComponent as m}from"./PreviewComponent-d11fc68a.js";import{W as p}from"./chunk-9d331b18.js";var u=function(e,i){for(var n={};e.length;){var s=e[0],r=s.code,o=s.message,l=s.path.join(".");if(!n[l])if("unionErrors"in s){var d=s.unionErrors[0].errors[0];n[l]={message:d.message,type:d.code}}else n[l]={message:o,type:r};if("unionErrors"in s&&s.unionErrors.forEach(function(i){return i.errors.forEach(function(i){return e.push(i)})}),i){var a=n[l].types,c=a&&a[s.code];n[l]=t(l,i,n,r,c?[].concat(c,s.message):s.message)}e.shift()}return n};const j=l({styling:l({colors:l({primary:d().regex(/^#[0-9A-Fa-f]{6}$/,"Invalid hex color"),secondary:d().regex(/^#[0-9A-Fa-f]{6}$/,"Invalid hex color"),background:d().regex(/^#[0-9A-Fa-f]{6}$/,"Invalid hex color"),text:d().regex(/^#[0-9A-Fa-f]{6}$/,"Invalid hex color"),border:d().regex(/^#[0-9A-Fa-f]{6}$/,"Invalid hex color")}),fonts:l({family:d().min(1,"Font family is required"),size:d().regex(/^\d+(px|rem|em)$/,"Invalid font size format"),weight:a(["300","400","500","600","700"])}),dimensions:l({width:d().regex(/^\d+(px|%|rem|em)$/,"Invalid width format"),height:d().regex(/^\d+(px|%|rem|em)$/,"Invalid height format"),borderRadius:d().regex(/^\d+(px|rem|em)$/,"Invalid border radius format")}),spacing:l({padding:d().regex(/^\d+(px|rem|em)$/,"Invalid padding format"),margin:d().regex(/^\d+(px|rem|em)$/,"Invalid margin format"),gap:d().regex(/^\d+(px|rem|em)$/,"Invalid gap format")})}),settings:l({maxReviews:c().min(1,"Must show at least 1 review").max(20,"Cannot show more than 20 reviews"),minRating:c().min(1,"Minimum rating must be at least 1").max(5,"Maximum rating cannot exceed 5"),sortBy:a(["newest","oldest","rating_high","rating_low"]),showPhotos:h(),showDates:h(),autoRefresh:h()})}),f=n.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
`,v=n.div`
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`,y=n.h3`
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 16px 0;
`,w=n.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`,b=n.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`,F=n.label`
  font-size: 14px;
  font-weight: 500;
  color: #555555;
`,R=n.input`
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }

  &:invalid {
    border-color: #ea4335;
  }
`,k=n.select`
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }
`,C=n.input`
  width: 50px;
  height: 35px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  padding: 0;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  &::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
  }
`,S=n.div`
  display: flex;
  align-items: center;
  gap: 8px;
`,z=n.input`
  width: 16px;
  height: 16px;
  cursor: pointer;
`,I=n.span`
  font-size: 12px;
  color: #ea4335;
  margin-top: 4px;
`,$=n.div`
  position: fixed;
  top: 20px;
  right: 20px;
  background: #4285f4;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
`;function A(){var n,t,l,d,a,c,h,m,p,A,P,B,M,E,N,T,D,H,W,_,G,O,V,L,U,q,J,K,Q,X,Y;const{config:Z,updateConfig:ee}=e(),{control:ie,handleSubmit:ne,watch:se,formState:{errors:re}}=x({resolver:(oe=j,void 0===le&&(le={}),function(e,i,n){try{return Promise.resolve(function(i,s){try{var o=Promise.resolve(oe["sync"===le.mode?"parse":"parseAsync"](e,te)).then(function(i){return n.shouldUseNativeValidation&&r({},n),{errors:{},values:le.raw?e:i}})}catch(t){return s(t)}return o&&o.then?o.then(void 0,s):o}(0,function(e){if(i=e,Array.isArray(null==i?void 0:i.errors))return{values:{},errors:o(u(e.errors,!n.shouldUseNativeValidation&&"all"===n.criteriaMode),n)};var i;throw e}))}catch(s){return Promise.reject(s)}}),defaultValues:{styling:{...Z.styling,fonts:{...Z.styling.fonts,weight:Z.styling.fonts.weight}},settings:Z.settings},mode:"onChange"});var oe,te,le;const de=se();s.useEffect(()=>{de.styling&&de.settings&&ee({styling:de.styling,settings:de.settings})},[de,ee]);return i.jsxs(f,{children:[i.jsx($,{children:"Preview updates in real-time"}),i.jsxs("form",{onSubmit:ne(e=>{ee({styling:e.styling,settings:e.settings})}),children:[i.jsxs(v,{children:[i.jsx(y,{children:"Colors"}),i.jsxs(w,{children:[i.jsxs(b,{children:[i.jsx(F,{htmlFor:"primary-color",children:"Primary Color"}),i.jsx(g,{name:"styling.colors.primary",control:ie,render:({field:e})=>i.jsx(C,{...e,type:"color",id:"primary-color"})}),(null==(t=null==(n=re.styling)?void 0:n.colors)?void 0:t.primary)&&i.jsx(I,{children:re.styling.colors.primary.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"secondary-color",children:"Secondary Color"}),i.jsx(g,{name:"styling.colors.secondary",control:ie,render:({field:e})=>i.jsx(C,{...e,type:"color",id:"secondary-color"})}),(null==(d=null==(l=re.styling)?void 0:l.colors)?void 0:d.secondary)&&i.jsx(I,{children:re.styling.colors.secondary.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"background-color",children:"Background Color"}),i.jsx(g,{name:"styling.colors.background",control:ie,render:({field:e})=>i.jsx(C,{...e,type:"color",id:"background-color"})}),(null==(c=null==(a=re.styling)?void 0:a.colors)?void 0:c.background)&&i.jsx(I,{children:re.styling.colors.background.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"text-color",children:"Text Color"}),i.jsx(g,{name:"styling.colors.text",control:ie,render:({field:e})=>i.jsx(C,{...e,type:"color",id:"text-color"})}),(null==(m=null==(h=re.styling)?void 0:h.colors)?void 0:m.text)&&i.jsx(I,{children:re.styling.colors.text.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"border-color",children:"Border Color"}),i.jsx(g,{name:"styling.colors.border",control:ie,render:({field:e})=>i.jsx(C,{...e,type:"color",id:"border-color"})}),(null==(A=null==(p=re.styling)?void 0:p.colors)?void 0:A.border)&&i.jsx(I,{children:re.styling.colors.border.message})]})]})]}),i.jsxs(v,{children:[i.jsx(y,{children:"Typography"}),i.jsxs(w,{children:[i.jsxs(b,{children:[i.jsx(F,{htmlFor:"font-family",children:"Font Family"}),i.jsx(g,{name:"styling.fonts.family",control:ie,render:({field:e})=>i.jsxs(k,{...e,id:"font-family",children:[i.jsx("option",{value:"Inter, sans-serif",children:"Inter"}),i.jsx("option",{value:"Arial, sans-serif",children:"Arial"}),i.jsx("option",{value:"Helvetica, sans-serif",children:"Helvetica"}),i.jsx("option",{value:"Georgia, serif",children:"Georgia"}),i.jsx("option",{value:"Times New Roman, serif",children:"Times New Roman"}),i.jsx("option",{value:"Roboto, sans-serif",children:"Roboto"}),i.jsx("option",{value:"Open Sans, sans-serif",children:"Open Sans"})]})}),(null==(B=null==(P=re.styling)?void 0:P.fonts)?void 0:B.family)&&i.jsx(I,{children:re.styling.fonts.family.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"font-size",children:"Font Size"}),i.jsx(g,{name:"styling.fonts.size",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"font-size",placeholder:"14px"})}),(null==(E=null==(M=re.styling)?void 0:M.fonts)?void 0:E.size)&&i.jsx(I,{children:re.styling.fonts.size.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"font-weight",children:"Font Weight"}),i.jsx(g,{name:"styling.fonts.weight",control:ie,render:({field:e})=>i.jsxs(k,{...e,id:"font-weight",children:[i.jsx("option",{value:"300",children:"Light (300)"}),i.jsx("option",{value:"400",children:"Regular (400)"}),i.jsx("option",{value:"500",children:"Medium (500)"}),i.jsx("option",{value:"600",children:"Semi Bold (600)"}),i.jsx("option",{value:"700",children:"Bold (700)"})]})}),(null==(T=null==(N=re.styling)?void 0:N.fonts)?void 0:T.weight)&&i.jsx(I,{children:re.styling.fonts.weight.message})]})]})]}),i.jsxs(v,{children:[i.jsx(y,{children:"Dimensions"}),i.jsxs(w,{children:[i.jsxs(b,{children:[i.jsx(F,{htmlFor:"width",children:"Width"}),i.jsx(g,{name:"styling.dimensions.width",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"width",placeholder:"400px"})}),(null==(H=null==(D=re.styling)?void 0:D.dimensions)?void 0:H.width)&&i.jsx(I,{children:re.styling.dimensions.width.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"height",children:"Height"}),i.jsx(g,{name:"styling.dimensions.height",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"height",placeholder:"300px"})}),(null==(_=null==(W=re.styling)?void 0:W.dimensions)?void 0:_.height)&&i.jsx(I,{children:re.styling.dimensions.height.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"border-radius",children:"Border Radius"}),i.jsx(g,{name:"styling.dimensions.borderRadius",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"border-radius",placeholder:"8px"})}),(null==(O=null==(G=re.styling)?void 0:G.dimensions)?void 0:O.borderRadius)&&i.jsx(I,{children:re.styling.dimensions.borderRadius.message})]})]})]}),i.jsxs(v,{children:[i.jsx(y,{children:"Spacing"}),i.jsxs(w,{children:[i.jsxs(b,{children:[i.jsx(F,{htmlFor:"padding",children:"Padding"}),i.jsx(g,{name:"styling.spacing.padding",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"padding",placeholder:"16px"})}),(null==(L=null==(V=re.styling)?void 0:V.spacing)?void 0:L.padding)&&i.jsx(I,{children:re.styling.spacing.padding.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"margin",children:"Margin"}),i.jsx(g,{name:"styling.spacing.margin",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"margin",placeholder:"0px"})}),(null==(q=null==(U=re.styling)?void 0:U.spacing)?void 0:q.margin)&&i.jsx(I,{children:re.styling.spacing.margin.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"gap",children:"Gap"}),i.jsx(g,{name:"styling.spacing.gap",control:ie,render:({field:e})=>i.jsx(R,{...e,type:"text",id:"gap",placeholder:"12px"})}),(null==(K=null==(J=re.styling)?void 0:J.spacing)?void 0:K.gap)&&i.jsx(I,{children:re.styling.spacing.gap.message})]})]})]}),i.jsxs(v,{children:[i.jsx(y,{children:"Widget Settings"}),i.jsxs(w,{children:[i.jsxs(b,{children:[i.jsx(F,{htmlFor:"max-reviews",children:"Maximum Reviews"}),i.jsx(g,{name:"settings.maxReviews",control:ie,render:({field:{onChange:e,value:n,...s}})=>i.jsx(R,{...s,type:"number",id:"max-reviews",min:"1",max:"20",value:n,onChange:i=>e(parseInt(i.target.value,10))})}),(null==(Q=re.settings)?void 0:Q.maxReviews)&&i.jsx(I,{children:re.settings.maxReviews.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"min-rating",children:"Minimum Rating"}),i.jsx(g,{name:"settings.minRating",control:ie,render:({field:{onChange:e,value:n,...s}})=>i.jsxs(k,{...s,id:"min-rating",value:n,onChange:i=>e(parseInt(i.target.value,10)),children:[i.jsx("option",{value:1,children:"1 Star"}),i.jsx("option",{value:2,children:"2 Stars"}),i.jsx("option",{value:3,children:"3 Stars"}),i.jsx("option",{value:4,children:"4 Stars"}),i.jsx("option",{value:5,children:"5 Stars"})]})}),(null==(X=re.settings)?void 0:X.minRating)&&i.jsx(I,{children:re.settings.minRating.message})]}),i.jsxs(b,{children:[i.jsx(F,{htmlFor:"sort-by",children:"Sort Reviews By"}),i.jsx(g,{name:"settings.sortBy",control:ie,render:({field:e})=>i.jsxs(k,{...e,id:"sort-by",children:[i.jsx("option",{value:"newest",children:"Newest First"}),i.jsx("option",{value:"oldest",children:"Oldest First"}),i.jsx("option",{value:"rating_high",children:"Highest Rating"}),i.jsx("option",{value:"rating_low",children:"Lowest Rating"})]})}),(null==(Y=re.settings)?void 0:Y.sortBy)&&i.jsx(I,{children:re.settings.sortBy.message})]})]}),i.jsxs(w,{style:{marginTop:"16px"},children:[i.jsx(b,{children:i.jsxs(S,{children:[i.jsx(g,{name:"settings.showPhotos",control:ie,render:({field:{onChange:e,value:n,...s}})=>i.jsx(z,{...s,type:"checkbox",id:"show-photos",checked:n,onChange:i=>e(i.target.checked)})}),i.jsx(F,{htmlFor:"show-photos",children:"Show Review Photos"})]})}),i.jsx(b,{children:i.jsxs(S,{children:[i.jsx(g,{name:"settings.showDates",control:ie,render:({field:{onChange:e,value:n,...s}})=>i.jsx(z,{...s,type:"checkbox",id:"show-dates",checked:n,onChange:i=>e(i.target.checked)})}),i.jsx(F,{htmlFor:"show-dates",children:"Show Review Dates"})]})}),i.jsx(b,{children:i.jsxs(S,{children:[i.jsx(g,{name:"settings.autoRefresh",control:ie,render:({field:{onChange:e,value:n,...s}})=>i.jsx(z,{...s,type:"checkbox",id:"auto-refresh",checked:n,onChange:i=>e(i.target.checked)})}),i.jsx(F,{htmlFor:"auto-refresh",children:"Auto-refresh Reviews"})]})})]})]})]})]})}const P=n.div`
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 32px 16px;
`,B=n.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`,M=n.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`,E=n.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
`,N=n.div`
  min-height: 600px;
`;function T(){return i.jsxs(P,{children:[i.jsx(B,{children:"Customize Style"}),i.jsx(M,{children:"Personalize colors, fonts, spacing, and other visual elements to match your brand. Changes are applied in real-time to help you see exactly how your widget will look."}),i.jsxs(E,{children:[i.jsx(p,{widgetStep:"customization",children:i.jsx(A,{})}),i.jsx(N,{children:i.jsx(p,{widgetStep:"preview",children:i.jsx(m,{})})})]})]})}export{T as StylePage};
//# sourceMappingURL=StylePage-43ea26e6.js.map
