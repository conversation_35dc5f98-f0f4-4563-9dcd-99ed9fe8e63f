# Google Reviews Widget Generator

A modern, responsive web application for generating embeddable Google Reviews widgets. Built with React, TypeScript, and Node.js.

## Features

- 🔍 **Business Search**: Find businesses using Google Places API
- 🎨 **Customizable Widgets**: Multiple templates and styling options
- 📱 **Responsive Design**: Works on all devices
- ⚡ **Fast Performance**: Optimized for speed and SEO
- 🔧 **Easy Integration**: Simple embed codes for any website

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Google Places API key

### Installation

1. Clone the repository:
```bash
git clone https://github.com/ajnalder/google-review-2.git
cd google-review-2
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Add your Google Places API key to .env
```

4. Start development servers:
```bash
npm run dev
```

## Project Structure

```
├── packages/
│   ├── frontend/          # React frontend application
│   │   ├── src/          # Source code
│   │   ├── api/          # Vercel API functions
│   │   └── public/       # Static assets
│   ├── backend/          # Node.js backend (optional)
│   └── widget-runtime/   # Widget embed script
├── public/               # Shared static files
└── scripts/             # Build and deployment scripts
```

## Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables

- `GOOGLE_PLACES_API_KEY` - Your Google Places API key
- `VITE_API_BASE_URL` - API base URL (default: `/api`)

## API Endpoints

- `GET /api/business/search` - Search for businesses
- `GET /api/reviews/[businessId]` - Get reviews for a business
- `GET /api/widget-config/[id]` - Get widget configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

MIT License - see LICENSE file for details
