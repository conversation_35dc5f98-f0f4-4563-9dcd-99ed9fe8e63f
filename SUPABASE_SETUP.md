# Supabase Setup Guide

This guide will help you set up Supabase as the database for your Google Reviews Widget Generator.

## Why Supabase?

✅ **PostgreSQL-based** - Works with our existing Prisma schema
✅ **Managed service** - No database maintenance required
✅ **Built-in APIs** - REST and GraphQL endpoints
✅ **Real-time features** - Live updates for widgets
✅ **Free tier** - 500MB database, 2GB bandwidth
✅ **Easy scaling** - Upgrade as you grow
✅ **Built-in auth** - Ready for user management features

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up or log in
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: `google-reviews-widget`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your users
6. Click "Create new project"
7. Wait for setup to complete (2-3 minutes)

## Step 2: Get Your Connection Details

Once your project is ready:

1. Go to **Settings** → **Database**
2. Copy the connection details:

```env
# Your Supabase connection string will look like:
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# Also get these from Settings → API:
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Step 3: Update Environment Variables

### For Development (.env)
```env
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### For Production (.env.production)
```env
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### For GitHub Secrets
Add these to your GitHub repository secrets:
- `DATABASE_URL`
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

## Step 4: Run Database Migrations

With your Supabase connection configured, run the Prisma migrations:

```bash
# Generate Prisma client
cd packages/backend
npm run db:generate

# Run migrations to create tables
npm run db:migrate
```

## Step 5: Verify Setup

1. Check your Supabase dashboard
2. Go to **Table Editor**
3. You should see these tables:
   - `businesses`
   - `widget_configs`
   - `reviews_cache`

## Step 6: Optional Supabase Features

### Real-time Subscriptions
Enable real-time for live widget updates:

1. Go to **Database** → **Replication**
2. Enable replication for your tables
3. Use Supabase client for real-time features

### Row Level Security (RLS)
For future user authentication:

1. Go to **Authentication** → **Policies**
2. Enable RLS on tables
3. Create policies for data access

### Edge Functions
For serverless functionality:

1. Go to **Edge Functions**
2. Deploy custom functions
3. Handle webhook processing

## Supabase Client Integration

If you want to use Supabase's additional features, add the client:

```bash
cd packages/backend
npm install @supabase/supabase-js
```

Then create a Supabase client:

```typescript
// packages/backend/src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export const supabase = createClient(supabaseUrl, supabaseKey)
```

## Monitoring and Analytics

Supabase provides built-in monitoring:

1. **Database** → **Reports** - Query performance
2. **API** → **Logs** - API usage and errors
3. **Auth** → **Users** - User analytics (if using auth)

## Backup Strategy

Supabase handles backups automatically:

- **Point-in-time recovery** - Up to 7 days (free tier)
- **Daily backups** - Automatic
- **Manual backups** - Export via dashboard

## Scaling Considerations

### Free Tier Limits:
- 500MB database size
- 2GB bandwidth per month
- 50MB file uploads
- 2 concurrent connections

### Paid Tiers:
- **Pro**: $25/month - 8GB database, 250GB bandwidth
- **Team**: $599/month - Dedicated resources
- **Enterprise**: Custom pricing

## Troubleshooting

### Connection Issues
```bash
# Test connection
psql "postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"
```

### Migration Issues
```bash
# Reset migrations (careful!)
npx prisma migrate reset

# Deploy specific migration
npx prisma migrate deploy
```

### Performance Issues
1. Check **Database** → **Reports** for slow queries
2. Add indexes via Prisma schema
3. Use connection pooling (built-in)

## Security Best Practices

1. **Use environment variables** - Never commit credentials
2. **Enable RLS** - When adding user features
3. **Rotate keys** - Regularly update API keys
4. **Monitor usage** - Watch for unusual activity
5. **Use service role key** - Only for server-side operations

## Next Steps

1. Set up your Supabase project
2. Update your environment variables
3. Run the migrations
4. Test the connection
5. Deploy to production

Your Google Reviews Widget Generator is now powered by Supabase! 🚀