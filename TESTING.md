# Testing Guide

This document outlines the comprehensive testing strategy for the Google Reviews Widget Generator project.

## Overview

The testing suite includes:
- **Unit Tests**: Test individual components and functions in isolation
- **Integration Tests**: Test API endpoints and component interactions
- **End-to-End Tests**: Test complete user workflows using Cypress
- **Accessibility Tests**: Ensure WCAG compliance using Playwright and axe-core
- **Cross-Browser Tests**: Verify compatibility across different browsers
- **Performance Tests**: Monitor bundle sizes and runtime performance

## Test Structure

```
packages/
├── frontend/
│   ├── src/test/
│   │   ├── components/           # Unit tests for React components
│   │   ├── integration/          # Integration tests with API
│   │   ├── accessibility/        # Accessibility tests
│   │   ├── mocks/               # Mock server and handlers
│   │   ├── setup.ts             # Test setup configuration
│   │   └── integration-setup.ts # Integration test setup
│   ├── cypress/
│   │   ├── e2e/                 # End-to-end tests
│   │   ├── fixtures/            # Test data fixtures
│   │   └── support/             # Cypress commands and configuration
│   └── playwright.config.ts     # Playwright configuration
├── backend/
│   ├── src/test/
│   │   ├── lib/                 # Unit tests for services and utilities
│   │   ├── integration/         # API integration tests
│   │   ├── factories/           # Test data factories
│   │   └── setup.ts             # Test database setup
│   └── vitest.config.ts         # Vitest configuration
└── widget-runtime/
    ├── src/test/
    │   ├── widget.test.ts       # Core widget functionality tests
    │   ├── performance.test.ts  # Performance and bundle size tests
    │   └── cross-browser.test.ts # Cross-browser compatibility tests
    └── vite.config.ts           # Vite test configuration
```

## Running Tests

### All Tests
```bash
npm run test:all
```

### Unit Tests
```bash
npm run test:unit
```

### Integration Tests
```bash
npm run test:integration
```

### End-to-End Tests
```bash
npm run test:e2e
```

### Accessibility Tests
```bash
npm run test:accessibility
```

### Cross-Browser Tests
```bash
npm run test:cross-browser
```

### Performance Tests
```bash
npm run test:performance
```

## Package-Specific Commands

### Frontend
```bash
cd packages/frontend
npm run test              # All frontend tests
npm run test:unit         # Unit tests only
npm run test:integration  # Integration tests only
npm run test:e2e          # Cypress E2E tests
npm run test:e2e:open     # Open Cypress UI
npm run test:accessibility # Playwright accessibility tests
```

### Backend
```bash
cd packages/backend
npm run test              # All backend tests
npm run test:unit         # Unit tests only
npm run test:integration  # Integration tests only
```

### Widget Runtime
```bash
cd packages/widget-runtime
npm run test              # All widget tests
npm run test:unit         # Unit tests only
npm run test:cross-browser # Cross-browser compatibility tests
npm run test:performance  # Performance tests
```

## Test Categories

### Unit Tests
- **Frontend**: React component testing with React Testing Library
- **Backend**: Service and utility function testing with Vitest
- **Widget Runtime**: Core widget functionality testing

**Coverage Requirements**: Minimum 80% code coverage for all packages

### Integration Tests
- **API Endpoints**: Test all REST API endpoints with real database
- **Component Integration**: Test React components with API interactions
- **Database Operations**: Test data persistence and retrieval

**Test Database**: Uses separate test database with automatic cleanup

### End-to-End Tests
- **Complete User Workflows**: Full widget creation process
- **Error Handling**: Network failures and validation errors
- **Responsive Design**: Mobile and desktop layouts
- **Keyboard Navigation**: Accessibility compliance

**Browser Support**: Chrome, Firefox, Safari, Edge

### Accessibility Tests
- **WCAG Compliance**: Level AA compliance testing
- **Screen Reader Support**: ARIA attributes and semantic HTML
- **Keyboard Navigation**: Tab order and focus management
- **Color Contrast**: Minimum contrast ratios
- **Touch Targets**: Minimum 44px touch targets on mobile

**Tools**: Playwright with axe-core integration

### Cross-Browser Tests
- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Legacy Support**: IE11 compatibility with polyfills
- **Mobile Browsers**: iOS Safari, Chrome Mobile
- **Feature Detection**: Graceful degradation for missing APIs

### Performance Tests
- **Bundle Size**: Widget runtime under 50KB gzipped
- **Load Times**: API responses under 200ms
- **Memory Usage**: No memory leaks in long-running widgets
- **Rendering Performance**: 60fps animations

## Test Data Management

### Factories
Test data is generated using factory patterns:
- `BusinessFactory`: Generate business test data
- `ReviewFactory`: Generate review test data
- `WidgetFactory`: Generate widget configuration test data

### Fixtures
Static test data for consistent E2E testing:
- Business search results
- Review data
- Widget configurations
- Embed code examples

### Mocking
- **API Responses**: MSW (Mock Service Worker) for HTTP mocking
- **External Services**: Google API responses
- **Browser APIs**: ResizeObserver, IntersectionObserver, etc.

## Continuous Integration

### GitHub Actions Workflow
```yaml
- Unit Tests (all packages)
- Integration Tests (with test database)
- E2E Tests (headless browser)
- Accessibility Tests (multiple browsers)
- Performance Tests (bundle analysis)
- Cross-Browser Tests (browser matrix)
```

### Quality Gates
- All tests must pass
- Code coverage above 80%
- No accessibility violations
- Bundle size within limits
- Performance benchmarks met

## Writing Tests

### Unit Test Example
```typescript
import { render, screen } from '@testing-library/react';
import { BusinessSearchComponent } from './BusinessSearchComponent';

describe('BusinessSearchComponent', () => {
  it('should render search input', () => {
    render(<BusinessSearchComponent />);
    expect(screen.getByPlaceholderText(/search for your business/i)).toBeInTheDocument();
  });
});
```

### Integration Test Example
```typescript
import request from 'supertest';
import { app } from '../app';

describe('Business Search API', () => {
  it('should search for businesses', async () => {
    const response = await request(app)
      .post('/api/business/search')
      .send({ query: 'Test Restaurant' })
      .expect(200);
    
    expect(response.body.businesses).toBeDefined();
  });
});
```

### E2E Test Example
```typescript
describe('Widget Creation Flow', () => {
  it('should complete widget creation', () => {
    cy.visit('/');
    cy.selectBusiness('Test Restaurant');
    cy.configureTemplate('carousel');
    cy.get('[data-testid="save-config-button"]').click();
    cy.get('[data-testid="embed-code-display"]').should('be.visible');
  });
});
```

## Debugging Tests

### Frontend Tests
```bash
npm run test:ui  # Open Vitest UI
npm run test:e2e:open  # Open Cypress UI
```

### Backend Tests
```bash
npm run test:watch  # Watch mode with auto-reload
```

### Common Issues
1. **Async Operations**: Use proper async/await or waitFor
2. **Mock Cleanup**: Clear mocks between tests
3. **DOM Cleanup**: Use cleanup utilities
4. **Test Isolation**: Ensure tests don't affect each other

## Performance Monitoring

### Bundle Analysis
```bash
npm run build:analyze  # Analyze bundle sizes
npm run performance    # Run performance tests
```

### Metrics Tracked
- Bundle size (gzipped)
- First Contentful Paint
- Time to Interactive
- API response times
- Memory usage

## Best Practices

1. **Test Naming**: Descriptive test names explaining behavior
2. **Test Structure**: Arrange-Act-Assert pattern
3. **Mock Strategy**: Mock external dependencies, not internal logic
4. **Data Isolation**: Use factories for dynamic test data
5. **Accessibility**: Include accessibility checks in all UI tests
6. **Performance**: Monitor and test performance regularly
7. **Browser Support**: Test across target browser matrix
8. **Error Scenarios**: Test error handling and edge cases

## Troubleshooting

### Common Test Failures
- **Timeout Issues**: Increase timeout for slow operations
- **Flaky Tests**: Add proper waits and assertions
- **Mock Issues**: Verify mock setup and cleanup
- **Browser Compatibility**: Check for missing polyfills

### Getting Help
- Check test logs for detailed error messages
- Use browser dev tools for E2E test debugging
- Review test documentation and examples
- Ask team members for assistance with complex scenarios