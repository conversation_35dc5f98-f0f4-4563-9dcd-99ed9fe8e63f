# CDN Configuration for Google Reviews Widget Generator
# This file contains configuration for setting up CDN distribution

cloudfront:
  distributions:
    - name: grwg-widget-assets
      description: "CDN for widget runtime assets"
      origins:
        - domain: widget-runtime.yourdomain.com
          path: /
          protocol: https
      behaviors:
        - path: "*.js"
          cache_policy: max-age=31536000  # 1 year
          compress: true
        - path: "*.css"
          cache_policy: max-age=31536000  # 1 year
          compress: true
        - path: "*.png,*.jpg,*.jpeg,*.gif,*.svg"
          cache_policy: max-age=2592000   # 30 days
          compress: true
        - path: "/health"
          cache_policy: no-cache
      
    - name: grwg-frontend-assets
      description: "CDN for frontend static assets"
      origins:
        - domain: app.yourdomain.com
          path: /
          protocol: https
      behaviors:
        - path: "/static/*"
          cache_policy: max-age=31536000  # 1 year
          compress: true
        - path: "/assets/*"
          cache_policy: max-age=31536000  # 1 year
          compress: true
        - path: "/"
          cache_policy: max-age=3600      # 1 hour
          compress: true

# Alternative CDN providers configuration
cloudflare:
  zones:
    - name: yourdomain.com
      settings:
        cache_level: aggressive
        browser_cache_ttl: 31536000
        edge_cache_ttl: 2592000
        compression: gzip
        minify:
          css: true
          js: true
          html: true
      
      page_rules:
        - url: "*.yourdomain.com/widget/*"
          settings:
            cache_level: cache_everything
            edge_cache_ttl: 31536000
        - url: "*.yourdomain.com/api/*"
          settings:
            cache_level: bypass

# Cache headers configuration
cache_headers:
  static_assets:
    - "Cache-Control: public, max-age=31536000, immutable"
    - "Expires: Thu, 31 Dec 2025 23:55:55 GMT"
  
  dynamic_content:
    - "Cache-Control: public, max-age=3600"
    - "Vary: Accept-Encoding"
  
  api_responses:
    - "Cache-Control: public, max-age=300"
    - "Vary: Accept-Encoding, Authorization"