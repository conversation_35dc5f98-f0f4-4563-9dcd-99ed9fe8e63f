var t=Object.defineProperty,n=(n,e,i)=>(((n,e,i)=>{e in n?t(n,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[e]=i})(n,"symbol"!=typeof e?e+"":e,i),i);const e=class t{constructor(){n(this,"loadedWidgets",new Set),n(this,"widgetConfigs",new Map),this.init()}static getInstance(){return t.instance||(t.instance=new t),t.instance}init(){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>this.scanAndLoadWidgets()):this.scanAndLoadWidgets(),this.observeForNewWidgets()}scanAndLoadWidgets(){document.querySelectorAll('[class*="grw-widget-"][data-grw-widget-lazy]').forEach(t=>this.loadWidget(t))}observeForNewWidgets(){new MutationObserver(t=>{t.forEach(t=>{t.addedNodes.forEach(t=>{if(t.nodeType===Node.ELEMENT_NODE){const n=t;n.matches&&n.matches('[class*="grw-widget-"][data-grw-widget-lazy]')&&this.loadWidget(n);const e=n.querySelectorAll&&n.querySelectorAll('[class*="grw-widget-"][data-grw-widget-lazy]');e&&e.forEach(t=>this.loadWidget(t))}})})}).observe(document.body,{childList:!0,subtree:!0})}async loadWidget(t){const n=this.extractWidgetId(t);if(n&&!this.loadedWidgets.has(n)){this.loadedWidgets.add(n);try{this.showLoadingState(t);const e=await this.loadWidgetConfig(n);if(!e)throw new Error(`Configuration not found for widget ${n}`);await this.renderWidget(t,e),this.removeLoadingState(t)}catch(e){this.showErrorState(t,e)}}}extractWidgetId(t){const n=Array.from(t.classList).find(t=>t.startsWith("grw-widget-"));return n?n.replace("grw-widget-",""):null}async loadWidgetConfig(t){if(this.widgetConfigs.has(t))return this.widgetConfigs.get(t);try{const e=localStorage.getItem(`widget-config-${t}`);if(e){const n=JSON.parse(e);return this.widgetConfigs.set(t,n),n}const i=["https://google-review-liard.vercel.app","https://google-review.vercel.app",window.location.origin.includes("vercel.app")?window.location.origin:null].filter(Boolean);let o,s;for(const r of i)try{if(o=await fetch(`${r}/api/widget-config/${t}`),o.ok)break}catch(n){s=n;continue}if(!o||!o.ok)throw s||new Error("Failed to fetch widget configuration from any URL");if(o.ok){const n=await o.json(),e=n.config||n;return this.widgetConfigs.set(t,e),e}}catch(n){}return null}showLoadingState(t){t.innerHTML='\n      <div style="\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        min-height: 200px;\n        background: #f8f9fa;\n        border-radius: 8px;\n        border: 1px solid #e0e0e0;\n        font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;\n        color: #666;\n      ">\n        <div style="text-align: center;">\n          <div style="\n            width: 24px;\n            height: 24px;\n            border: 2px solid #e0e0e0;\n            border-top: 2px solid #4285f4;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin: 0 auto 12px;\n          "></div>\n          <div>Loading reviews...</div>\n        </div>\n      </div>\n      <style>\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      </style>\n    '}removeLoadingState(t){}showErrorState(t,n){t.innerHTML=`\n      <div style="\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        min-height: 200px;\n        background: #fff5f5;\n        border-radius: 8px;\n        border: 1px solid #fed7d7;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        color: #c53030;\n        text-align: center;\n        padding: 20px;\n      ">\n        <div>\n          <div style="font-size: 18px; margin-bottom: 8px;">⚠️</div>\n          <div style="font-weight: 500; margin-bottom: 4px;">Failed to load reviews</div>\n          <div style="font-size: 12px; opacity: 0.8;">${n.message}</div>\n        </div>\n      </div>\n    `}async renderWidget(t,n){new o(t,n).render()}};n(e,"instance");let i=e;class o{constructor(t,e){n(this,"element"),n(this,"config"),this.element=t,this.config=e}render(){var t;this.element.innerHTML="";const n=document.createElement("div");if(n.className="grw-simple-widget",n.style.cssText=`\n            width: ${this.config.styling.dimensions.width};\n            height: ${this.config.styling.dimensions.height};\n            background: ${this.config.styling.colors.background};\n            border: 1px solid ${this.config.styling.colors.border};\n            border-radius: ${this.config.styling.dimensions.borderRadius};\n            padding: ${this.config.styling.spacing.padding};\n            font-family: ${this.config.styling.fonts.family};\n            font-size: ${this.config.styling.fonts.size};\n            color: ${this.config.styling.colors.text};\n            overflow: hidden;\n            position: relative;\n        `,null==(t=this.config.headerSettings)?void 0:t.showHeader){const t=this.createHeader();n.appendChild(t)}const e=this.createReviewsContent();n.appendChild(e),this.element.appendChild(n)}createHeader(){var t,n,e;const i=document.createElement("div");if(i.className="grw-widget-header",i.style.cssText=`\n            text-align: ${(null==(t=this.config.headerSettings)?void 0:t.headerAlign)||"center"};\n            margin-bottom: 16px;\n            border-bottom: 1px solid ${this.config.styling.colors.border};\n            padding-bottom: 12px;\n        `,null==(n=this.config.headerSettings)?void 0:n.showHeader){const t=document.createElement("h3");t.textContent=this.config.headerSettings.headerText||"Customer Reviews",t.style.cssText=`\n                margin: 0 0 8px 0;\n                color: ${this.config.headerSettings.headerColor||this.config.styling.colors.text};\n                font-size: ${"large"===this.config.headerSettings.headerSize?"24px":"small"===this.config.headerSettings.headerSize?"16px":"20px"};\n                font-weight: 600;\n            `,i.appendChild(t)}if(null==(e=this.config.headerSettings)?void 0:e.showSubheader){const t=document.createElement("p");t.textContent=this.config.headerSettings.subheaderText||"What our customers are saying",t.style.cssText=`\n                margin: 0;\n                color: ${this.config.headerSettings.subheaderColor||"#666"};\n                font-size: 14px;\n                opacity: 0.8;\n            `,i.appendChild(t)}return i}createReviewsContent(){const t=document.createElement("div");return t.className="grw-reviews-content",t.style.cssText=`\n            display: flex;\n            flex-direction: column;\n            gap: ${this.config.styling.spacing.gap};\n            height: 100%;\n            overflow-y: auto;\n        `,[{name:"Sarah Allen",rating:5,text:"Super fast delivery and we love our new toys. Thank you",date:"10 days ago",avatar:"S"},{name:"South Auckland - Manukau",rating:5,text:"Easy as. Ordered online, paid and within a week parcel arrived. Thank you",date:"12 days ago",avatar:"S"},{name:"Hugh & Jane Masters",rating:5,text:"Easy website to browse. Great efficient service on ordering. Impressed by the...",date:"2 weeks ago",avatar:"H"}].slice(0,this.config.settings.maxReviews).forEach(n=>{if(n.rating>=this.config.settings.minRating){const e=this.createReviewElement(n);t.appendChild(e)}}),t}createReviewElement(t){const n=document.createElement("div");n.className="grw-review-item",n.style.cssText="\n            display: flex;\n            gap: 12px;\n            padding: 12px;\n            background: rgba(255, 255, 255, 0.5);\n            border-radius: 8px;\n            border: 1px solid rgba(0, 0, 0, 0.1);\n        ";const e=document.createElement("div");e.style.cssText=`\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            background: ${this.config.styling.colors.primary};\n            color: white;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-weight: 600;\n            flex-shrink: 0;\n        `,e.textContent=t.avatar,n.appendChild(e);const i=document.createElement("div");i.style.cssText="flex: 1; min-width: 0;";const o=document.createElement("div");o.style.cssText="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;";const s=document.createElement("span");s.textContent=t.name,s.style.cssText="font-weight: 500; font-size: 14px;",o.appendChild(s);const r=document.createElement("span");r.textContent="★".repeat(t.rating),r.style.cssText=`color: ${this.config.styling.colors.secondary}; font-size: 12px;`,o.appendChild(r),i.appendChild(o);const a=document.createElement("p");if(a.textContent=t.text,a.style.cssText="margin: 0 0 4px 0; font-size: 13px; line-height: 1.4;",i.appendChild(a),this.config.settings.showDates){const n=document.createElement("span");n.textContent=t.date,n.style.cssText="font-size: 11px; color: #666; opacity: 0.8;",i.appendChild(n)}return n.appendChild(i),n}}const s=i.getInstance();window.GoogleReviewsWidgetPlatform=s,window.GoogleReviewsWidget={render:(t,n)=>{new(require("./components/ReviewsDisplay").WidgetRenderer)(t,n).render()}};