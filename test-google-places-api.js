const https = require('https');

// Your API key from the .env file
const API_KEY = 'AIzaSyCbtGpXrq2VQAp4n7nWaPSpXHCTSnUyVA0';

// Test 1: Simple text search for a well-known business
console.log('🔍 Testing Google Places API...\n');

function testPlacesAPI() {
    // Test with a well-known business first
    const query = 'McDonald\'s Auckland New Zealand';
    const url = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${API_KEY}`;

    console.log('Testing with query:', query);
    console.log('API URL:', url.replace(API_KEY, 'YOUR_API_KEY'));

    https.get(url, (res) => {
        let data = '';

        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            try {
                const response = JSON.parse(data);

                console.log('\n📊 API Response Status:', response.status);

                if (response.status === 'OK') {
                    console.log('✅ SUCCESS! Google Places API is working');
                    console.log(`Found ${response.results.length} results`);

                    if (response.results.length > 0) {
                        const firstResult = response.results[0];
                        console.log('\n📍 First Result:');
                        console.log('  Name:', firstResult.name);
                        console.log('  Address:', firstResult.formatted_address);
                        console.log('  Place ID:', firstResult.place_id);
                        console.log('  Rating:', firstResult.rating || 'No rating');
                    }

                    // Now test with your business
                    console.log('\n🔍 Now testing with your business...');
                    testYourBusiness();

                } else if (response.status === 'REQUEST_DENIED') {
                    console.log('❌ ERROR: API Request Denied');
                    console.log('This usually means:');
                    console.log('  1. Places API is not enabled for your project');
                    console.log('  2. API key restrictions are blocking the request');
                    console.log('  3. Billing is not set up');
                    console.log('\nError message:', response.error_message || 'No specific error message');

                } else if (response.status === 'OVER_QUERY_LIMIT') {
                    console.log('❌ ERROR: Over Query Limit');
                    console.log('You have exceeded your API quota');

                } else {
                    console.log('❌ ERROR: API returned status:', response.status);
                    console.log('Error message:', response.error_message || 'No specific error message');
                }

            } catch (error) {
                console.log('❌ ERROR: Failed to parse API response');
                console.log('Raw response:', data);
            }
        });

    }).on('error', (error) => {
        console.log('❌ ERROR: Network request failed');
        console.log(error.message);
    });
}

function testYourBusiness() {
    const query = 'The Toy Box Te Puke New Zealand';
    const url = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${API_KEY}`;

    console.log('Testing your business with query:', query);

    https.get(url, (res) => {
        let data = '';

        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            try {
                const response = JSON.parse(data);

                console.log('\n📊 Your Business Search Status:', response.status);

                if (response.status === 'OK') {
                    if (response.results.length > 0) {
                        console.log('🎉 FOUND YOUR BUSINESS!');
                        response.results.forEach((result, index) => {
                            console.log(`\n📍 Result ${index + 1}:`);
                            console.log('  Name:', result.name);
                            console.log('  Address:', result.formatted_address);
                            console.log('  Place ID:', result.place_id);
                            console.log('  Rating:', result.rating || 'No rating');
                            console.log('  Types:', result.types?.join(', ') || 'No types');
                        });
                    } else {
                        console.log('❌ Your business was not found in Google Places');
                        console.log('This could mean:');
                        console.log('  1. Business is not yet listed on Google');
                        console.log('  2. Business name is different in Google\'s database');
                        console.log('  3. Business needs to be verified on Google Business Profile');
                        console.log('\n💡 Try these alternatives:');
                        console.log('  - Search for just "Toy Box Te Puke"');
                        console.log('  - Use your street address instead');
                        console.log('  - Check if your business appears on Google Maps manually');
                    }
                } else {
                    console.log('❌ Search failed with status:', response.status);
                }

            } catch (error) {
                console.log('❌ ERROR: Failed to parse response for your business');
                console.log('Raw response:', data);
            }
        });

    }).on('error', (error) => {
        console.log('❌ ERROR: Network request failed for your business');
        console.log(error.message);
    });
}

// Start the test
testPlacesAPI();
