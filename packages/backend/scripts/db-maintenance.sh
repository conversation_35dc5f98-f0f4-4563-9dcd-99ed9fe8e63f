#!/bin/bash

# Database maintenance script for production
# Usage: ./db-maintenance.sh [environment]

set -e

ENVIRONMENT=${1:-production}

# Load environment variables
if [ -f ".env.${ENVIRONMENT}" ]; then
    source ".env.${ENVIRONMENT}"
fi

echo "Starting database maintenance for ${ENVIRONMENT} environment..."

# Connect to database and run maintenance queries
psql "${DATABASE_URL}" << EOF

-- Analyze tables for query optimization
ANALYZE businesses;
ANALYZE widget_configs;
ANALYZE reviews_cache;

-- Update table statistics
VACUUM ANALYZE businesses;
VACUUM ANALYZE widget_configs;
VACUUM ANALYZE reviews_cache;

-- Clean up expired cache entries
DELETE FROM reviews_cache WHERE expires_at < NOW();

-- Show table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Show index usage statistics
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Show slow queries (if pg_stat_statements is enabled)
-- SELECT query, calls, total_time, mean_time, rows 
-- FROM pg_stat_statements 
-- ORDER BY mean_time DESC 
-- LIMIT 10;

EOF

echo "Database maintenance completed successfully"