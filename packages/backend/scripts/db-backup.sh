#!/bin/bash

# Database backup script for production
# Usage: ./db-backup.sh [environment]

set -e

ENVIRONMENT=${1:-production}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/postgres"
RETENTION_DAYS=30

# Load environment variables
if [ -f ".env.${ENVIRONMENT}" ]; then
    source ".env.${ENVIRONMENT}"
fi

# Ensure backup directory exists
mkdir -p "${BACKUP_DIR}"

# Create backup filename
BACKUP_FILE="${BACKUP_DIR}/grwg_backup_${ENVIRONMENT}_${TIMESTAMP}.sql"

echo "Starting database backup for ${ENVIRONMENT} environment..."

# Create database backup
pg_dump "${DATABASE_URL}" \
    --verbose \
    --clean \
    --no-owner \
    --no-privileges \
    --format=custom \
    --file="${BACKUP_FILE}.custom"

# Also create a plain SQL backup for easier inspection
pg_dump "${DATABASE_URL}" \
    --verbose \
    --clean \
    --no-owner \
    --no-privileges \
    --format=plain \
    --file="${BACKUP_FILE}"

# Compress the plain SQL backup
gzip "${BACKUP_FILE}"

echo "Database backup completed: ${BACKUP_FILE}.gz"
echo "Custom format backup: ${BACKUP_FILE}.custom"

# Clean up old backups (keep only last 30 days)
find "${BACKUP_DIR}" -name "grwg_backup_${ENVIRONMENT}_*.sql.gz" -mtime +${RETENTION_DAYS} -delete
find "${BACKUP_DIR}" -name "grwg_backup_${ENVIRONMENT}_*.custom" -mtime +${RETENTION_DAYS} -delete

echo "Old backups cleaned up (retention: ${RETENTION_DAYS} days)"

# Upload to cloud storage (uncomment and configure as needed)
# aws s3 cp "${BACKUP_FILE}.gz" "s3://your-backup-bucket/postgres/${ENVIRONMENT}/"
# aws s3 cp "${BACKUP_FILE}.custom" "s3://your-backup-bucket/postgres/${ENVIRONMENT}/"

echo "Backup process completed successfully"