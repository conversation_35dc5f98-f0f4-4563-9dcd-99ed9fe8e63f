// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Business {
  id          String   @id @default(uuid())
  placeId     String   @unique @map("place_id")
  name        String
  address     String?
  rating      Float?
  reviewCount Int?     @map("review_count")
  photoUrl    String?  @map("photo_url")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  widgetConfigs WidgetConfig[]
  reviewsCache  ReviewsCache[]

  // Indexes for performance
  @@index([name])
  @@index([rating])
  @@index([createdAt])
  @@map("businesses")
}

model WidgetConfig {
  id             String   @id @default(uuid())
  businessId     String   @map("business_id")
  templateType   String   @map("template_type")
  stylingConfig  Json     @map("styling_config")
  widgetSettings Json     @map("widget_settings")
  embedCode      String?  @map("embed_code")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  business Business @relation(fields: [businessId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([businessId])
  @@index([templateType])
  @@index([createdAt])
  @@map("widget_configs")
}

model ReviewsCache {
  id           String   @id @default(uuid())
  businessId   String   @map("business_id")
  reviewsData  Json     @map("reviews_data")
  lastFetched  DateTime @map("last_fetched")
  expiresAt    DateTime @map("expires_at")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  business Business @relation(fields: [businessId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([businessId])
  @@index([expiresAt])
  @@index([lastFetched])
  @@map("reviews_cache")
}