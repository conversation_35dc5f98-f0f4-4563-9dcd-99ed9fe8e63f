{"name": "@grwg/backend", "version": "1.0.0", "description": "Node.js backend API for Google Reviews Widget Generator", "private": true, "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "test:unit": "vitest run --reporter=verbose src/test/lib", "test:integration": "vitest run --reporter=verbose src/test/integration", "test:all": "npm run test:unit && npm run test:integration", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.0.0", "@supabase/supabase-js": "^2.52.0", "axios": "^1.4.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^4.18.0", "helmet": "^7.0.0", "prom-client": "^15.0.0", "rate-limiter-flexible": "^2.4.2", "redis": "^4.6.0", "uuid": "^9.0.0", "winston": "^3.10.0", "zod": "^3.21.0"}, "devDependencies": {"@faker-js/faker": "^8.3.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.0", "@types/node": "^20.4.0", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "nock": "^13.4.0", "prisma": "^5.0.0", "redis-memory-server": "^0.10.0", "supertest": "^6.3.0", "testcontainers": "^10.4.0", "tsx": "^3.12.0", "vitest": "^0.33.0"}}