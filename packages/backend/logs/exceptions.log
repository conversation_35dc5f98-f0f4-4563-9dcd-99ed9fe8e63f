{"timestamp":"2025-07-18T04:13:46.044Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesA<PERSON><PERSON>ey, myBusinessApi<PERSON>ey\nError: Google API configuration validation failed. Missing or invalid fields: places<PERSON><PERSON><PERSON><PERSON>, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:13:46 GMT+1200 (New Zealand Standard Time)","process":{"pid":50811,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":148733952,"heapTotal":55922688,"heapUsed":35401216,"external":8172834,"arrayBuffers":2986599}},"os":{"loadavg":[4.***********,10.***********,11.095703125],"uptime":226467},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":50811}
{"timestamp":"2025-07-18T04:18:01.223Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:18:01 GMT+1200 (New Zealand Standard Time)","process":{"pid":53607,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":111841280,"heapTotal":37908480,"heapUsed":23553032,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[14.927734375,10.7177734375,10.***********],"uptime":226722},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":53607}
{"timestamp":"2025-07-18T04:18:31.900Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:18:31 GMT+1200 (New Zealand Standard Time)","process":{"pid":53757,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":110993408,"heapTotal":37646336,"heapUsed":23714344,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[13.***********,10.***********,10.9345703125],"uptime":226752},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":53757}
{"timestamp":"2025-07-18T04:18:43.613Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:18:43 GMT+1200 (New Zealand Standard Time)","process":{"pid":53786,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":113827840,"heapTotal":37908480,"heapUsed":23555096,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[13.***********,10.8251953125,10.***********],"uptime":226764},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":53786}
{"timestamp":"2025-07-18T04:18:59.564Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:18:59 GMT+1200 (New Zealand Standard Time)","process":{"pid":53857,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":110374912,"heapTotal":37646336,"heapUsed":23552576,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[11.***********,10.***********,10.***********],"uptime":226780},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":53857}
{"timestamp":"2025-07-18T04:45:54.540Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:45:54 GMT+1200 (New Zealand Standard Time)","process":{"pid":64830,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":122114048,"heapTotal":38170624,"heapUsed":23935392,"external":4521620,"arrayBuffers":121153}},"os":{"loadavg":[7.3232421875,7.***********,7.***********],"uptime":228395},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":64830}
{"timestamp":"2025-07-18T04:48:28.271Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:48:28 GMT+1200 (New Zealand Standard Time)","process":{"pid":66163,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":110403584,"heapTotal":37908480,"heapUsed":23658952,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[8.***********,6.***********,7.***********],"uptime":228549},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":66163}
{"timestamp":"2025-07-18T04:48:39.560Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:48:39 GMT+1200 (New Zealand Standard Time)","process":{"pid":66407,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":113176576,"heapTotal":38694912,"heapUsed":22618936,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[7.***********,6.***********,7.***********],"uptime":228560},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":66407}
{"timestamp":"2025-07-18T04:49:05.707Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 16:49:05 GMT+1200 (New Zealand Standard Time)","process":{"pid":66827,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":112238592,"heapTotal":38170624,"heapUsed":23618336,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[6.2646484375,6.***********,6.947265625],"uptime":228586},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":66827}
{"timestamp":"2025-07-18T05:10:55.407Z","level":"error","message":"uncaughtException: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\nError: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","service":"grwg-backend","version":"1.0.0","error":{},"stack":"Error: Google API configuration validation failed. Missing or invalid fields: placesApiKey, myBusinessApiKey\n    at loadGoogleApiConfig (file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts:33:13)\n    at new BusinessSearchService (file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts:41:20)\n    at file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts:8:31\n    at ModuleJob.run (node:internal/modules/esm/module_job:329:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)","exception":true,"date":"Fri Jul 18 2025 17:10:55 GMT+1200 (New Zealand Standard Time)","process":{"pid":71300,"uid":501,"gid":20,"cwd":"/Users/<USER>/google-review/packages/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","version":"v22.17.0","argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/google-review/packages/backend/src/index.ts"],"memoryUsage":{"rss":113524736,"heapTotal":37908480,"heapUsed":23757320,"external":4468671,"arrayBuffers":68156}},"os":{"loadavg":[8.552734375,4.***********,4.6767578125],"uptime":229896},"trace":[{"column":13,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/google-api/config.ts","function":"loadGoogleApiConfig","line":33,"method":null,"native":false},{"column":20,"file":"file:///Users/<USER>/google-review/packages/backend/src/lib/services/business-search.service.ts","function":"new BusinessSearchService","line":41,"method":null,"native":false},{"column":31,"file":"file:///Users/<USER>/google-review/packages/backend/src/routes/business.routes.ts","function":null,"line":8,"method":null,"native":false},{"column":25,"file":"node:internal/modules/esm/module_job","function":"ModuleJob.run","line":329,"method":"run","native":false},{"column":26,"file":"node:internal/modules/esm/loader","function":"onImport.tracePromise.__proto__","line":644,"method":"__proto__","native":false},{"column":5,"file":"node:internal/modules/run_main","function":"asyncRunEntryPointWithESMLoader","line":117,"method":null,"native":false}],"hostname":"unknown","pid":71300}
