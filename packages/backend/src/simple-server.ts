import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import { testSupabaseConnection } from './lib/supabase.js';

// Load environment variables
dotenv.config({ path: '.env' });

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
    const dbTest = await testSupabaseConnection();
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: dbTest.success ? 'connected' : 'disconnected',
        error: dbTest.error || null
    });
});

// API routes
app.get('/api', (req, res) => {
    res.json({ message: 'Google Reviews Widget Generator API' });
});

// Basic business search endpoint (placeholder)
app.post('/api/business/search', (req, res) => {
    res.json({
        businesses: [],
        message: 'Business search endpoint - ready for Google API integration'
    });
});

// Basic reviews endpoint (placeholder)
app.get('/api/reviews/:businessId', (req, res) => {
    res.json({
        reviews: [],
        message: 'Reviews endpoint - ready for Google API integration'
    });
});

// Basic widget config endpoints (placeholder)
app.get('/api/widget-configs', (req, res) => {
    res.json({
        configs: [],
        message: 'Widget configs endpoint - ready for database integration'
    });
});

app.post('/api/widget-configs', (req, res) => {
    res.json({
        success: true,
        message: 'Widget config created - ready for database integration'
    });
});

// Basic embed code endpoint (placeholder)
app.get('/api/embed-code/:configId', (req, res) => {
    const { configId } = req.params;
    const embedCode = `<script src="http://localhost:3002/widget.js" data-grw-widget-id="${configId}"></script>`;

    res.json({
        embedCode,
        message: 'Embed code generated'
    });
});

// Basic widget data endpoint (placeholder)
app.get('/api/widget/:widgetId/data', (req, res) => {
    const { widgetId } = req.params;

    res.json({
        reviews: [],
        businessInfo: {
            name: 'Sample Business',
            rating: 4.5,
            reviewCount: 0
        },
        totalReviews: 0,
        averageRating: 4.5,
        lastUpdated: new Date(),
        message: `Widget data for ${widgetId} - ready for Google API integration`
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// Error handler
app.use((err: any, req: any, res: any, next: any) => {
    console.error('Server error:', err);
    res.status(500).json({ error: 'Internal server error' });
});

app.listen(PORT, async () => {
    console.log(`✅ Backend server running on port ${PORT}`);

    // Test database connection
    const dbTest = await testSupabaseConnection();
    if (dbTest.success) {
        console.log('✅ Database connection successful');
    } else {
        console.log('❌ Database connection failed:', dbTest.error);
    }
});
