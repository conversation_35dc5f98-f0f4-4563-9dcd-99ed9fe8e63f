import { PrismaClient } from '@prisma/client';
import { DatabaseResult } from '../types/index.js';

// Global Prisma client instance
let prisma: PrismaClient;

// Database connection configuration
const DATABASE_CONFIG = {
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] as const : ['error'] as const,
  errorFormat: 'pretty' as const,
};

/**
 * Get or create Prisma client instance
 */
export function getPrismaClient(): PrismaClient {
  if (!prisma) {
    prisma = new PrismaClient(DATABASE_CONFIG);
  }
  return prisma;
}

/**
 * Initialize database connection
 */
export async function initializeDatabase(): Promise<DatabaseResult> {
  try {
    const client = getPrismaClient();
    
    // Test the connection
    await client.$connect();
    
    console.log('Database connection established successfully');
    
    return {
      success: true,
      data: 'Database initialized successfully',
    };
  } catch (error) {
    console.error('Failed to initialize database:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown database error',
    };
  }
}

/**
 * Close database connection
 */
export async function closeDatabaseConnection(): Promise<void> {
  if (prisma) {
    await prisma.$disconnect();
    console.log('Database connection closed');
  }
}

/**
 * Health check for database connection
 */
export async function checkDatabaseHealth(): Promise<DatabaseResult> {
  try {
    const client = getPrismaClient();
    
    // Simple query to test connection
    await client.$queryRaw`SELECT 1`;
    
    return {
      success: true,
      data: 'Database is healthy',
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Database health check failed',
    };
  }
}

/**
 * Execute database transaction
 */
export async function executeTransaction<T>(
  callback: (prisma: PrismaClient) => Promise<T>
): Promise<DatabaseResult<T>> {
  try {
    const client = getPrismaClient();
    const result = await client.$transaction(callback);
    
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Transaction failed',
    };
  }
}

/**
 * Handle database errors with proper logging
 */
export function handleDatabaseError(error: unknown, operation: string): DatabaseResult {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  
  console.error(`Database operation failed [${operation}]:`, error);
  
  return {
    success: false,
    error: `${operation} failed: ${errorMessage}`,
  };
}

// Export the Prisma client instance
export { prisma };
export default getPrismaClient;