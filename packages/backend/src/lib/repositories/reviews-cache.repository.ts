import { PrismaClient } from '@prisma/client';
import { getPrismaClient, handleDatabaseError } from '../database.js';
import { 
  ReviewsCacheData, 
  DatabaseResult 
} from '../../types/index.js';

export interface ReviewsCacheRecord {
  id: string;
  businessId: string;
  reviewsData: ReviewsCacheData;
  lastFetched: Date;
  expiresAt: Date;
  createdAt: Date;
}

export interface ReviewsCacheCreate {
  businessId: string;
  reviewsData: ReviewsCacheData;
  lastFetched: Date;
  expiresAt: Date;
}

export class ReviewsCacheRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = getPrismaClient();
  }

  /**
   * Create or update reviews cache entry
   */
  async upsert(data: ReviewsCacheCreate): Promise<DatabaseResult<ReviewsCacheRecord>> {
    try {
      const reviewsCache = await this.prisma.reviewsCache.upsert({
        where: { businessId: data.businessId },
        update: {
          reviewsData: data.reviewsData,
          lastFetched: data.lastFetched,
          expiresAt: data.expiresAt,
        },
        create: {
          businessId: data.businessId,
          reviewsData: data.reviewsData,
          lastFetched: data.lastFetched,
          expiresAt: data.expiresAt,
        },
        include: {
          business: true,
        },
      });

      return {
        success: true,
        data: reviewsCache as ReviewsCacheRecord,
      };
    } catch (error) {
      return handleDatabaseError(error, 'upsert reviews cache');
    }
  }

  /**
   * Find reviews cache by business ID
   */
  async findByBusinessId(businessId: string): Promise<DatabaseResult<ReviewsCacheRecord | null>> {
    try {
      const reviewsCache = await this.prisma.reviewsCache.findUnique({
        where: { businessId },
        include: {
          business: true,
        },
      });

      return {
        success: true,
        data: reviewsCache as ReviewsCacheRecord | null,
      };
    } catch (error) {
      return handleDatabaseError(error, 'find reviews cache by business ID');
    }
  }

  /**
   * Find valid (non-expired) reviews cache by business ID
   */
  async findValidByBusinessId(businessId: string): Promise<DatabaseResult<ReviewsCacheRecord | null>> {
    try {
      const now = new Date();
      const reviewsCache = await this.prisma.reviewsCache.findFirst({
        where: {
          businessId,
          expiresAt: {
            gt: now,
          },
        },
        include: {
          business: true,
        },
      });

      return {
        success: true,
        data: reviewsCache as ReviewsCacheRecord | null,
      };
    } catch (error) {
      return handleDatabaseError(error, 'find valid reviews cache by business ID');
    }
  }

  /**
   * Delete reviews cache by business ID
   */
  async deleteByBusinessId(businessId: string): Promise<DatabaseResult<boolean>> {
    try {
      await this.prisma.reviewsCache.deleteMany({
        where: { businessId },
      });

      return {
        success: true,
        data: true,
      };
    } catch (error) {
      return handleDatabaseError(error, 'delete reviews cache by business ID');
    }
  }

  /**
   * Delete expired cache entries
   */
  async deleteExpired(): Promise<DatabaseResult<number>> {
    try {
      const now = new Date();
      const result = await this.prisma.reviewsCache.deleteMany({
        where: {
          expiresAt: {
            lt: now,
          },
        },
      });

      return {
        success: true,
        data: result.count,
      };
    } catch (error) {
      return handleDatabaseError(error, 'delete expired reviews cache');
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<DatabaseResult<{
    total: number;
    expired: number;
    valid: number;
  }>> {
    try {
      const now = new Date();
      
      const [total, expired] = await Promise.all([
        this.prisma.reviewsCache.count(),
        this.prisma.reviewsCache.count({
          where: {
            expiresAt: {
              lt: now,
            },
          },
        }),
      ]);

      return {
        success: true,
        data: {
          total,
          expired,
          valid: total - expired,
        },
      };
    } catch (error) {
      return handleDatabaseError(error, 'get cache statistics');
    }
  }

  /**
   * Check if cache exists and is valid for business
   */
  async isValidForBusiness(businessId: string): Promise<DatabaseResult<boolean>> {
    try {
      const now = new Date();
      const count = await this.prisma.reviewsCache.count({
        where: {
          businessId,
          expiresAt: {
            gt: now,
          },
        },
      });

      return {
        success: true,
        data: count > 0,
      };
    } catch (error) {
      return handleDatabaseError(error, 'check cache validity for business');
    }
  }

  /**
   * Get cache entries that need refresh (expired or close to expiring)
   */
  async findNeedingRefresh(bufferMinutes: number = 30): Promise<DatabaseResult<ReviewsCacheRecord[]>> {
    try {
      const bufferTime = new Date();
      bufferTime.setMinutes(bufferTime.getMinutes() + bufferMinutes);

      const reviewsCaches = await this.prisma.reviewsCache.findMany({
        where: {
          expiresAt: {
            lt: bufferTime,
          },
        },
        include: {
          business: true,
        },
        orderBy: {
          expiresAt: 'asc',
        },
      });

      return {
        success: true,
        data: reviewsCaches as ReviewsCacheRecord[],
      };
    } catch (error) {
      return handleDatabaseError(error, 'find cache entries needing refresh');
    }
  }
}