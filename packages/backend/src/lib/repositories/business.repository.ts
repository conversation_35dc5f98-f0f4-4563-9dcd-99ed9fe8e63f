import { PrismaClient } from '@prisma/client';
import { getPrismaClient, handleDatabaseError } from '../database.js';
import { 
  Business, 
  BusinessCreate, 
  BusinessUpdate, 
  DatabaseResult,
  PaginationParams,
  PaginatedResponse 
} from '../../types/index.js';

export class BusinessRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = getPrismaClient();
  }

  /**
   * Create a new business
   */
  async create(data: BusinessCreate): Promise<DatabaseResult<Business>> {
    try {
      const business = await this.prisma.business.create({
        data: {
          placeId: data.placeId,
          name: data.name,
          address: data.address,
          rating: data.rating,
          reviewCount: data.reviewCount,
          photoUrl: data.photoUrl,
        },
      });

      return {
        success: true,
        data: business as Business,
      };
    } catch (error) {
      return handleDatabaseError(error, 'create business');
    }
  }

  /**
   * Find business by ID
   */
  async findById(id: string): Promise<DatabaseResult<Business | null>> {
    try {
      const business = await this.prisma.business.findUnique({
        where: { id },
      });

      return {
        success: true,
        data: business as Business | null,
      };
    } catch (error) {
      return handleDatabaseError(error, 'find business by ID');
    }
  }

  /**
   * Find business by place ID
   */
  async findByPlaceId(placeId: string): Promise<DatabaseResult<Business | null>> {
    try {
      const business = await this.prisma.business.findUnique({
        where: { placeId },
      });

      return {
        success: true,
        data: business as Business | null,
      };
    } catch (error) {
      return handleDatabaseError(error, 'find business by place ID');
    }
  }

  /**
   * Update business
   */
  async update(id: string, data: BusinessUpdate): Promise<DatabaseResult<Business>> {
    try {
      const business = await this.prisma.business.update({
        where: { id },
        data: {
          ...(data.placeId && { placeId: data.placeId }),
          ...(data.name && { name: data.name }),
          ...(data.address !== undefined && { address: data.address }),
          ...(data.rating !== undefined && { rating: data.rating }),
          ...(data.reviewCount !== undefined && { reviewCount: data.reviewCount }),
          ...(data.photoUrl !== undefined && { photoUrl: data.photoUrl }),
        },
      });

      return {
        success: true,
        data: business as Business,
      };
    } catch (error) {
      return handleDatabaseError(error, 'update business');
    }
  }

  /**
   * Delete business
   */
  async delete(id: string): Promise<DatabaseResult<boolean>> {
    try {
      await this.prisma.business.delete({
        where: { id },
      });

      return {
        success: true,
        data: true,
      };
    } catch (error) {
      return handleDatabaseError(error, 'delete business');
    }
  }

  /**
   * Search businesses by name
   */
  async searchByName(
    query: string, 
    pagination?: PaginationParams
  ): Promise<DatabaseResult<PaginatedResponse<Business>>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const skip = (page - 1) * limit;

      const [businesses, total] = await Promise.all([
        this.prisma.business.findMany({
          where: {
            name: {
              contains: query,
              mode: 'insensitive',
            },
          },
          skip,
          take: limit,
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.business.count({
          where: {
            name: {
              contains: query,
              mode: 'insensitive',
            },
          },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: {
          data: businesses as Business[],
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return handleDatabaseError(error, 'search businesses by name');
    }
  }

  /**
   * Get all businesses with pagination
   */
  async findAll(pagination?: PaginationParams): Promise<DatabaseResult<PaginatedResponse<Business>>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const skip = (page - 1) * limit;

      const [businesses, total] = await Promise.all([
        this.prisma.business.findMany({
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
        }),
        this.prisma.business.count(),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: {
          data: businesses as Business[],
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return handleDatabaseError(error, 'find all businesses');
    }
  }

  /**
   * Check if business exists by place ID
   */
  async existsByPlaceId(placeId: string): Promise<DatabaseResult<boolean>> {
    try {
      const count = await this.prisma.business.count({
        where: { placeId },
      });

      return {
        success: true,
        data: count > 0,
      };
    } catch (error) {
      return handleDatabaseError(error, 'check business existence by place ID');
    }
  }
}