import { PrismaClient } from '@prisma/client';
import { getPrismaClient, handleDatabaseError } from '../database.js';
import { 
  WidgetConfig, 
  WidgetConfigCreate, 
  WidgetConfigUpdate, 
  DatabaseResult,
  PaginationParams,
  PaginatedResponse 
} from '../../types/index.js';

export class WidgetConfigRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = getPrismaClient();
  }

  /**
   * Create a new widget configuration
   */
  async create(data: WidgetConfigCreate): Promise<DatabaseResult<WidgetConfig>> {
    try {
      const widgetConfig = await this.prisma.widgetConfig.create({
        data: {
          businessId: data.businessId,
          templateType: data.templateType,
          stylingConfig: data.stylingConfig,
          widgetSettings: data.widgetSettings,
        },
        include: {
          business: true,
        },
      });

      return {
        success: true,
        data: widgetConfig as WidgetConfig,
      };
    } catch (error) {
      return handleDatabaseError(error, 'create widget configuration');
    }
  }

  /**
   * Find widget configuration by ID
   */
  async findById(id: string): Promise<DatabaseResult<WidgetConfig | null>> {
    try {
      const widgetConfig = await this.prisma.widgetConfig.findUnique({
        where: { id },
        include: {
          business: true,
        },
      });

      return {
        success: true,
        data: widgetConfig as WidgetConfig | null,
      };
    } catch (error) {
      return handleDatabaseError(error, 'find widget configuration by ID');
    }
  }

  /**
   * Find widget configurations by business ID
   */
  async findByBusinessId(
    businessId: string,
    pagination?: PaginationParams
  ): Promise<DatabaseResult<PaginatedResponse<WidgetConfig>>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const skip = (page - 1) * limit;

      const [widgetConfigs, total] = await Promise.all([
        this.prisma.widgetConfig.findMany({
          where: { businessId },
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            business: true,
          },
        }),
        this.prisma.widgetConfig.count({
          where: { businessId },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: {
          data: widgetConfigs as WidgetConfig[],
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return handleDatabaseError(error, 'find widget configurations by business ID');
    }
  }

  /**
   * Update widget configuration
   */
  async update(id: string, data: WidgetConfigUpdate): Promise<DatabaseResult<WidgetConfig>> {
    try {
      const widgetConfig = await this.prisma.widgetConfig.update({
        where: { id },
        data: {
          ...(data.businessId && { businessId: data.businessId }),
          ...(data.templateType && { templateType: data.templateType }),
          ...(data.stylingConfig && { stylingConfig: data.stylingConfig }),
          ...(data.widgetSettings && { widgetSettings: data.widgetSettings }),
          ...(data.embedCode !== undefined && { embedCode: data.embedCode }),
        },
        include: {
          business: true,
        },
      });

      return {
        success: true,
        data: widgetConfig as WidgetConfig,
      };
    } catch (error) {
      return handleDatabaseError(error, 'update widget configuration');
    }
  }

  /**
   * Delete widget configuration
   */
  async delete(id: string): Promise<DatabaseResult<boolean>> {
    try {
      await this.prisma.widgetConfig.delete({
        where: { id },
      });

      return {
        success: true,
        data: true,
      };
    } catch (error) {
      return handleDatabaseError(error, 'delete widget configuration');
    }
  }

  /**
   * Get all widget configurations with pagination
   */
  async findAll(pagination?: PaginationParams): Promise<DatabaseResult<PaginatedResponse<WidgetConfig>>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const skip = (page - 1) * limit;

      const [widgetConfigs, total] = await Promise.all([
        this.prisma.widgetConfig.findMany({
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            business: true,
          },
        }),
        this.prisma.widgetConfig.count(),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: {
          data: widgetConfigs as WidgetConfig[],
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return handleDatabaseError(error, 'find all widget configurations');
    }
  }

  /**
   * Find widget configurations by template type
   */
  async findByTemplateType(
    templateType: string,
    pagination?: PaginationParams
  ): Promise<DatabaseResult<PaginatedResponse<WidgetConfig>>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const skip = (page - 1) * limit;

      const [widgetConfigs, total] = await Promise.all([
        this.prisma.widgetConfig.findMany({
          where: { templateType },
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            business: true,
          },
        }),
        this.prisma.widgetConfig.count({
          where: { templateType },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: {
          data: widgetConfigs as WidgetConfig[],
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return handleDatabaseError(error, 'find widget configurations by template type');
    }
  }

  /**
   * Count widget configurations by business ID
   */
  async countByBusinessId(businessId: string): Promise<DatabaseResult<number>> {
    try {
      const count = await this.prisma.widgetConfig.count({
        where: { businessId },
      });

      return {
        success: true,
        data: count,
      };
    } catch (error) {
      return handleDatabaseError(error, 'count widget configurations by business ID');
    }
  }
}