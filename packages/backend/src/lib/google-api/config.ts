import { z } from 'zod';

// Environment configuration schema
const GoogleApiConfigSchema = z.object({
  placesApiKey: z.string().min(1, 'Google Places API key is required'),
  myBusinessApiKey: z.string().min(1, 'Google My Business API key is required'),
  baseUrl: z.string().url().default('https://maps.googleapis.com'),
  myBusinessBaseUrl: z.string().url().default('https://mybusinessbusinessinformation.googleapis.com'),
  timeout: z.number().default(10000),
  retryAttempts: z.number().default(3),
  retryDelay: z.number().default(1000),
});

export type GoogleApiConfig = z.infer<typeof GoogleApiConfigSchema>;

// Load and validate configuration from environment
export function loadGoogleApiConfig(): GoogleApiConfig {
  const config = {
    placesApiKey: process.env.GOOGLE_PLACES_API_KEY,
    myBusinessApiKey: process.env.GOOGLE_MY_BUSINESS_API_KEY,
    baseUrl: process.env.GOOGLE_API_BASE_URL,
    myBusinessBaseUrl: process.env.GOOGLE_MY_BUSINESS_BASE_URL,
    timeout: process.env.GOOGLE_API_TIMEOUT ? parseInt(process.env.GOOGLE_API_TIMEOUT) : undefined,
    retryAttempts: process.env.GOOGLE_API_RETRY_ATTEMPTS ? parseInt(process.env.GOOGLE_API_RETRY_ATTEMPTS) : undefined,
    retryDelay: process.env.GOOGLE_API_RETRY_DELAY ? parseInt(process.env.GOOGLE_API_RETRY_DELAY) : undefined,
  };

  try {
    return GoogleApiConfigSchema.parse(config);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingFields = error.errors.map(err => err.path.join('.')).join(', ');
      throw new Error(`Google API configuration validation failed. Missing or invalid fields: ${missingFields}`);
    }
    throw error;
  }
}

// API endpoints configuration
export const GOOGLE_API_ENDPOINTS = {
  PLACES: {
    FIND_PLACE: '/maps/api/place/findplacefromtext/json',
    PLACE_DETAILS: '/maps/api/place/details/json',
    NEARBY_SEARCH: '/maps/api/place/nearbysearch/json',
    TEXT_SEARCH: '/maps/api/place/textsearch/json',
  },
  MY_BUSINESS: {
    LOCATIONS: '/v1/locations',
    REVIEWS: '/v1/locations/{locationId}/reviews',
  },
} as const;