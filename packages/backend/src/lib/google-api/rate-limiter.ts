import { RateLimiterRedis, RateLimiterMemory } from 'rate-limiter-flexible';
import Redis from 'redis';
import { logger } from '../logger.js';

export interface RateLimiterConfig {
  keyPrefix: string;
  points: number; // Number of requests
  duration: number; // Per duration in seconds
  blockDuration: number; // Block for duration in seconds
  execEvenly?: boolean; // Spread requests evenly across duration
}

export class GoogleApiRateLimiter {
  private placesLimiter: RateLimiterRedis | RateLimiterMemory;
  private myBusinessLimiter: RateLimiterRedis | RateLimiterMemory;
  private redisClient?: Redis.RedisClientType;

  constructor(redisClient?: Redis.RedisClientType) {
    this.redisClient = redisClient;

    // Google Places API rate limits (adjust based on your quota)
    const placesConfig: RateLimiterConfig = {
      keyPrefix: 'google_places_api',
      points: 100, // 100 requests
      duration: 60, // per 60 seconds
      blockDuration: 60, // block for 60 seconds if limit exceeded
      execEvenly: true,
    };

    // Google My Business API rate limits (adjust based on your quota)
    const myBusinessConfig: RateLimiterConfig = {
      keyPrefix: 'google_mybusiness_api',
      points: 50, // 50 requests
      duration: 60, // per 60 seconds
      blockDuration: 60, // block for 60 seconds if limit exceeded
      execEvenly: true,
    };

    if (redisClient) {
      this.placesLimiter = new RateLimiterRedis({
        storeClient: redisClient,
        ...placesConfig,
      });

      this.myBusinessLimiter = new RateLimiterRedis({
        storeClient: redisClient,
        ...myBusinessConfig,
      });
    } else {
      // Fallback to memory-based rate limiter
      logger.warn('Using memory-based rate limiter. Consider using Redis for production.');
      
      this.placesLimiter = new RateLimiterMemory(placesConfig);
      this.myBusinessLimiter = new RateLimiterMemory(myBusinessConfig);
    }
  }

  async checkPlacesApiLimit(key: string = 'default'): Promise<void> {
    try {
      await this.placesLimiter.consume(key);
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      logger.warn(`Google Places API rate limit exceeded. Retry after ${secs} seconds.`);
      throw new Error(`Rate limit exceeded. Retry after ${secs} seconds.`);
    }
  }

  async checkMyBusinessApiLimit(key: string = 'default'): Promise<void> {
    try {
      await this.myBusinessLimiter.consume(key);
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      logger.warn(`Google My Business API rate limit exceeded. Retry after ${secs} seconds.`);
      throw new Error(`Rate limit exceeded. Retry after ${secs} seconds.`);
    }
  }

  async getRemainingPoints(api: 'places' | 'mybusiness', key: string = 'default'): Promise<number> {
    const limiter = api === 'places' ? this.placesLimiter : this.myBusinessLimiter;
    const res = await limiter.get(key);
    return res ? res.remainingPoints || 0 : 0;
  }

  async resetLimits(api?: 'places' | 'mybusiness', key: string = 'default'): Promise<void> {
    if (!api || api === 'places') {
      await this.placesLimiter.delete(key);
    }
    if (!api || api === 'mybusiness') {
      await this.myBusinessLimiter.delete(key);
    }
  }
}