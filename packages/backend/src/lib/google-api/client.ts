import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { GoogleApiConfig, GOOGLE_API_ENDPOINTS } from './config.js';
import { logger } from '../logger.js';

export interface GoogleApiError extends Error {
  status?: string;
  code?: number;
  details?: any;
}

export class GoogleApiClient {
  private placesClient: AxiosInstance;
  private myBusinessClient: AxiosInstance;
  private config: GoogleApiConfig;

  constructor(config: GoogleApiConfig) {
    this.config = config;
    
    // Initialize Places API client
    this.placesClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Initialize My Business API client
    this.myBusinessClient = axios.create({
      baseURL: config.myBusinessBaseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.myBusinessApiKey}`,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptors for logging
    this.placesClient.interceptors.request.use(
      (config) => {
        logger.debug('Google Places API request', {
          url: config.url,
          method: config.method,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('Google Places API request error', error);
        return Promise.reject(error);
      }
    );

    this.myBusinessClient.interceptors.request.use(
      (config) => {
        logger.debug('Google My Business API request', {
          url: config.url,
          method: config.method,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('Google My Business API request error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptors for error handling
    this.placesClient.interceptors.response.use(
      (response) => this.handleSuccessResponse(response),
      (error) => this.handleErrorResponse(error, 'Places API')
    );

    this.myBusinessClient.interceptors.response.use(
      (response) => this.handleSuccessResponse(response),
      (error) => this.handleErrorResponse(error, 'My Business API')
    );
  }

  private handleSuccessResponse(response: AxiosResponse): AxiosResponse {
    logger.debug('Google API response', {
      status: response.status,
      url: response.config.url,
    });
    return response;
  }

  private handleErrorResponse(error: any, apiName: string): Promise<never> {
    const googleError: GoogleApiError = new Error(`${apiName} error`);
    
    if (error.response) {
      // Server responded with error status
      googleError.code = error.response.status;
      googleError.status = error.response.data?.status || 'UNKNOWN_ERROR';
      googleError.details = error.response.data;
      googleError.message = error.response.data?.error_message || `${apiName} returned ${error.response.status}`;
    } else if (error.request) {
      // Request was made but no response received
      googleError.message = `${apiName} network error`;
      googleError.status = 'NETWORK_ERROR';
    } else {
      // Something else happened
      googleError.message = error.message || `${apiName} unknown error`;
      googleError.status = 'UNKNOWN_ERROR';
    }

    logger.error(`${apiName} error`, {
      message: googleError.message,
      status: googleError.status,
      code: googleError.code,
      details: googleError.details,
    });

    return Promise.reject(googleError);
  }

  // Places API methods
  async findPlace(input: string, inputType: 'textquery' | 'phonenumber' = 'textquery', fields: string[] = ['place_id', 'name', 'formatted_address']): Promise<any> {
    const params = {
      input,
      inputtype: inputType,
      fields: fields.join(','),
      key: this.config.placesApiKey,
    };

    const response = await this.placesClient.get(GOOGLE_API_ENDPOINTS.PLACES.FIND_PLACE, { params });
    return response.data;
  }

  async getPlaceDetails(placeId: string, fields: string[] = ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total', 'reviews']): Promise<any> {
    const params = {
      place_id: placeId,
      fields: fields.join(','),
      key: this.config.placesApiKey,
    };

    const response = await this.placesClient.get(GOOGLE_API_ENDPOINTS.PLACES.PLACE_DETAILS, { params });
    return response.data;
  }

  async searchPlaces(query: string, location?: string, radius?: number): Promise<any> {
    const params: any = {
      query,
      key: this.config.placesApiKey,
    };

    if (location) {
      params.location = location;
    }
    if (radius) {
      params.radius = radius;
    }

    const response = await this.placesClient.get(GOOGLE_API_ENDPOINTS.PLACES.TEXT_SEARCH, { params });
    return response.data;
  }

  // My Business API methods
  async getBusinessLocations(): Promise<any> {
    const response = await this.myBusinessClient.get(GOOGLE_API_ENDPOINTS.MY_BUSINESS.LOCATIONS);
    return response.data;
  }

  async getBusinessReviews(locationId: string): Promise<any> {
    const endpoint = GOOGLE_API_ENDPOINTS.MY_BUSINESS.REVIEWS.replace('{locationId}', locationId);
    const response = await this.myBusinessClient.get(endpoint);
    return response.data;
  }

  // Health check method
  async healthCheck(): Promise<{ placesApi: boolean; myBusinessApi: boolean }> {
    const results = {
      placesApi: false,
      myBusinessApi: false,
    };

    try {
      // Test Places API with a simple request
      await this.findPlace('test', 'textquery', ['place_id']);
      results.placesApi = true;
    } catch (error) {
      logger.warn('Places API health check failed', error);
    }

    try {
      // Test My Business API with a simple request
      await this.getBusinessLocations();
      results.myBusinessApi = true;
    } catch (error) {
      logger.warn('My Business API health check failed', error);
    }

    return results;
  }
}

// Singleton instance
let googleApiClient: GoogleApiClient | null = null;

export function getGoogleApiClient(config?: GoogleApiConfig): GoogleApiClient {
  if (!googleApiClient && config) {
    googleApiClient = new GoogleApiClient(config);
  }
  
  if (!googleApiClient) {
    throw new Error('Google API client not initialized. Call with config first.');
  }
  
  return googleApiClient;
}

export function resetGoogleApiClient(): void {
  googleApiClient = null;
}