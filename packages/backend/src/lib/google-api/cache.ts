import Redis from 'redis';
import { logger } from '../logger.js';

export interface CacheConfig {
  defaultTtl: number; // Default TTL in seconds
  keyPrefix: string;
}

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
}

export class GoogleApiCache {
  private redisClient?: Redis.RedisClientType;
  private memoryCache: Map<string, CacheEntry>;
  private config: CacheConfig;

  constructor(redisClient?: Redis.RedisClientType, config?: Partial<CacheConfig>) {
    this.redisClient = redisClient;
    this.memoryCache = new Map();
    this.config = {
      defaultTtl: 3600, // 1 hour default
      keyPrefix: 'google_api_cache',
      ...config,
    };

    if (!redisClient) {
      logger.warn('Using memory-based cache. Consider using Redis for production.');
      // Clean up memory cache periodically
      setInterval(() => this.cleanupMemoryCache(), 300000); // Every 5 minutes
    }
  }

  private generateKey(namespace: string, identifier: string): string {
    return `${this.config.keyPrefix}:${namespace}:${identifier}`;
  }

  private cleanupMemoryCache(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > entry.ttl * 1000) {
        this.memoryCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.debug(`Cleaned up ${cleaned} expired cache entries from memory`);
    }
  }

  async get<T = any>(namespace: string, identifier: string): Promise<T | null> {
    const key = this.generateKey(namespace, identifier);

    try {
      if (this.redisClient) {
        const cached = await this.redisClient.get(key);
        if (cached) {
          const entry: CacheEntry<T> = JSON.parse(cached);
          logger.debug(`Cache hit for ${key}`);
          return entry.data;
        }
      } else {
        const entry = this.memoryCache.get(key);
        if (entry) {
          const now = Date.now();
          if (now - entry.timestamp < entry.ttl * 1000) {
            logger.debug(`Memory cache hit for ${key}`);
            return entry.data;
          } else {
            this.memoryCache.delete(key);
          }
        }
      }

      logger.debug(`Cache miss for ${key}`);
      return null;
    } catch (error) {
      logger.error(`Cache get error for ${key}:`, error);
      return null;
    }
  }

  async set<T = any>(namespace: string, identifier: string, data: T, ttl?: number): Promise<void> {
    const key = this.generateKey(namespace, identifier);
    const actualTtl = ttl || this.config.defaultTtl;

    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl: actualTtl,
      };

      if (this.redisClient) {
        await this.redisClient.setEx(key, actualTtl, JSON.stringify(entry));
      } else {
        this.memoryCache.set(key, entry);
      }

      logger.debug(`Cached data for ${key} with TTL ${actualTtl}s`);
    } catch (error) {
      logger.error(`Cache set error for ${key}:`, error);
    }
  }

  async delete(namespace: string, identifier: string): Promise<void> {
    const key = this.generateKey(namespace, identifier);

    try {
      if (this.redisClient) {
        await this.redisClient.del(key);
      } else {
        this.memoryCache.delete(key);
      }

      logger.debug(`Deleted cache entry for ${key}`);
    } catch (error) {
      logger.error(`Cache delete error for ${key}:`, error);
    }
  }

  async clear(namespace?: string): Promise<void> {
    try {
      if (this.redisClient) {
        const pattern = namespace 
          ? `${this.config.keyPrefix}:${namespace}:*`
          : `${this.config.keyPrefix}:*`;
        
        const keys = await this.redisClient.keys(pattern);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
          logger.debug(`Cleared ${keys.length} cache entries for pattern ${pattern}`);
        }
      } else {
        if (namespace) {
          const prefix = `${this.config.keyPrefix}:${namespace}:`;
          for (const key of this.memoryCache.keys()) {
            if (key.startsWith(prefix)) {
              this.memoryCache.delete(key);
            }
          }
        } else {
          this.memoryCache.clear();
        }
        logger.debug(`Cleared memory cache for namespace ${namespace || 'all'}`);
      }
    } catch (error) {
      logger.error(`Cache clear error:`, error);
    }
  }

  // Specific cache methods for Google API data
  async getPlaceDetails(placeId: string): Promise<any | null> {
    return this.get('place_details', placeId);
  }

  async setPlaceDetails(placeId: string, data: any, ttl: number = 3600): Promise<void> {
    return this.set('place_details', placeId, data, ttl);
  }

  async getPlaceSearch(query: string): Promise<any | null> {
    const searchKey = Buffer.from(query).toString('base64');
    return this.get('place_search', searchKey);
  }

  async setPlaceSearch(query: string, data: any, ttl: number = 1800): Promise<void> {
    const searchKey = Buffer.from(query).toString('base64');
    return this.set('place_search', searchKey, data, ttl);
  }

  async getBusinessReviews(locationId: string): Promise<any | null> {
    return this.get('business_reviews', locationId);
  }

  async setBusinessReviews(locationId: string, data: any, ttl: number = 1800): Promise<void> {
    return this.set('business_reviews', locationId, data, ttl);
  }

  // Cache statistics
  async getStats(): Promise<{ memoryEntries?: number; redisConnected?: boolean }> {
    const stats: { memoryEntries?: number; redisConnected?: boolean } = {};

    if (!this.redisClient) {
      stats.memoryEntries = this.memoryCache.size;
    } else {
      try {
        await this.redisClient.ping();
        stats.redisConnected = true;
      } catch {
        stats.redisConnected = false;
      }
    }

    return stats;
  }
}