import { getGoogleApiClient, GoogleApiClient } from '../google-api/client.js';
import type { GoogleApiError } from '../google-api/client.js';
import { loadGoogleApiConfig } from '../google-api/config.js';
import { BusinessRepository } from '../repositories/business.repository.js';
import {
  Business,
  BusinessCreate,
  BusinessSearchRequest,
  BusinessSearchResponse
} from '../../types/index.js';
import { logger } from '../logger.js';

export interface GooglePlaceResult {
  place_id: string;
  name: string;
  formatted_address?: string;
  rating?: number;
  user_ratings_total?: number;
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
}

export interface GoogleMapsUrlInfo {
  placeId?: string;
  query?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export class BusinessSearchService {
  private googleApiClient: GoogleApiClient;
  private businessRepository: BusinessRepository;

  constructor() {
    const config = loadGoogleApiConfig();
    this.googleApiClient = getGoogleApiClient(config);
    this.businessRepository = new BusinessRepository();
  }

  /**
   * Search for businesses using Google Places API
   */
  async searchBusinesses(request: BusinessSearchRequest): Promise<BusinessSearchResponse> {
    try {
      logger.info('Starting business search', { query: request.query, location: request.location });

      let searchResults: GooglePlaceResult[] = [];

      // If Google Maps URL is provided, try to extract place ID first
      if (request.googleMapsUrl) {
        const urlInfo = this.parseGoogleMapsUrl(request.googleMapsUrl);
        if (urlInfo.placeId) {
          logger.info('Found place ID in Google Maps URL', { placeId: urlInfo.placeId });
          const placeDetails = await this.getPlaceDetailsByPlaceId(urlInfo.placeId);
          if (placeDetails) {
            searchResults = [placeDetails];
          }
        } else if (urlInfo.query) {
          // Use extracted query from URL
          searchResults = await this.performTextSearch(urlInfo.query, request.location);
        }
      }

      // If no results from URL parsing, perform regular text search
      if (searchResults.length === 0) {
        searchResults = await this.performTextSearch(request.query, request.location);
      }

      // Convert Google Places results to Business objects
      const businesses = await this.convertToBusinessObjects(searchResults);

      logger.info('Business search completed', {
        query: request.query,
        resultsCount: businesses.length
      });

      return {
        businesses,
        totalResults: businesses.length,
      };

    } catch (error) {
      logger.error('Business search failed', {
        query: request.query,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      if (error && typeof error === 'object' && 'message' in error) {
        throw new Error(`Google API error: ${(error as Error).message}`);
      }

      throw new Error('Business search failed');
    }
  }

  /**
   * Parse Google Maps URL to extract place ID or search query
   */
  private parseGoogleMapsUrl(url: string): GoogleMapsUrlInfo {
    try {
      const urlObj = new URL(url);
      const result: GoogleMapsUrlInfo = {};

      // Check for place ID in various URL formats
      const placeIdMatch = url.match(/place_id=([^&]+)/);
      if (placeIdMatch) {
        result.placeId = placeIdMatch[1];
        return result;
      }

      // Check for place ID in /place/ URLs
      const placePathMatch = url.match(/\/place\/[^\/]+\/data=.*!1s([^!]+)/);
      if (placePathMatch) {
        result.placeId = placePathMatch[1];
        return result;
      }

      // Extract search query from URL path
      const pathMatch = url.match(/\/place\/([^\/]+)/);
      if (pathMatch) {
        result.query = decodeURIComponent(pathMatch[1].replace(/\+/g, ' '));
        return result;
      }

      // Extract coordinates if present
      const coordsMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
      if (coordsMatch) {
        result.coordinates = {
          lat: parseFloat(coordsMatch[1]),
          lng: parseFloat(coordsMatch[2]),
        };
      }

      // Extract query from search parameter
      const searchParams = urlObj.searchParams;
      const query = searchParams.get('q') || searchParams.get('query');
      if (query) {
        result.query = query;
      }

      return result;
    } catch (error) {
      logger.warn('Failed to parse Google Maps URL', { url, error });
      return {};
    }
  }

  /**
   * Get place details by place ID
   */
  private async getPlaceDetailsByPlaceId(placeId: string): Promise<GooglePlaceResult | null> {
    try {
      const fields = [
        'place_id',
        'name',
        'formatted_address',
        'rating',
        'user_ratings_total',
        'photos'
      ];

      const response = await this.googleApiClient.getPlaceDetails(placeId, fields);

      if (response.status === 'OK' && response.result) {
        return response.result;
      }

      logger.warn('Place details not found', { placeId, status: response.status });
      return null;
    } catch (error) {
      logger.error('Failed to get place details', { placeId, error });
      return null;
    }
  }

  /**
   * Perform text search using Google Places API
   */
  private async performTextSearch(query: string, location?: string): Promise<GooglePlaceResult[]> {
    try {
      // First try with findPlace for exact matches
      const findPlaceResponse = await this.googleApiClient.findPlace(
        query,
        'textquery',
        ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total']
      );

      if (findPlaceResponse.status === 'OK' && findPlaceResponse.candidates?.length > 0) {
        // Get detailed information for each candidate
        const detailedResults = await Promise.all(
          findPlaceResponse.candidates.map(async (candidate: any) => {
            const details = await this.getPlaceDetailsByPlaceId(candidate.place_id);
            return details || candidate;
          })
        );

        return detailedResults.filter(Boolean);
      }

      // If findPlace doesn't return results, try text search
      const textSearchResponse = await this.googleApiClient.searchPlaces(query, location);

      if (textSearchResponse.status === 'OK' && textSearchResponse.results?.length > 0) {
        return textSearchResponse.results.slice(0, 10); // Limit to 10 results
      }

      logger.info('No results found for search query', { query, location });
      return [];
    } catch (error) {
      logger.error('Text search failed', { query, location, error });
      throw error;
    }
  }

  /**
   * Convert Google Places results to Business objects
   */
  private async convertToBusinessObjects(googleResults: GooglePlaceResult[]): Promise<Business[]> {
    const businesses: Business[] = [];

    for (const result of googleResults) {
      try {
        // TEMPORARY FIX: Skip database operations and return Google API results directly
        // This bypasses database connection issues while keeping the API functional

        const business: Business = {
          id: result.place_id, // Use place_id as temporary ID
          placeId: result.place_id,
          name: result.name,
          address: result.formatted_address || undefined,
          rating: result.rating || undefined,
          reviewCount: result.user_ratings_total || undefined,
          photoUrl: this.getPhotoUrl(result.photos?.[0]) || undefined,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        businesses.push(business);

        // TODO: Re-enable database operations once Supabase connection is fixed
        /*
        // Check if business already exists in database
        const existingResult = await this.businessRepository.findByPlaceId(result.place_id);
        
        if (existingResult.success && existingResult.data) {
          // Update existing business with latest data
          const updateData: Partial<BusinessCreate> = {
            name: result.name,
            address: result.formatted_address,
            rating: result.rating,
            reviewCount: result.user_ratings_total,
            photoUrl: this.getPhotoUrl(result.photos?.[0]),
          };

          const updateResult = await this.businessRepository.update(
            existingResult.data.id,
            updateData
          );

          if (updateResult.success) {
            businesses.push(updateResult.data);
          }
        } else {
          // Create new business
          const businessData: BusinessCreate = {
            placeId: result.place_id,
            name: result.name,
            address: result.formatted_address,
            rating: result.rating,
            reviewCount: result.user_ratings_total,
            photoUrl: this.getPhotoUrl(result.photos?.[0]),
          };

          const createResult = await this.businessRepository.create(businessData);
          
          if (createResult.success) {
            businesses.push(createResult.data);
          }
        }
        */
      } catch (error) {
        logger.error('Failed to process business result', {
          placeId: result.place_id,
          name: result.name,
          error
        });
        // Continue processing other results even if one fails
      }
    }

    return businesses;
  }

  /**
   * Generate photo URL from Google Places photo reference
   */
  private getPhotoUrl(photo?: { photo_reference: string; height: number; width: number }): string | undefined {
    if (!photo?.photo_reference) {
      return undefined;
    }

    const config = loadGoogleApiConfig();
    const maxWidth = Math.min(photo.width, 400); // Limit photo size

    return `${config.baseUrl}/maps/api/place/photo?maxwidth=${maxWidth}&photo_reference=${photo.photo_reference}&key=${config.placesApiKey}`;
  }

  /**
   * Validate search request
   */
  static validateSearchRequest(request: any): BusinessSearchRequest {
    if (!request.query || typeof request.query !== 'string' || request.query.trim().length === 0) {
      throw new Error('Search query is required and must be a non-empty string');
    }

    if (request.location && typeof request.location !== 'string') {
      throw new Error('Location must be a string');
    }

    if (request.googleMapsUrl) {
      try {
        new URL(request.googleMapsUrl);
      } catch {
        throw new Error('Invalid Google Maps URL format');
      }
    }

    return {
      query: request.query.trim(),
      location: request.location?.trim(),
      googleMapsUrl: request.googleMapsUrl?.trim(),
    };
  }
}
