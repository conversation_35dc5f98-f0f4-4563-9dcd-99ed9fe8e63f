import { GoogleApiClient, getGoogleApiClient, GoogleApiError } from '../google-api/client.js';
import { GoogleApiCache } from '../google-api/cache.js';
import { loadGoogleApiConfig } from '../google-api/config.js';
import { ReviewsCacheRepository } from '../repositories/reviews-cache.repository.js';
import { BusinessRepository } from '../repositories/business.repository.js';
import { 
  Review, 
  BusinessInfo, 
  ReviewsResponse, 
  ReviewsCacheData,
  ReviewSchema,
  BusinessInfoSchema,
  ReviewsResponseSchema,
  ReviewsCacheDataSchema
} from '../../types/index.js';
import { logger } from '../logger.js';

export interface ReviewsFetchOptions {
  forceRefresh?: boolean;
  maxReviews?: number;
  minRating?: number;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low';
}

export interface GoogleReviewData {
  author_name: string;
  author_url?: string;
  profile_photo_url?: string;
  rating: number;
  relative_time_description: string;
  text: string;
  time: number;
}

export interface GooglePlaceDetailsResponse {
  result: {
    place_id: string;
    name: string;
    formatted_address?: string;
    rating?: number;
    user_ratings_total?: number;
    reviews?: GoogleReviewData[];
    photos?: Array<{
      photo_reference: string;
      height: number;
      width: number;
    }>;
  };
  status: string;
}

export class ReviewsService {
  private googleApiClient: GoogleApiClient;
  private googleApiCache: GoogleApiCache;
  private reviewsCacheRepository: ReviewsCacheRepository;
  private businessRepository: BusinessRepository;
  private readonly DEFAULT_CACHE_TTL = 3600; // 1 hour
  private readonly REVIEWS_CACHE_TTL = 1800; // 30 minutes

  constructor(googleApiCache?: GoogleApiCache) {
    const config = loadGoogleApiConfig();
    this.googleApiClient = getGoogleApiClient(config);
    this.googleApiCache = googleApiCache || new GoogleApiCache();
    this.reviewsCacheRepository = new ReviewsCacheRepository();
    this.businessRepository = new BusinessRepository();
  }

  /**
   * Get reviews for a business by place ID
   */
  async getReviewsByPlaceId(
    placeId: string, 
    options: ReviewsFetchOptions = {}
  ): Promise<ReviewsResponse> {
    try {
      logger.info('Fetching reviews for place', { placeId, options });

      // First, get or create business record
      const business = await this.getOrCreateBusiness(placeId);
      
      // Check cache first unless force refresh is requested
      if (!options.forceRefresh) {
        const cachedReviews = await this.getCachedReviews(business.id);
        if (cachedReviews) {
          logger.info('Returning cached reviews', { placeId, businessId: business.id });
          return this.formatReviewsResponse(cachedReviews, options);
        }
      }

      // Fetch fresh reviews from Google API
      const freshReviews = await this.fetchReviewsFromGoogle(placeId);
      
      // Cache the fresh reviews
      await this.cacheReviews(business.id, freshReviews);

      logger.info('Fetched and cached fresh reviews', { 
        placeId, 
        businessId: business.id, 
        reviewCount: freshReviews.reviews.length 
      });

      return this.formatReviewsResponse(freshReviews, options);

    } catch (error) {
      logger.error('Failed to get reviews', { placeId, error });
      
      // Try to return cached data as fallback
      try {
        const business = await this.getBusinessByPlaceId(placeId);
        if (business) {
          const cachedReviews = await this.getCachedReviews(business.id, true); // Include expired
          if (cachedReviews) {
            logger.warn('Returning expired cached reviews as fallback', { placeId });
            return this.formatReviewsResponse(cachedReviews, options);
          }
        }
      } catch (fallbackError) {
        logger.error('Fallback to cached data also failed', { placeId, fallbackError });
      }

      throw new Error(`Failed to fetch reviews for place ${placeId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get reviews for a business by business ID
   */
  async getReviewsByBusinessId(
    businessId: string, 
    options: ReviewsFetchOptions = {}
  ): Promise<ReviewsResponse> {
    try {
      const businessResult = await this.businessRepository.findById(businessId);
      if (!businessResult.success || !businessResult.data) {
        throw new Error(`Business not found: ${businessId}`);
      }

      return this.getReviewsByPlaceId(businessResult.data.placeId, options);
    } catch (error) {
      logger.error('Failed to get reviews by business ID', { businessId, error });
      throw error;
    }
  }

  /**
   * Refresh reviews for multiple businesses (background job)
   */
  async refreshReviewsForBusinesses(businessIds: string[]): Promise<{
    successful: string[];
    failed: Array<{ businessId: string; error: string }>;
  }> {
    const results = {
      successful: [] as string[],
      failed: [] as Array<{ businessId: string; error: string }>
    };

    logger.info('Starting bulk reviews refresh', { businessCount: businessIds.length });

    for (const businessId of businessIds) {
      try {
        await this.getReviewsByBusinessId(businessId, { forceRefresh: true });
        results.successful.push(businessId);
        
        // Add delay to respect rate limits
        await this.delay(100);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.failed.push({ businessId, error: errorMessage });
        logger.error('Failed to refresh reviews for business', { businessId, error: errorMessage });
      }
    }

    logger.info('Completed bulk reviews refresh', {
      successful: results.successful.length,
      failed: results.failed.length
    });

    return results;
  }

  /**
   * Get businesses that need review refresh
   */
  async getBusinessesNeedingRefresh(bufferMinutes: number = 30): Promise<string[]> {
    try {
      const result = await this.reviewsCacheRepository.findNeedingRefresh(bufferMinutes);
      if (result.success && result.data) {
        return result.data.map(cache => cache.businessId);
      }
      return [];
    } catch (error) {
      logger.error('Failed to get businesses needing refresh', { error });
      return [];
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<number> {
    try {
      const result = await this.reviewsCacheRepository.deleteExpired();
      if (result.success && typeof result.data === 'number') {
        logger.info('Cleaned up expired cache entries', { count: result.data });
        return result.data;
      }
      return 0;
    } catch (error) {
      logger.error('Failed to cleanup expired cache', { error });
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    database: { total: number; expired: number; valid: number };
    redis: { memoryEntries?: number; redisConnected?: boolean };
  }> {
    try {
      const [dbStats, redisStats] = await Promise.all([
        this.reviewsCacheRepository.getStats(),
        this.googleApiCache.getStats()
      ]);

      return {
        database: dbStats.success ? dbStats.data : { total: 0, expired: 0, valid: 0 },
        redis: redisStats
      };
    } catch (error) {
      logger.error('Failed to get cache stats', { error });
      return {
        database: { total: 0, expired: 0, valid: 0 },
        redis: {}
      };
    }
  }

  /**
   * Get or create business record
   */
  private async getOrCreateBusiness(placeId: string): Promise<{ id: string; placeId: string; name: string }> {
    // First try to find existing business
    const existingResult = await this.businessRepository.findByPlaceId(placeId);
    if (existingResult.success && existingResult.data) {
      return existingResult.data;
    }

    // If not found, fetch basic info from Google and create
    try {
      const placeDetails = await this.googleApiClient.getPlaceDetails(
        placeId, 
        ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total']
      );

      if (placeDetails.status === 'OK' && placeDetails.result) {
        const createResult = await this.businessRepository.create({
          placeId: placeDetails.result.place_id,
          name: placeDetails.result.name,
          address: placeDetails.result.formatted_address,
          rating: placeDetails.result.rating,
          reviewCount: placeDetails.result.user_ratings_total,
        });

        if (createResult.success && createResult.data) {
          return createResult.data;
        }
      }
    } catch (error) {
      logger.error('Failed to create business from place details', { placeId, error });
    }

    throw new Error(`Could not get or create business for place ID: ${placeId}`);
  }

  /**
   * Get business by place ID
   */
  private async getBusinessByPlaceId(placeId: string): Promise<{ id: string; placeId: string; name: string } | null> {
    const result = await this.businessRepository.findByPlaceId(placeId);
    return result.success ? result.data : null;
  }

  /**
   * Get cached reviews from database
   */
  private async getCachedReviews(businessId: string, includeExpired: boolean = false): Promise<ReviewsCacheData | null> {
    try {
      const result = includeExpired 
        ? await this.reviewsCacheRepository.findByBusinessId(businessId)
        : await this.reviewsCacheRepository.findValidByBusinessId(businessId);

      if (result.success && result.data) {
        return result.data.reviewsData;
      }
      return null;
    } catch (error) {
      logger.error('Failed to get cached reviews', { businessId, error });
      return null;
    }
  }

  /**
   * Fetch reviews from Google API
   */
  private async fetchReviewsFromGoogle(placeId: string): Promise<ReviewsCacheData> {
    try {
      // Check Google API cache first
      const cachedData = await this.googleApiCache.getPlaceDetails(placeId);
      let placeDetails: GooglePlaceDetailsResponse;

      if (cachedData) {
        placeDetails = cachedData;
        logger.debug('Using cached Google API data', { placeId });
      } else {
        // Fetch from Google API
        placeDetails = await this.googleApiClient.getPlaceDetails(
          placeId,
          ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total', 'reviews', 'photos']
        );

        // Cache the API response
        await this.googleApiCache.setPlaceDetails(placeId, placeDetails, this.DEFAULT_CACHE_TTL);
      }

      if (placeDetails.status !== 'OK' || !placeDetails.result) {
        throw new Error(`Google API returned status: ${placeDetails.status}`);
      }

      // Convert Google reviews to our format
      const reviews = this.convertGoogleReviews(placeDetails.result.reviews || []);
      const businessInfo = this.convertGoogleBusinessInfo(placeDetails.result);

      const now = new Date();
      const expiresAt = new Date(now.getTime() + this.REVIEWS_CACHE_TTL * 1000);

      const reviewsCacheData: ReviewsCacheData = {
        reviews,
        businessInfo,
        totalReviews: placeDetails.result.user_ratings_total || reviews.length,
        averageRating: placeDetails.result.rating || 0,
        lastUpdated: now,
        expiresAt
      };

      // Validate the data
      const validationResult = ReviewsCacheDataSchema.safeParse(reviewsCacheData);
      if (!validationResult.success) {
        logger.error('Invalid reviews cache data', { 
          placeId, 
          errors: validationResult.error.errors 
        });
        throw new Error('Invalid reviews data format');
      }

      return reviewsCacheData;

    } catch (error) {
      logger.error('Failed to fetch reviews from Google', { placeId, error });
      throw error;
    }
  }

  /**
   * Cache reviews in database
   */
  private async cacheReviews(businessId: string, reviewsData: ReviewsCacheData): Promise<void> {
    try {
      await this.reviewsCacheRepository.upsert({
        businessId,
        reviewsData,
        lastFetched: reviewsData.lastUpdated,
        expiresAt: reviewsData.expiresAt
      });
    } catch (error) {
      logger.error('Failed to cache reviews', { businessId, error });
      // Don't throw here as this is not critical for the main flow
    }
  }

  /**
   * Convert Google reviews to our format
   */
  private convertGoogleReviews(googleReviews: GoogleReviewData[]): Review[] {
    return googleReviews.map((googleReview, index) => {
      const review: Review = {
        id: `${googleReview.time}_${index}`, // Generate ID from timestamp and index
        authorName: googleReview.author_name,
        authorPhotoUrl: googleReview.profile_photo_url,
        rating: googleReview.rating,
        text: googleReview.text || '',
        publishedDate: new Date(googleReview.time * 1000), // Convert Unix timestamp
        isVerified: true // Google reviews are considered verified
      };

      // Validate the review
      const validationResult = ReviewSchema.safeParse(review);
      if (!validationResult.success) {
        logger.warn('Invalid review data, using defaults', { 
          review, 
          errors: validationResult.error.errors 
        });
        // Return a minimal valid review
        return {
          id: `invalid_${Date.now()}_${index}`,
          authorName: googleReview.author_name || 'Anonymous',
          rating: Math.max(1, Math.min(5, googleReview.rating || 5)),
          text: googleReview.text || '',
          publishedDate: new Date(),
          isVerified: true
        };
      }

      return review;
    });
  }

  /**
   * Convert Google business info to our format
   */
  private convertGoogleBusinessInfo(googleResult: any): BusinessInfo {
    const businessInfo: BusinessInfo = {
      placeId: googleResult.place_id,
      name: googleResult.name,
      address: googleResult.formatted_address,
      rating: googleResult.rating,
      reviewCount: googleResult.user_ratings_total,
      photoUrl: this.getPhotoUrl(googleResult.photos?.[0])
    };

    // Validate the business info
    const validationResult = BusinessInfoSchema.safeParse(businessInfo);
    if (!validationResult.success) {
      logger.warn('Invalid business info, using minimal data', { 
        businessInfo, 
        errors: validationResult.error.errors 
      });
      // Return minimal valid business info
      return {
        placeId: googleResult.place_id || '',
        name: googleResult.name || 'Unknown Business'
      };
    }

    return businessInfo;
  }

  /**
   * Generate photo URL from Google Places photo reference
   */
  private getPhotoUrl(photo?: { photo_reference: string; height: number; width: number }): string | undefined {
    if (!photo?.photo_reference) {
      return undefined;
    }

    const config = loadGoogleApiConfig();
    const maxWidth = Math.min(photo.width, 400);
    
    return `${config.baseUrl}/maps/api/place/photo?maxwidth=${maxWidth}&photo_reference=${photo.photo_reference}&key=${config.placesApiKey}`;
  }

  /**
   * Format reviews response with filtering and sorting
   */
  private formatReviewsResponse(reviewsData: ReviewsCacheData, options: ReviewsFetchOptions): ReviewsResponse {
    let reviews = [...reviewsData.reviews];

    // Apply rating filter
    if (options.minRating && options.minRating > 0) {
      reviews = reviews.filter(review => review.rating >= options.minRating!);
    }

    // Apply sorting
    switch (options.sortBy) {
      case 'newest':
        reviews.sort((a, b) => b.publishedDate.getTime() - a.publishedDate.getTime());
        break;
      case 'oldest':
        reviews.sort((a, b) => a.publishedDate.getTime() - b.publishedDate.getTime());
        break;
      case 'rating_high':
        reviews.sort((a, b) => b.rating - a.rating);
        break;
      case 'rating_low':
        reviews.sort((a, b) => a.rating - b.rating);
        break;
      default:
        // Default to newest first
        reviews.sort((a, b) => b.publishedDate.getTime() - a.publishedDate.getTime());
    }

    // Apply limit
    if (options.maxReviews && options.maxReviews > 0) {
      reviews = reviews.slice(0, options.maxReviews);
    }

    const response: ReviewsResponse = {
      reviews,
      businessInfo: reviewsData.businessInfo,
      totalReviews: reviewsData.totalReviews,
      averageRating: reviewsData.averageRating,
      lastUpdated: reviewsData.lastUpdated
    };

    // Validate the response
    const validationResult = ReviewsResponseSchema.safeParse(response);
    if (!validationResult.success) {
      logger.error('Invalid reviews response format', { 
        errors: validationResult.error.errors 
      });
      throw new Error('Invalid reviews response format');
    }

    return response;
  }

  /**
   * Utility method to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}