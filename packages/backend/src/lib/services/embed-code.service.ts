import { v4 as uuidv4 } from 'uuid';
import { WidgetConfig } from '../../types/widget.js';

export interface EmbedCodeOptions {
  widgetId: string;
  config: WidgetConfig;
  baseUrl?: string;
}

export interface EmbedCodeResult {
  embedCode: string;
  widgetId: string;
  embedUrl: string;
  previewUrl: string;
}

export class EmbedCodeService {
  private readonly defaultBaseUrl: string;
  private readonly widgetRuntimeUrl: string;

  constructor(
    baseUrl: string = process.env.WIDGET_BASE_URL || 'https://widgets.example.com',
    widgetRuntimeUrl: string = process.env.WIDGET_RUNTIME_URL || 'https://cdn.example.com/widget-runtime.js'
  ) {
    this.defaultBaseUrl = baseUrl;
    this.widgetRuntimeUrl = widgetRuntimeUrl;
  }

  /**
   * Generate a unique widget ID
   */
  generateWidgetId(): string {
    return uuidv4();
  }

  /**
   * Generate HTML embed code for a widget configuration
   */
  generateEmbedCode(options: EmbedCodeOptions): EmbedCodeResult {
    const { widgetId, config, baseUrl = this.defaultBaseUrl } = options;

    const embedUrl = `${baseUrl}/widget/${widgetId}`;
    const previewUrl = `${baseUrl}/preview/${widgetId}`;

    // Create the embed code with configuration data
    const embedCode = this.createEmbedHtml(widgetId, config, embedUrl);

    return {
      embedCode,
      widgetId,
      embedUrl,
      previewUrl,
    };
  }

  /**
   * Generate embed code for an existing widget configuration
   */
  generateEmbedCodeForConfig(config: WidgetConfig, baseUrl?: string): EmbedCodeResult {
    return this.generateEmbedCode({
      widgetId: config.id,
      config,
      baseUrl,
    });
  }

  /**
   * Update embed code for a widget configuration
   */
  updateEmbedCode(config: WidgetConfig, baseUrl?: string): WidgetConfig {
    const embedResult = this.generateEmbedCodeForConfig(config, baseUrl);

    return {
      ...config,
      embedCode: embedResult.embedCode,
      updatedAt: new Date(),
    };
  }

  /**
   * Create the actual HTML embed code
   */
  private createEmbedHtml(widgetId: string, config: WidgetConfig, embedUrl: string): string {
    // Serialize configuration for embedding
    const configData = this.serializeConfig(config);

    // Create the embed HTML
    const embedHtml = `<!-- Google Reviews Widget by ReviewsWidget -->
<div id="reviews-widget-${widgetId}" 
     class="reviews-widget-container" 
     data-widget-id="${widgetId}"
     data-config='${JSON.stringify(configData)}'
     style="width: ${config.stylingConfig.dimensions.width}px; height: ${config.stylingConfig.dimensions.height}px; max-width: 100%;">
  <div class="reviews-widget-loading" style="display: flex; align-items: center; justify-content: center; height: 100%; font-family: Arial, sans-serif; color: #666;">
    Loading reviews...
  </div>
</div>
<script>
(function() {
  if (window.ReviewsWidget) return;
  
  var script = document.createElement('script');
  script.src = '${this.widgetRuntimeUrl}';
  script.async = true;
  script.onload = function() {
    if (window.ReviewsWidget) {
      window.ReviewsWidget.init('${widgetId}');
    }
  };
  document.head.appendChild(script);
})();
</script>`;

    return embedHtml;
  }

  /**
   * Serialize widget configuration for embedding
   */
  private serializeConfig(config: WidgetConfig): any {
    return {
      businessId: config.businessId,
      templateType: config.templateType,
      styling: config.stylingConfig,
      settings: config.widgetSettings,
      // Ensure all styling is included
      colors: config.stylingConfig.colors,
      fonts: config.stylingConfig.fonts,
      dimensions: config.stylingConfig.dimensions,
      spacing: config.stylingConfig.spacing,
    };
  }

  /**
   * Generate a simple iframe embed code (alternative approach)
   */
  generateIframeEmbedCode(widgetId: string, config: WidgetConfig, baseUrl?: string): string {
    const url = baseUrl || this.defaultBaseUrl;
    const iframeSrc = `${url}/embed/${widgetId}`;

    return `<!-- Google Reviews Widget (iframe) -->
<iframe src="${iframeSrc}" 
        width="${config.stylingConfig.dimensions.width}" 
        height="${config.stylingConfig.dimensions.height}"
        frameborder="0" 
        scrolling="no"
        style="max-width: 100%; border: none;"
        title="Google Reviews Widget">
</iframe>`;
  }

  /**
   * Validate embed code format
   */
  validateEmbedCode(embedCode: string): boolean {
    // Basic validation checks
    const hasWidgetContainer = embedCode.includes('reviews-widget-container');
    const hasScript = embedCode.includes('<script>');
    const hasWidgetId = embedCode.includes('data-widget-id=');
    const hasConfig = embedCode.includes('data-config=');

    return hasWidgetContainer && hasScript && hasWidgetId && hasConfig;
  }

  /**
   * Extract widget ID from embed code
   */
  extractWidgetIdFromEmbedCode(embedCode: string): string | null {
    const match = embedCode.match(/data-widget-id="([^"]+)"/);
    return match ? match[1] : null;
  }

  /**
   * Generate embed code with custom styling
   */
  generateCustomStyledEmbedCode(
    widgetId: string,
    config: WidgetConfig,
    customCss?: string,
    baseUrl?: string
  ): EmbedCodeResult {
    const result = this.generateEmbedCode({ widgetId, config, baseUrl });

    if (customCss) {
      // Inject custom CSS into the embed code
      const styleTag = `<style>${customCss}</style>`;
      result.embedCode = result.embedCode.replace('</script>', `</script>\n${styleTag}`);
    }

    return result;
  }
}

// Export singleton instance
export const embedCodeService = new EmbedCodeService();
