import { WidgetConfigRepository } from '../repositories/widget-config.repository.js';
import { BusinessRepository } from '../repositories/business.repository.js';
import { 
  WidgetConfig, 
  WidgetConfigCreate, 
  WidgetConfigUpdate,
  WidgetConfigSchema,
  WidgetConfigCreateSchema,
  WidgetConfigUpdateSchema,
  DEFAULT_WIDGET_STYLING,
  DEFAULT_WIDGET_SETTINGS,
  TemplateType,
  PaginationParams,
  PaginatedResponse,
  ServiceResult
} from '../../types/index.js';
import { logger } from '../logger.js';
import { randomUUID } from 'crypto';

export interface EmbedCodeOptions {
  widgetId: string;
  baseUrl?: string;
  async?: boolean;
  defer?: boolean;
}

export interface WidgetConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class WidgetConfigService {
  private widgetConfigRepository: WidgetConfigRepository;
  private businessRepository: BusinessRepository;
  private readonly DEFAULT_BASE_URL = process.env.WIDGET_BASE_URL || 'https://widgets.example.com';

  constructor() {
    this.widgetConfigRepository = new WidgetConfigRepository();
    this.businessRepository = new BusinessRepository();
  }

  /**
   * Create a new widget configuration
   */
  async createWidgetConfig(data: WidgetConfigCreate): Promise<ServiceResult<WidgetConfig>> {
    try {
      logger.info('Creating widget configuration', { businessId: data.businessId, templateType: data.templateType });

      // Validate input data
      const validationResult = WidgetConfigCreateSchema.safeParse(data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid widget configuration data'
          }
        };
      }

      // Verify business exists
      const businessResult = await this.businessRepository.findById(data.businessId);
      if (!businessResult.success || !businessResult.data) {
        return {
          success: false,
          error: {
            code: 'BUSINESS_NOT_FOUND',
            message: 'Business not found'
          }
        };
      }

      // Apply defaults for missing styling and settings
      const configData = {
        ...data,
        stylingConfig: {
          ...DEFAULT_WIDGET_STYLING,
          ...data.stylingConfig,
          colors: {
            ...DEFAULT_WIDGET_STYLING.colors,
            ...data.stylingConfig?.colors,
          },
          fonts: {
            ...DEFAULT_WIDGET_STYLING.fonts,
            ...data.stylingConfig?.fonts,
          },
          dimensions: {
            ...DEFAULT_WIDGET_STYLING.dimensions,
            ...data.stylingConfig?.dimensions,
          },
          spacing: {
            ...DEFAULT_WIDGET_STYLING.spacing,
            ...data.stylingConfig?.spacing,
          },
        },
        widgetSettings: {
          ...DEFAULT_WIDGET_SETTINGS,
          ...data.widgetSettings,
        },
      };

      // Create widget configuration
      const createResult = await this.widgetConfigRepository.create(configData);
      if (!createResult.success || !createResult.data) {
        return {
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: `Failed to create widget configuration: ${createResult.error}`
          }
        };
      }

      const widgetConfig = createResult.data;

      // Generate embed code
      const embedCode = this.generateEmbedCodeSync({
        widgetId: widgetConfig.id,
        async: true,
        defer: true
      });

      // Update with embed code
      const updateResult = await this.widgetConfigRepository.update(widgetConfig.id, { embedCode });
      if (!updateResult.success || !updateResult.data) {
        logger.warn('Failed to update widget with embed code', { widgetId: widgetConfig.id });
        return {
          success: true,
          data: widgetConfig
        }; // Return without embed code rather than failing
      }

      logger.info('Widget configuration created successfully', { 
        widgetId: updateResult.data.id,
        businessId: data.businessId 
      });

      return {
        success: true,
        data: updateResult.data
      };

    } catch (error) {
      logger.error('Failed to create widget configuration', { 
        businessId: data.businessId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Get widget configuration by ID
   */
  async getWidgetConfigById(id: string): Promise<ServiceResult<WidgetConfig>> {
    try {
      const result = await this.widgetConfigRepository.findById(id);
      if (!result.success) {
        return {
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: `Failed to get widget configuration: ${result.error}`
          }
        };
      }

      if (!result.data) {
        return {
          success: false,
          error: {
            code: 'WIDGET_NOT_FOUND',
            message: 'Widget configuration not found'
          }
        };
      }

      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      logger.error('Failed to get widget configuration by ID', { id, error });
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Get widget configurations by business ID
   */
  async getWidgetConfigsByBusinessId(
    businessId: string, 
    pagination?: PaginationParams
  ): Promise<ServiceResult<PaginatedResponse<WidgetConfig>>> {
    try {
      const result = await this.widgetConfigRepository.findByBusinessId(businessId, pagination);
      if (!result.success) {
        return {
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: `Failed to get widget configurations: ${result.error}`
          }
        };
      }

      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      logger.error('Failed to get widget configurations by business ID', { businessId, error });
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Update widget configuration
   */
  async updateWidgetConfig(id: string, data: WidgetConfigUpdate): Promise<ServiceResult<WidgetConfig>> {
    try {
      logger.info('Updating widget configuration', { id });

      // Validate input data
      const validationResult = WidgetConfigUpdateSchema.safeParse(data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Invalid widget configuration update data: ${errors.join(', ')}`
          }
        };
      }

      // Get existing configuration
      const existingConfigResult = await this.getWidgetConfigById(id);
      if (!existingConfigResult.success) {
        return existingConfigResult;
      }
      const existingConfig = existingConfigResult.data;

      // If business ID is being changed, verify new business exists
      if (data.businessId && data.businessId !== existingConfig.businessId) {
        const businessResult = await this.businessRepository.findById(data.businessId);
        if (!businessResult.success || !businessResult.data) {
          return {
            success: false,
            error: {
              code: 'BUSINESS_NOT_FOUND',
              message: `Business not found: ${data.businessId}`
            }
          };
        }
      }

      // Merge styling and settings with existing values
      const updateData = { ...data };
      
      if (data.stylingConfig) {
        updateData.stylingConfig = {
          colors: {
            ...existingConfig.stylingConfig.colors,
            ...data.stylingConfig.colors,
          },
          fonts: {
            ...existingConfig.stylingConfig.fonts,
            ...data.stylingConfig.fonts,
          },
          dimensions: {
            ...existingConfig.stylingConfig.dimensions,
            ...data.stylingConfig.dimensions,
          },
          spacing: {
            ...existingConfig.stylingConfig.spacing,
            ...data.stylingConfig.spacing,
          },
        };
      }

      if (data.widgetSettings) {
        updateData.widgetSettings = {
          ...existingConfig.widgetSettings,
          ...data.widgetSettings,
        };
      }

      // Update widget configuration
      const updateResult = await this.widgetConfigRepository.update(id, updateData);
      if (!updateResult.success || !updateResult.data) {
        return {
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: `Failed to update widget configuration: ${updateResult.error}`
          }
        };
      }

      // Regenerate embed code if configuration changed significantly
      const shouldRegenerateEmbed = data.templateType || data.stylingConfig || data.widgetSettings;
      if (shouldRegenerateEmbed) {
        const newEmbedCode = this.generateEmbedCodeSync({
          widgetId: id,
          async: true,
          defer: true
        });

        const embedUpdateResult = await this.widgetConfigRepository.update(id, { embedCode: newEmbedCode });
        if (embedUpdateResult.success && embedUpdateResult.data) {
          logger.info('Widget configuration updated with new embed code', { id });
          return {
            success: true,
            data: embedUpdateResult.data
          };
        }
      }

      logger.info('Widget configuration updated successfully', { id });
      return {
        success: true,
        data: updateResult.data
      };

    } catch (error) {
      logger.error('Failed to update widget configuration', { id, error });
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Delete widget configuration
   */
  async deleteWidgetConfig(id: string): Promise<ServiceResult<boolean>> {
    try {
      logger.info('Deleting widget configuration', { id });

      // Verify widget exists
      const widgetResult = await this.getWidgetConfigById(id);
      if (!widgetResult.success) {
        return widgetResult as ServiceResult<boolean>;
      }

      const result = await this.widgetConfigRepository.delete(id);
      if (!result.success) {
        return {
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: `Failed to delete widget configuration: ${result.error}`
          }
        };
      }

      logger.info('Widget configuration deleted successfully', { id });
      return {
        success: true,
        data: true
      };
    } catch (error) {
      logger.error('Failed to delete widget configuration', { id, error });
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Get all widget configurations with pagination
   */
  async getAllWidgetConfigs(pagination?: PaginationParams): Promise<PaginatedResponse<WidgetConfig>> {
    try {
      const result = await this.widgetConfigRepository.findAll(pagination);
      if (!result.success) {
        throw new Error(`Failed to get widget configurations: ${result.error}`);
      }

      return result.data;
    } catch (error) {
      logger.error('Failed to get all widget configurations', { error });
      throw error;
    }
  }

  /**
   * Get widget configurations by template type
   */
  async getWidgetConfigsByTemplate(
    templateType: TemplateType, 
    pagination?: PaginationParams
  ): Promise<PaginatedResponse<WidgetConfig>> {
    try {
      const result = await this.widgetConfigRepository.findByTemplateType(templateType, pagination);
      if (!result.success) {
        throw new Error(`Failed to get widget configurations by template: ${result.error}`);
      }

      return result.data;
    } catch (error) {
      logger.error('Failed to get widget configurations by template', { templateType, error });
      throw error;
    }
  }

  /**
   * Validate widget configuration
   */
  validateWidgetConfig(config: Partial<WidgetConfig>): WidgetConfigValidationResult {
    const result: WidgetConfigValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // Validate against schema
      const validationResult = WidgetConfigSchema.partial().safeParse(config);
      if (!validationResult.success) {
        result.isValid = false;
        result.errors = validationResult.error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        );
      }

      // Additional business logic validations
      if (config.stylingConfig?.dimensions) {
        const { width, height } = config.stylingConfig.dimensions;
        if (width && height && width * height > 1000000) { // 1M pixels
          result.warnings.push('Widget dimensions are very large and may impact performance');
        }
      }

      if (config.widgetSettings?.maxReviews && config.widgetSettings.maxReviews > 20) {
        result.warnings.push('Displaying more than 20 reviews may impact loading performance');
      }

      if (config.widgetSettings?.refreshInterval && config.widgetSettings.refreshInterval < 300) {
        result.warnings.push('Refresh interval less than 5 minutes may exceed API rate limits');
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Generate embed code for widget (synchronous version)
   */
  generateEmbedCodeSync(options: EmbedCodeOptions): string {
    const { widgetId, baseUrl = this.DEFAULT_BASE_URL, async = true, defer = true } = options;

    const scriptAttributes = [
      async ? 'async' : '',
      defer ? 'defer' : '',
    ].filter(Boolean).join(' ');

    const embedCode = `<!-- Google Reviews Widget -->
<div id="grw-widget-${widgetId}" data-widget-id="${widgetId}"></div>
<script ${scriptAttributes} src="${baseUrl}/widget.js" data-widget-id="${widgetId}"></script>
<!-- End Google Reviews Widget -->`;

    return embedCode;
  }

  /**
   * Generate embed code for widget by ID (async version for tests)
   */
  async generateEmbedCode(widgetId: string): Promise<ServiceResult<string>> {
    return this.generateEmbedCodeById(widgetId);
  }

  /**
   * Generate embed code for widget by ID (async version for API)
   */
  async generateEmbedCodeById(widgetId: string): Promise<ServiceResult<string>> {
    try {
      // Verify widget exists
      const widgetResult = await this.getWidgetConfigById(widgetId);
      if (!widgetResult.success) {
        return {
          success: false,
          error: {
            code: 'WIDGET_NOT_FOUND',
            message: `Widget not found: ${widgetId}`
          }
        };
      }
      
      const embedCode = this.generateEmbedCodeSync({
        widgetId: widgetResult.data.id,
        async: true,
        defer: true
      });

      return {
        success: true,
        data: embedCode
      };
    } catch (error) {
      logger.error('Failed to generate embed code', { widgetId, error });
      return {
        success: false,
        error: {
          code: 'WIDGET_NOT_FOUND',
          message: `Widget not found: ${widgetId}`
        }
      };
    }
  }

  /**
   * Get widget configuration statistics
   */
  async getWidgetConfigStats(): Promise<{
    total: number;
    byTemplate: Record<TemplateType, number>;
    recentlyCreated: number;
    recentlyUpdated: number;
  }> {
    try {
      // Get all configurations (we'll process them in memory for stats)
      // In a production system, you'd want to optimize this with database aggregations
      const allConfigsResult = await this.widgetConfigRepository.findAll({ page: 1, limit: 1000 });
      if (!allConfigsResult.success) {
        throw new Error(`Failed to get widget configurations for stats: ${allConfigsResult.error}`);
      }

      const configs = allConfigsResult.data.data;
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const stats = {
        total: configs.length,
        byTemplate: {} as Record<TemplateType, number>,
        recentlyCreated: 0,
        recentlyUpdated: 0,
      };

      // Initialize template counts
      const templateTypes: TemplateType[] = ['carousel', 'badge', 'grid-with-summary', 'simple-carousel', 'slider', 'floating-badge'];
      templateTypes.forEach(template => {
        stats.byTemplate[template] = 0;
      });

      // Process configurations
      configs.forEach(config => {
        // Count by template
        stats.byTemplate[config.templateType]++;

        // Count recent activity
        if (config.createdAt > oneDayAgo) {
          stats.recentlyCreated++;
        }
        if (config.updatedAt > oneDayAgo) {
          stats.recentlyUpdated++;
        }
      });

      return stats;
    } catch (error) {
      logger.error('Failed to get widget configuration stats', { error });
      throw error;
    }
  }

  /**
   * Clone widget configuration
   */
  async cloneWidgetConfig(id: string, businessId?: string): Promise<WidgetConfig> {
    try {
      logger.info('Cloning widget configuration', { id, businessId });

      // Get existing configuration
      const existingConfigResult = await this.getWidgetConfigById(id);
      if (!existingConfigResult.success) {
        throw new Error(`Failed to get existing widget configuration: ${existingConfigResult.error?.message}`);
      }

      const existingConfig = existingConfigResult.data;

      // Create new configuration data
      const cloneData: WidgetConfigCreate = {
        businessId: businessId || existingConfig.businessId,
        templateType: existingConfig.templateType,
        stylingConfig: existingConfig.stylingConfig,
        widgetSettings: existingConfig.widgetSettings,
      };

      // Create the clone
      const clonedConfig = await this.createWidgetConfig(cloneData);

      logger.info('Widget configuration cloned successfully', { 
        originalId: id, 
        clonedId: clonedConfig.id 
      });

      return clonedConfig;
    } catch (error) {
      logger.error('Failed to clone widget configuration', { id, error });
      throw error;
    }
  }

  /**
   * Get widget count by business ID
   */
  async getWidgetCountByBusinessId(businessId: string): Promise<number> {
    try {
      const result = await this.widgetConfigRepository.countByBusinessId(businessId);
      if (!result.success) {
        throw new Error(`Failed to get widget count: ${result.error}`);
      }

      return result.data;
    } catch (error) {
      logger.error('Failed to get widget count by business ID', { businessId, error });
      throw error;
    }
  }
}