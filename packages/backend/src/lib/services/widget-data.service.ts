import { Review } from '../../types/review.js';
import { Business } from '../../types/business.js';
import { WidgetConfig } from '../../types/widget.js';
import { ReviewsService } from './reviews.service.js';
import { WidgetConfigService } from './widget-config.service.js';
import { logger } from '../logger.js';

export interface WidgetDataResponse {
  reviews: Review[];
  businessInfo: Business;
  totalReviews: number;
  averageRating: number;
  lastUpdated: Date;
  widgetConfig: WidgetConfig;
}

export interface WidgetDataError {
  code: string;
  message: string;
  timestamp: Date;
}

export class WidgetDataService {
  private reviewsService: ReviewsService;
  private widgetConfigService: WidgetConfigService;
  private cache: Map<string, { data: WidgetDataResponse; expiresAt: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.reviewsService = new ReviewsService();
    this.widgetConfigService = new WidgetConfigService();
  }

  /**
   * Get widget data for embedded widgets
   */
  async getWidgetData(widgetId: string): Promise<WidgetDataResponse> {
    try {
      // Check cache first
      const cached = this.getCachedData(widgetId);
      if (cached) {
        logger.info('Serving widget data from cache', { widgetId });
        return cached;
      }

      // Get widget configuration
      const widgetConfig = await this.widgetConfigService.getWidgetConfig(widgetId);
      if (!widgetConfig) {
        throw new Error(`Widget configuration not found for ID: ${widgetId}`);
      }

      // Get business and reviews data
      const [reviewsData, businessInfo] = await Promise.all([
        this.reviewsService.getReviews(widgetConfig.businessId),
        this.reviewsService.getBusinessInfo(widgetConfig.businessId)
      ]);

      // Apply widget filters and settings
      const filteredReviews = this.applyWidgetFilters(reviewsData.reviews, widgetConfig);

      const widgetData: WidgetDataResponse = {
        reviews: filteredReviews,
        businessInfo,
        totalReviews: reviewsData.totalReviews,
        averageRating: reviewsData.averageRating,
        lastUpdated: reviewsData.lastUpdated,
        widgetConfig
      };

      // Cache the data
      this.setCachedData(widgetId, widgetData);

      logger.info('Widget data retrieved successfully', {
        widgetId,
        reviewCount: filteredReviews.length,
        businessId: widgetConfig.businessId
      });

      return widgetData;
    } catch (error) {
      logger.error('Failed to get widget data', {
        widgetId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get widget data with fallback to cached data on error
   */
  async getWidgetDataWithFallback(widgetId: string): Promise<WidgetDataResponse> {
    try {
      return await this.getWidgetData(widgetId);
    } catch (error) {
      // Try to serve stale cached data as fallback
      const staleData = this.getStaleData(widgetId);
      if (staleData) {
        logger.warn('Serving stale widget data due to error', {
          widgetId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        return staleData;
      }
      
      // If no fallback data available, throw the original error
      throw error;
    }
  }

  /**
   * Preload widget data for better performance
   */
  async preloadWidgetData(widgetId: string): Promise<void> {
    try {
      await this.getWidgetData(widgetId);
      logger.info('Widget data preloaded successfully', { widgetId });
    } catch (error) {
      logger.warn('Failed to preload widget data', {
        widgetId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Clear cache for a specific widget
   */
  clearWidgetCache(widgetId: string): void {
    this.cache.delete(widgetId);
    logger.info('Widget cache cleared', { widgetId });
  }

  /**
   * Clear all cached widget data
   */
  clearAllCache(): void {
    this.cache.clear();
    logger.info('All widget cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }

  private getCachedData(widgetId: string): WidgetDataResponse | null {
    const cached = this.cache.get(widgetId);
    if (cached && Date.now() < cached.expiresAt) {
      return cached.data;
    }
    
    // Remove expired cache entry
    if (cached) {
      this.cache.delete(widgetId);
    }
    
    return null;
  }

  private getStaleData(widgetId: string): WidgetDataResponse | null {
    const cached = this.cache.get(widgetId);
    return cached ? cached.data : null;
  }

  private setCachedData(widgetId: string, data: WidgetDataResponse): void {
    this.cache.set(widgetId, {
      data,
      expiresAt: Date.now() + this.CACHE_TTL
    });
  }

  private applyWidgetFilters(reviews: Review[], config: WidgetConfig): Review[] {
    let filteredReviews = [...reviews];

    // Apply rating filter
    if (config.settings?.minRating) {
      filteredReviews = filteredReviews.filter(review => review.rating >= config.settings.minRating!);
    }

    // Apply date filter if specified
    if (config.settings?.dateRange) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - config.settings.dateRange);
      filteredReviews = filteredReviews.filter(review => review.publishedDate >= cutoffDate);
    }

    // Apply sorting
    if (config.settings?.sortBy) {
      switch (config.settings.sortBy) {
        case 'newest':
          filteredReviews.sort((a, b) => b.publishedDate.getTime() - a.publishedDate.getTime());
          break;
        case 'oldest':
          filteredReviews.sort((a, b) => a.publishedDate.getTime() - b.publishedDate.getTime());
          break;
        case 'highest_rating':
          filteredReviews.sort((a, b) => b.rating - a.rating);
          break;
        case 'lowest_rating':
          filteredReviews.sort((a, b) => a.rating - b.rating);
          break;
        case 'most_helpful':
          // Sort by helpfulness if available, fallback to newest
          filteredReviews.sort((a, b) => {
            const aHelpful = (a as any).helpfulCount || 0;
            const bHelpful = (b as any).helpfulCount || 0;
            if (aHelpful !== bHelpful) {
              return bHelpful - aHelpful;
            }
            return b.publishedDate.getTime() - a.publishedDate.getTime();
          });
          break;
        default:
          // Default to newest
          filteredReviews.sort((a, b) => b.publishedDate.getTime() - a.publishedDate.getTime());
      }
    }

    // Limit number of reviews
    if (config.settings?.maxReviews) {
      filteredReviews = filteredReviews.slice(0, config.settings.maxReviews);
    }

    return filteredReviews;
  }
}