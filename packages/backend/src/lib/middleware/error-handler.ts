import { Request, Response, NextFunction } from 'express';
import { logger } from '../logger.js';

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly isOperational: boolean;
  public readonly details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_SERVER_ERROR',
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.details = details;
    this.name = 'AppError';

    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', true, details);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND', true);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized access') {
    super(message, 401, 'UNAUTHORIZED', true);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden access') {
    super(message, 403, 'FORBIDDEN', true);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT', true);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED', true);
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: any) {
    super(`${service} service error: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', true, details);
  }
}

export class GoogleApiError extends ExternalServiceError {
  constructor(message: string, details?: any) {
    super('Google API', message, details);
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(`Database error: ${message}`, 500, 'DATABASE_ERROR', true, details);
  }
}

// Error handler middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = err;

  // Convert non-AppError instances to AppError
  if (!(error instanceof AppError)) {
    // Handle specific error types
    if (error.name === 'ValidationError') {
      error = new ValidationError(error.message);
    } else if (error.name === 'CastError') {
      error = new ValidationError('Invalid data format');
    } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      error = new DatabaseError(error.message);
    } else if (error.message.includes('Google API')) {
      error = new GoogleApiError(error.message);
    } else if (error.message.includes('ECONNREFUSED') || error.message.includes('ENOTFOUND')) {
      error = new ExternalServiceError('Network', 'Connection failed');
    } else {
      // Generic server error
      error = new AppError(
        process.env.NODE_ENV === 'production' 
          ? 'An unexpected error occurred' 
          : error.message,
        500,
        'INTERNAL_SERVER_ERROR',
        false
      );
    }
  }

  const appError = error as AppError;

  // Log error details
  const errorContext = {
    error: appError.message,
    code: appError.code,
    statusCode: appError.statusCode,
    stack: appError.stack,
    url: req.url,
    method: req.method,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    body: req.body,
    query: req.query,
    params: req.params,
    timestamp: new Date().toISOString(),
  };

  // Log based on severity
  if (appError.statusCode >= 500) {
    logger.error('Server error', errorContext);
  } else if (appError.statusCode >= 400) {
    logger.warn('Client error', errorContext);
  } else {
    logger.info('Request error', errorContext);
  }

  // Send error response
  const errorResponse: ApiError = {
    code: appError.code,
    message: appError.message,
    timestamp: new Date().toISOString(),
  };

  // Include details in development or for operational errors
  if (process.env.NODE_ENV === 'development' || appError.isOperational) {
    if (appError.details) {
      errorResponse.details = appError.details;
    }
  }

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    (errorResponse as any).stack = appError.stack;
  }

  res.status(appError.statusCode).json({
    error: errorResponse,
  });
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = fn(req, res, next);
      return Promise.resolve(result).catch(next);
    } catch (error) {
      next(error);
    }
  };
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

// Unhandled promise rejection handler
export const handleUnhandledRejection = () => {
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('Unhandled Promise Rejection', {
      reason: reason?.message || reason,
      stack: reason?.stack,
      promise: promise.toString(),
    });
    
    // In production, you might want to gracefully shut down
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  });
};

// Uncaught exception handler
export const handleUncaughtException = () => {
  process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception', {
      error: error.message,
      stack: error.stack,
    });
    
    // Exit the process as the application is in an undefined state
    process.exit(1);
  });
};

// Request timeout middleware
export const requestTimeout = (timeoutMs: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        const error = new AppError(
          'Request timeout',
          408,
          'REQUEST_TIMEOUT'
        );
        next(error);
      }
    }, timeoutMs);

    res.on('finish', () => {
      clearTimeout(timeout);
    });

    res.on('close', () => {
      clearTimeout(timeout);
    });

    next();
  };
};

// Rate limiting error handler
export const rateLimitHandler = (req: Request, res: Response) => {
  const error = new RateLimitError('Too many requests, please try again later');
  
  logger.warn('Rate limit exceeded', {
    ip: req.ip,
    url: req.url,
    userAgent: req.get('User-Agent'),
  });

  res.status(error.statusCode).json({
    error: {
      code: error.code,
      message: error.message,
      timestamp: new Date().toISOString(),
    },
  });
};