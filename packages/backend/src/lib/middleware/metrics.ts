import { Request, Response, NextFunction } from 'express';
import { register, Counter, Histogram, Gauge } from 'prom-client';
import { logPerformance, logRequest } from '../logger.js';

// Create metrics
const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const activeConnections = new Gauge({
  name: 'http_active_connections',
  help: 'Number of active HTTP connections'
});

const googleApiRequestsTotal = new Counter({
  name: 'google_api_requests_total',
  help: 'Total number of Google API requests',
  labelNames: ['api', 'status']
});

const googleApiRequestDuration = new Histogram({
  name: 'google_api_request_duration_seconds',
  help: 'Duration of Google API requests in seconds',
  labelNames: ['api'],
  buckets: [0.1, 0.5, 1, 2, 5, 10]
});

const databaseConnectionsActive = new Gauge({
  name: 'database_connections_active',
  help: 'Number of active database connections'
});

const redisConnectionsActive = new Gauge({
  name: 'redis_connections_active',
  help: 'Number of active Redis connections'
});

const cacheHitRate = new Counter({
  name: 'cache_operations_total',
  help: 'Total number of cache operations',
  labelNames: ['operation', 'result']
});

// Middleware to track HTTP metrics
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  activeConnections.inc();

  // Override res.end to capture metrics
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = (Date.now() - startTime) / 1000;
    const route = req.route?.path || req.path;
    
    // Record metrics
    httpRequestsTotal.inc({
      method: req.method,
      route,
      status_code: res.statusCode
    });

    httpRequestDuration.observe({
      method: req.method,
      route,
      status_code: res.statusCode
    }, duration);

    activeConnections.dec();

    // Log request
    logRequest(req.method, req.originalUrl, res.statusCode, duration);
    
    // Log performance if request is slow
    if (duration > 1) {
      logPerformance(`Slow request: ${req.method} ${req.originalUrl}`, duration, {
        statusCode: res.statusCode,
        userAgent: req.get('User-Agent')
      });
    }

    originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Google API metrics helpers
export const recordGoogleApiRequest = (api: string, duration: number, success: boolean) => {
  googleApiRequestsTotal.inc({
    api,
    status: success ? 'success' : 'error'
  });

  googleApiRequestDuration.observe({ api }, duration / 1000);
};

// Database metrics helpers
export const setDatabaseConnections = (count: number) => {
  databaseConnectionsActive.set(count);
};

export const setRedisConnections = (count: number) => {
  redisConnectionsActive.set(count);
};

// Cache metrics helpers
export const recordCacheOperation = (operation: 'hit' | 'miss' | 'set' | 'delete') => {
  cacheHitRate.inc({
    operation: operation === 'hit' || operation === 'miss' ? 'get' : operation,
    result: operation
  });
};

// Metrics endpoint handler
export const metricsHandler = async (req: Request, res: Response) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    res.status(500).end(error);
  }
};

// Health check handler
export const healthHandler = (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.APP_VERSION || '1.0.0'
  });
};

export default {
  metricsMiddleware,
  metricsHandler,
  healthHandler,
  recordGoogleApiRequest,
  setDatabaseConnections,
  setRedisConnections,
  recordCacheOperation
};