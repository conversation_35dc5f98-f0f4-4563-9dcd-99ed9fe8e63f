import winston from 'winston';

// Custom format for structured logging
const customFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      service,
      environment: process.env.NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      ...meta
    });
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: customFormat,
  defaultMeta: { 
    service: 'grwg-backend',
    hostname: process.env.HOSTNAME || 'unknown',
    pid: process.pid
  },
  transports: [
    // Write all logs with importance level of `error` or less to `error.log`
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    // Write all logs with importance level of `info` or less to `combined.log`
    new winston.transports.File({ 
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    // Write performance logs
    new winston.transports.File({
      filename: 'logs/performance.log',
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, duration, ...meta }) => {
          if (meta.type === 'performance') {
            return JSON.stringify({
              timestamp,
              level,
              message,
              duration,
              ...meta
            });
          }
          return null;
        })
      )
    })
  ],
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// If we're not in production, log to the console with a simple format
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Performance logging helper
export const logPerformance = (operation: string, duration: number, metadata?: any) => {
  logger.info(`Performance: ${operation}`, {
    type: 'performance',
    operation,
    duration,
    ...metadata
  });
};

// Request logging helper
export const logRequest = (method: string, url: string, statusCode: number, duration: number, userId?: string) => {
  logger.info(`${method} ${url} ${statusCode}`, {
    type: 'request',
    method,
    url,
    statusCode,
    duration,
    userId
  });
};

// Error logging helper
export const logError = (error: Error, context?: any) => {
  logger.error(error.message, {
    type: 'error',
    stack: error.stack,
    name: error.name,
    ...context
  });
};

export default logger;