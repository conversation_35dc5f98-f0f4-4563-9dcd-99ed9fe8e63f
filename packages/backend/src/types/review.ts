import { z } from 'zod';

// Review-related types and validation schemas

export const ReviewSchema = z.object({
  id: z.string().min(1),
  authorName: z.string().min(1),
  authorPhotoUrl: z.string().url().optional(),
  rating: z.number().int().min(1).max(5),
  text: z.string(),
  publishedDate: z.date(),
  isVerified: z.boolean().default(false),
});

export const BusinessInfoSchema = z.object({
  placeId: z.string().min(1),
  name: z.string().min(1),
  address: z.string().optional(),
  rating: z.number().min(0).max(5).optional(),
  reviewCount: z.number().int().min(0).optional(),
  photoUrl: z.string().url().optional(),
});

export const ReviewsResponseSchema = z.object({
  reviews: z.array(ReviewSchema),
  businessInfo: BusinessInfoSchema,
  totalReviews: z.number().int().min(0),
  averageRating: z.number().min(0).max(5),
  lastUpdated: z.date(),
});

export const ReviewsCacheDataSchema = z.object({
  reviews: z.array(ReviewSchema),
  businessInfo: BusinessInfoSchema,
  totalReviews: z.number().int().min(0),
  averageRating: z.number().min(0).max(5),
  lastUpdated: z.date(),
  expiresAt: z.date(),
});

// TypeScript types derived from Zod schemas
export type Review = z.infer<typeof ReviewSchema>;
export type BusinessInfo = z.infer<typeof BusinessInfoSchema>;
export type ReviewsResponse = z.infer<typeof ReviewsResponseSchema>;
export type ReviewsCacheData = z.infer<typeof ReviewsCacheDataSchema>;