import { z } from 'zod';

// Business-related types and validation schemas

export const BusinessSchema = z.object({
  id: z.string().uuid(),
  placeId: z.string().min(1),
  name: z.string().min(1),
  address: z.string().optional(),
  rating: z.number().min(0).max(5).optional(),
  reviewCount: z.number().int().min(0).optional(),
  photoUrl: z.string().url().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const BusinessCreateSchema = z.object({
  placeId: z.string().min(1),
  name: z.string().min(1),
  address: z.string().optional(),
  rating: z.number().min(0).max(5).optional(),
  reviewCount: z.number().int().min(0).optional(),
  photoUrl: z.string().url().optional(),
});

export const BusinessUpdateSchema = BusinessCreateSchema.partial();

export const BusinessSearchRequestSchema = z.object({
  query: z.string().min(1).refine(val => val.trim().length > 0, {
    message: "Query cannot be empty or contain only whitespace"
  }),
  location: z.string().optional(),
  googleMapsUrl: z.string().url().optional(),
});

export const BusinessSearchResponseSchema = z.object({
  businesses: z.array(BusinessSchema),
  totalResults: z.number().int().min(0),
});

// TypeScript types derived from Zod schemas
export type Business = z.infer<typeof BusinessSchema>;
export type BusinessCreate = z.infer<typeof BusinessCreateSchema>;
export type BusinessUpdate = z.infer<typeof BusinessUpdateSchema>;
export type BusinessSearchRequest = z.infer<typeof BusinessSearchRequestSchema>;
export type BusinessSearchResponse = z.infer<typeof BusinessSearchResponseSchema>;