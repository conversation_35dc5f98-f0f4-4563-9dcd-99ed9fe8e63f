import { z } from 'zod';

// Widget configuration types and validation schemas

export const TemplateTypeSchema = z.enum([
  'carousel',
  'badge',
  'grid-with-summary',
  'simple-carousel',
  'slider',
  'floating-badge'
]);

export const ColorSchemeSchema = z.object({
  primary: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
  secondary: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
  background: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
  text: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
  accent: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
});

export const FontSettingsSchema = z.object({
  family: z.string().min(1),
  size: z.number().int().min(8).max(72),
  weight: z.enum(['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900']),
  lineHeight: z.number().min(0.5).max(3),
});

export const DimensionSettingsSchema = z.object({
  width: z.number().int().min(200).max(1200),
  height: z.number().int().min(150).max(800),
  maxWidth: z.number().int().min(200).max(1200).optional(),
  maxHeight: z.number().int().min(150).max(800).optional(),
});

export const SpacingSettingsSchema = z.object({
  padding: z.number().int().min(0).max(50),
  margin: z.number().int().min(0).max(50),
  borderRadius: z.number().int().min(0).max(50),
  itemSpacing: z.number().int().min(0).max(30),
});

export const WidgetStylingSchema = z.object({
  colors: ColorSchemeSchema,
  fonts: FontSettingsSchema,
  dimensions: DimensionSettingsSchema,
  spacing: SpacingSettingsSchema,
});

export const SortOptionSchema = z.enum([
  'newest',
  'oldest',
  'highest-rating',
  'lowest-rating',
  'most-helpful'
]);

export const WidgetSettingsSchema = z.object({
  maxReviews: z.number().int().min(1).max(50),
  minRating: z.number().int().min(1).max(5),
  sortBy: SortOptionSchema,
  showPhotos: z.boolean(),
  showDates: z.boolean(),
  autoRefresh: z.boolean(),
  refreshInterval: z.number().int().min(300).max(86400).optional(), // 5 minutes to 24 hours in seconds
  headerText: z.string().max(100).optional(),
  showBusinessInfo: z.boolean().default(true),
});

export const WidgetConfigSchema = z.object({
  id: z.string().uuid(),
  businessId: z.string().uuid(),
  templateType: TemplateTypeSchema,
  stylingConfig: WidgetStylingSchema,
  widgetSettings: WidgetSettingsSchema,
  embedCode: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const WidgetConfigCreateSchema = z.object({
  businessId: z.string().uuid(),
  templateType: TemplateTypeSchema,
  stylingConfig: WidgetStylingSchema,
  widgetSettings: WidgetSettingsSchema,
});

export const WidgetConfigUpdateSchema = WidgetConfigCreateSchema.partial().extend({
  embedCode: z.string().optional(),
});

// Default styling configuration
export const DEFAULT_WIDGET_STYLING: WidgetStyling = {
  colors: {
    primary: '#4285f4',
    secondary: '#34a853',
    background: '#ffffff',
    text: '#333333',
    accent: '#fbbc04',
  },
  fonts: {
    family: 'Arial, sans-serif',
    size: 14,
    weight: 'normal',
    lineHeight: 1.4,
  },
  dimensions: {
    width: 400,
    height: 300,
  },
  spacing: {
    padding: 16,
    margin: 8,
    borderRadius: 8,
    itemSpacing: 12,
  },
};

// Default widget settings
export const DEFAULT_WIDGET_SETTINGS: WidgetSettings = {
  maxReviews: 10,
  minRating: 1,
  sortBy: 'newest',
  showPhotos: true,
  showDates: true,
  autoRefresh: true,
  refreshInterval: 3600, // 1 hour
  showBusinessInfo: true,
};

// TypeScript types derived from Zod schemas
export type TemplateType = z.infer<typeof TemplateTypeSchema>;
export type ColorScheme = z.infer<typeof ColorSchemeSchema>;
export type FontSettings = z.infer<typeof FontSettingsSchema>;
export type DimensionSettings = z.infer<typeof DimensionSettingsSchema>;
export type SpacingSettings = z.infer<typeof SpacingSettingsSchema>;
export type WidgetStyling = z.infer<typeof WidgetStylingSchema>;
export type SortOption = z.infer<typeof SortOptionSchema>;
export type WidgetSettings = z.infer<typeof WidgetSettingsSchema>;
export type WidgetConfig = z.infer<typeof WidgetConfigSchema>;
export type WidgetConfigCreate = z.infer<typeof WidgetConfigCreateSchema>;
export type WidgetConfigUpdate = z.infer<typeof WidgetConfigUpdateSchema>;