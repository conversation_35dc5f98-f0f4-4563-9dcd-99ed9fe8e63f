import { Router, Request, Response } from 'express';
import { WidgetConfigService } from '../lib/services/widget-config.service.js';
import { 
  WidgetConfigCreate, 
  WidgetConfigUpdate, 
  PaginationParams 
} from '../types/index.js';
import { logger } from '../lib/logger.js';

const router = Router();
const widgetConfigService = new WidgetConfigService();

/**
 * Create a new widget configuration
 * POST /api/widget-configs
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const widgetData: WidgetConfigCreate = req.body;
    
    const result = await widgetConfigService.createWidgetConfig(widgetData);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.status(201).json({
      success: true,
      data: result.data
    });
  } catch (error) {
    logger.error('Error in POST /widget-configs', { error });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Get widget configuration by ID
 * GET /api/widget-configs/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const result = await widgetConfigService.getWidgetConfig(id);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    logger.error('Error in GET /widget-configs/:id', { error, widgetId: req.params.id });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Update widget configuration
 * PUT /api/widget-configs/:id
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData: WidgetConfigUpdate = req.body;
    
    const result = await widgetConfigService.updateWidgetConfig(id, updateData);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    logger.error('Error in PUT /widget-configs/:id', { error, widgetId: req.params.id });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Delete widget configuration
 * DELETE /api/widget-configs/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const result = await widgetConfigService.deleteWidgetConfig(id);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: { deleted: true }
    });
  } catch (error) {
    logger.error('Error in DELETE /widget-configs/:id', { error, widgetId: req.params.id });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Get all widget configurations with pagination
 * GET /api/widget-configs
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const pagination: PaginationParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 10
    };

    // Validate pagination parameters
    if (pagination.page < 1 || pagination.limit < 1 || pagination.limit > 100) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100.'
        }
      });
    }
    
    const result = await widgetConfigService.getAllWidgetConfigs(pagination);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: result.data.data,
      pagination: result.data.pagination
    });
  } catch (error) {
    logger.error('Error in GET /widget-configs', { error });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Get widget configurations by business ID
 * GET /api/widget-configs/business/:businessId
 */
router.get('/business/:businessId', async (req: Request, res: Response) => {
  try {
    const { businessId } = req.params;
    const pagination: PaginationParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 10
    };

    // Validate pagination parameters
    if (pagination.page < 1 || pagination.limit < 1 || pagination.limit > 100) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100.'
        }
      });
    }
    
    const result = await widgetConfigService.getWidgetConfigsByBusinessId(businessId, pagination);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: result.data.data,
      pagination: result.data.pagination
    });
  } catch (error) {
    logger.error('Error in GET /widget-configs/business/:businessId', { 
      error, 
      businessId: req.params.businessId 
    });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Generate embed code for widget configuration
 * POST /api/widget-configs/:id/embed-code
 */
router.post('/:id/embed-code', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const result = await widgetConfigService.generateEmbedCode(id);
    
    if (!result.success) {
      const statusCode = getStatusCodeFromError(result.error.code);
      return res.status(statusCode).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: {
        embedCode: result.data
      }
    });
  } catch (error) {
    logger.error('Error in POST /widget-configs/:id/embed-code', { 
      error, 
      widgetId: req.params.id 
    });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Validate widget configuration data
 * POST /api/widget-configs/validate
 */
router.post('/validate', async (req: Request, res: Response) => {
  try {
    const widgetData = req.body;
    const isUpdate = req.query.update === 'true';
    
    const result = widgetConfigService.validateWidgetConfig(widgetData, isUpdate);
    
    if (!result.success) {
      return res.status(400).json({
        error: result.error
      });
    }

    res.json({
      success: true,
      data: { valid: true }
    });
  } catch (error) {
    logger.error('Error in POST /widget-configs/validate', { error });
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred'
      }
    });
  }
});

/**
 * Helper function to map error codes to HTTP status codes
 */
function getStatusCodeFromError(errorCode: string): number {
  switch (errorCode) {
    case 'VALIDATION_ERROR':
      return 400;
    case 'WIDGET_NOT_FOUND':
    case 'BUSINESS_NOT_FOUND':
      return 404;
    case 'DATABASE_ERROR':
      return 500;
    case 'INTERNAL_ERROR':
    default:
      return 500;
  }
}

export default router;