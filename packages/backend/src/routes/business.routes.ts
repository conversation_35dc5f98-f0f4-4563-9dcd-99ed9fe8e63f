import { Router, Request, Response } from 'express';
import { BusinessSearchService } from '../lib/services/business-search.service.js';
import { BusinessSearchRequestSchema } from '../types/business.js';
import { logger } from '../lib/logger.js';
import { ValidationError, GoogleApiError, NotFoundError, asyncHandler } from '../lib/middleware/error-handler.js';

const router = Router();
const businessSearchService = new BusinessSearchService();

/**
 * POST /api/business/search
 * Search for businesses using Google Places API
 */
router.post('/search', asyncHandler(async (req: Request, res: Response) => {
  // Validate request body
  const validationResult = BusinessSearchRequestSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    throw new ValidationError('Invalid request data', validationResult.error.errors);
  }

  const searchRequest = validationResult.data;
  
  logger.info('Processing business search request', { 
    query: searchRequest.query,
    hasLocation: !!searchRequest.location,
    hasGoogleMapsUrl: !!searchRequest.googleMapsUrl,
  });

  try {
    // Perform business search
    const searchResponse = await businessSearchService.searchBusinesses(searchRequest);

    logger.info('Business search completed successfully', {
      query: searchRequest.query,
      resultsCount: searchResponse.totalResults,
    });

    res.json(searchResponse);
  } catch (error) {
    if (error instanceof Error && error.message.includes('Google API')) {
      throw new GoogleApiError(error.message);
    }
    throw error;
  }
}));

/**
 * GET /api/business/:id
 * Get business details by ID
 */
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id || typeof id !== 'string') {
    throw new ValidationError('Business ID is required');
  }

  // This would typically use a business service to get details
  // For now, we'll return a placeholder response
  logger.info('Business details request', { businessId: id });

  throw new NotFoundError('Business details endpoint not yet implemented');
}));

export default router;