import { Router, Request, Response } from 'express';
import { logger } from '../lib/logger.js';
import { ValidationError, asyncHandler } from '../lib/middleware/error-handler.js';
import { z } from 'zod';

const router = Router();

// Schema for error log data
const ErrorLogSchema = z.object({
  message: z.string(),
  type: z.string(),
  stack: z.string().optional(),
  context: z.object({
    widgetId: z.string(),
    apiUrl: z.string().optional(),
    retryCount: z.number().optional(),
    hasStaleData: z.boolean().optional(),
  }),
  timestamp: z.string(),
  userAgent: z.string(),
  url: z.string(),
});

/**
 * POST /api/widget/error-log
 * Log widget runtime errors for monitoring
 */
router.post('/error-log', asyncHandler(async (req: Request, res: Response) => {
  // Validate request body
  const validationResult = ErrorLogSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    throw new ValidationError('Invalid error log data', validationResult.error.errors);
  }

  const errorData = validationResult.data;
  
  // Log the widget error with appropriate severity
  logger.error('Widget runtime error', {
    ...errorData,
    clientIP: req.ip,
    referer: req.get('Referer'),
    source: 'widget-runtime',
  });

  // In production, you could also send this to external monitoring services
  // Example integrations:
  // - Sentry: Sentry.captureException(new Error(errorData.message), { extra: errorData });
  // - LogRocket: LogRocket.captureException(new Error(errorData.message));
  // - Bugsnag: Bugsnag.notify(new Error(errorData.message), { metaData: errorData });

  res.status(200).json({
    success: true,
    message: 'Error logged successfully',
    timestamp: new Date().toISOString(),
  });
}));

/**
 * GET /api/widget/error-stats
 * Get error statistics for monitoring dashboard (admin only)
 */
router.get('/error-stats', asyncHandler(async (req: Request, res: Response) => {
  // This would typically require authentication/authorization
  // For now, return placeholder data
  
  const stats = {
    totalErrors: 0,
    errorsByType: {},
    errorsByWidget: {},
    recentErrors: [],
    timestamp: new Date().toISOString(),
  };

  res.json(stats);
}));

export default router;