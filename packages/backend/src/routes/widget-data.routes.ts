import { Router, Request, Response } from 'express';
import { WidgetDataService } from '../lib/services/widget-data.service.js';
import { logger } from '../lib/logger.js';

const router = Router();
const widgetDataService = new WidgetDataService();

/**
 * GET /api/widget/:widgetId/data
 * Get widget data for embedded widgets
 */
router.get('/:widgetId/data', async (req: Request, res: Response) => {
  try {
    const { widgetId } = req.params;
    
    if (!widgetId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_WIDGET_ID',
          message: 'Widget ID is required',
          timestamp: new Date().toISOString()
        }
      });
    }

    const widgetData = await widgetDataService.getWidgetDataWithFallback(widgetId);
    
    // Set cache headers for client-side caching
    res.set({
      'Cache-Control': 'public, max-age=300, stale-while-revalidate=600', // 5 minutes cache, 10 minutes stale
      'ETag': `"${widgetId}-${widgetData.lastUpdated.getTime()}"`,
      'Last-Modified': widgetData.lastUpdated.toUTCString()
    });

    res.json(widgetData);
  } catch (error) {
    logger.error('Failed to get widget data', {
      widgetId: req.params.widgetId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        error: {
          code: 'WIDGET_NOT_FOUND',
          message: 'Widget configuration not found',
          timestamp: new Date().toISOString()
        }
      });
    }

    res.status(500).json({
      error: {
        code: 'WIDGET_DATA_ERROR',
        message: 'Failed to retrieve widget data',
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * POST /api/widget/:widgetId/preload
 * Preload widget data for better performance
 */
router.post('/:widgetId/preload', async (req: Request, res: Response) => {
  try {
    const { widgetId } = req.params;
    
    if (!widgetId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_WIDGET_ID',
          message: 'Widget ID is required',
          timestamp: new Date().toISOString()
        }
      });
    }

    await widgetDataService.preloadWidgetData(widgetId);
    
    res.json({
      success: true,
      message: 'Widget data preloaded successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to preload widget data', {
      widgetId: req.params.widgetId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: {
        code: 'PRELOAD_ERROR',
        message: 'Failed to preload widget data',
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * DELETE /api/widget/:widgetId/cache
 * Clear cache for a specific widget
 */
router.delete('/:widgetId/cache', async (req: Request, res: Response) => {
  try {
    const { widgetId } = req.params;
    
    if (!widgetId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_WIDGET_ID',
          message: 'Widget ID is required',
          timestamp: new Date().toISOString()
        }
      });
    }

    widgetDataService.clearWidgetCache(widgetId);
    
    res.json({
      success: true,
      message: 'Widget cache cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to clear widget cache', {
      widgetId: req.params.widgetId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: {
        code: 'CACHE_CLEAR_ERROR',
        message: 'Failed to clear widget cache',
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * GET /api/widget/cache/stats
 * Get cache statistics
 */
router.get('/cache/stats', async (req: Request, res: Response) => {
  try {
    const stats = widgetDataService.getCacheStats();
    
    res.json({
      ...stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get cache stats', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: {
        code: 'CACHE_STATS_ERROR',
        message: 'Failed to retrieve cache statistics',
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * DELETE /api/widget/cache
 * Clear all widget cache
 */
router.delete('/cache', async (req: Request, res: Response) => {
  try {
    widgetDataService.clearAllCache();
    
    res.json({
      success: true,
      message: 'All widget cache cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to clear all widget cache', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: {
        code: 'CACHE_CLEAR_ALL_ERROR',
        message: 'Failed to clear all widget cache',
        timestamp: new Date().toISOString()
      }
    });
  }
});

export default router;