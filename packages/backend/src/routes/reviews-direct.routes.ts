import { Router, Request, Response } from 'express';
import { GoogleApiClient, getGoogleApiClient } from '../lib/google-api/client.js';
import { loadGoogleApiConfig } from '../lib/google-api/config.js';
import { logger } from '../lib/logger.js';

const router = Router();

// Initialize Google API client
const config = loadGoogleApiConfig();
const googleApiClient = getGoogleApiClient(config);

interface GoogleReviewData {
    author_name: string;
    author_url?: string;
    profile_photo_url?: string;
    rating: number;
    relative_time_description: string;
    text: string;
    time: number;
}

interface Review {
    id: string;
    authorName: string;
    authorPhotoUrl?: string;
    rating: number;
    text: string;
    publishedDate: string;
    isVerified: boolean;
}

/**
 * GET /api/reviews-direct/place/:placeId
 * Get reviews directly from Google Places API without database dependency
 */
router.get('/place/:placeId', async (req: Request, res: Response) => {
    try {
        const { placeId } = req.params;

        logger.info('Fetching reviews directly from Google Places API', { placeId });

        // Fetch place details with reviews from Google Places API
        const placeDetails = await googleApiClient.getPlaceDetails(
            placeId,
            ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total', 'reviews']
        );

        if (placeDetails.status !== 'OK' || !placeDetails.result) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'PLACE_NOT_FOUND',
                    message: `Place not found or Google API error: ${placeDetails.status}`,
                    timestamp: new Date()
                }
            });
        }

        const result = placeDetails.result;
        const googleReviews = result.reviews || [];

        // Convert Google reviews to our format
        const reviews: Review[] = googleReviews.map((googleReview: GoogleReviewData, index: number) => ({
            id: `${googleReview.time}_${index}`,
            authorName: googleReview.author_name,
            authorPhotoUrl: googleReview.profile_photo_url,
            rating: googleReview.rating,
            text: googleReview.text || '',
            publishedDate: new Date(googleReview.time * 1000).toISOString(),
            isVerified: true
        }));

        // Apply query parameters for filtering
        let filteredReviews = [...reviews];

        // Apply rating filter
        if (req.query.minRating) {
            const minRating = parseInt(req.query.minRating as string);
            if (!isNaN(minRating) && minRating >= 1 && minRating <= 5) {
                filteredReviews = filteredReviews.filter(review => review.rating >= minRating);
            }
        }

        // Apply sorting
        const sortBy = req.query.sortBy as string;
        switch (sortBy) {
            case 'newest':
                filteredReviews.sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime());
                break;
            case 'oldest':
                filteredReviews.sort((a, b) => new Date(a.publishedDate).getTime() - new Date(b.publishedDate).getTime());
                break;
            case 'rating_high':
                filteredReviews.sort((a, b) => b.rating - a.rating);
                break;
            case 'rating_low':
                filteredReviews.sort((a, b) => a.rating - b.rating);
                break;
            default:
                // Default to newest first
                filteredReviews.sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime());
        }

        // Apply limit
        if (req.query.maxReviews) {
            const maxReviews = parseInt(req.query.maxReviews as string);
            if (!isNaN(maxReviews) && maxReviews > 0) {
                filteredReviews = filteredReviews.slice(0, maxReviews);
            }
        }

        const response = {
            reviews: filteredReviews,
            businessInfo: {
                placeId: result.place_id,
                name: result.name,
                address: result.formatted_address,
                rating: result.rating,
                reviewCount: result.user_ratings_total
            },
            totalReviews: result.user_ratings_total || reviews.length,
            averageRating: result.rating || 0,
            lastUpdated: new Date().toISOString()
        };

        logger.info('Successfully fetched reviews directly from Google', {
            placeId,
            reviewCount: filteredReviews.length,
            businessName: result.name
        });

        res.json({
            success: true,
            data: response
        });

    } catch (error) {
        logger.error('Failed to get reviews directly from Google', {
            placeId: req.params.placeId,
            error: error instanceof Error ? error.message : 'Unknown error'
        });

        res.status(500).json({
            success: false,
            error: {
                code: 'GOOGLE_API_ERROR',
                message: error instanceof Error ? error.message : 'Failed to fetch reviews from Google Places API',
                timestamp: new Date()
            }
        });
    }
});

export default router;
