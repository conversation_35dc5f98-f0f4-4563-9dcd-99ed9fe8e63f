import { Router } from 'express';
import { z } from 'zod';
import { EmbedCodeService } from '../lib/services/embed-code.service.js';
import { WidgetConfigService } from '../lib/services/widget-config.service.js';

const embedCodeService = new EmbedCodeService();
const widgetConfigService = new WidgetConfigService();

const router = Router();

// Request/Response schemas
const GenerateEmbedCodeRequestSchema = z.object({
  widgetId: z.string().uuid(),
  baseUrl: z.string().url().optional(),
});

const GenerateEmbedCodeForConfigRequestSchema = z.object({
  configId: z.string().uuid(),
  baseUrl: z.string().url().optional(),
});

const ValidateEmbedCodeRequestSchema = z.object({
  embedCode: z.string().min(1),
});

// Generate embed code for existing widget configuration
router.post('/generate', async (req, res) => {
  try {
    const { configId, baseUrl } = GenerateEmbedCodeForConfigRequestSchema.parse(req.body);

    const config = await widgetConfigService.getWidgetConfig(configId);
    if (!config) {
      return res.status(404).json({
        error: {
          code: 'WIDGET_CONFIG_NOT_FOUND',
          message: 'Widget configuration not found',
        },
      });
    }

    // Generate embed code with full styling config
    const embedResult = embedCodeService.generateEmbedCodeForConfig(config, baseUrl);

    // Update config with embed code
    await widgetConfigService.updateWidgetConfig(configId, {
      embedCode: embedResult.embedCode,
    });

    res.json({
      success: true,
      data: embedResult,
    });
  } catch (error) {
    console.error('Error generating embed code:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to generate embed code',
      },
    });
  }
});

// Generate iframe embed code
router.post('/generate/iframe', async (req, res) => {
  try {
    const { configId, baseUrl } = GenerateEmbedCodeForConfigRequestSchema.parse(req.body);

    // Get widget configuration
    const config = await widgetConfigService.getWidgetConfig(configId);
    if (!config) {
      return res.status(404).json({
        error: {
          code: 'WIDGET_CONFIG_NOT_FOUND',
          message: 'Widget configuration not found',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Generate iframe embed code
    const iframeEmbedCode = embedCodeService.generateIframeEmbedCode(config.id, config, baseUrl);

    res.json({
      success: true,
      data: {
        embedCode: iframeEmbedCode,
        widgetId: config.id,
        type: 'iframe',
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.error('Error generating iframe embed code:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to generate iframe embed code',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

// Validate embed code
router.post('/validate', async (req, res) => {
  try {
    const { embedCode } = ValidateEmbedCodeRequestSchema.parse(req.body);

    const isValid = embedCodeService.validateEmbedCode(embedCode);
    const widgetId = embedCodeService.extractWidgetIdFromEmbedCode(embedCode);

    res.json({
      success: true,
      data: {
        isValid,
        widgetId,
        hasWidgetId: !!widgetId,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.error('Error validating embed code:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to validate embed code',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

// Get embed code for existing widget
router.get('/:widgetId', async (req, res) => {
  try {
    const { widgetId } = req.params;
    const { baseUrl } = req.query;

    // Validate widget ID format
    if (!z.string().uuid().safeParse(widgetId).success) {
      return res.status(400).json({
        error: {
          code: 'INVALID_WIDGET_ID',
          message: 'Invalid widget ID format',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Get widget configuration
    const config = await widgetConfigService.getWidgetConfig(widgetId);
    if (!config) {
      return res.status(404).json({
        error: {
          code: 'WIDGET_CONFIG_NOT_FOUND',
          message: 'Widget configuration not found',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Generate or return existing embed code
    let embedResult;
    if (config.embedCode && !baseUrl) {
      // Return existing embed code if no custom base URL
      const extractedWidgetId = embedCodeService.extractWidgetIdFromEmbedCode(config.embedCode);
      embedResult = {
        embedCode: config.embedCode,
        widgetId: extractedWidgetId || config.id,
        embedUrl: `${process.env.WIDGET_BASE_URL || 'https://widgets.example.com'}/widget/${config.id}`,
        previewUrl: `${process.env.WIDGET_BASE_URL || 'https://widgets.example.com'}/preview/${config.id}`,
      };
    } else {
      // Generate new embed code
      embedResult = embedCodeService.generateEmbedCodeForConfig(config, baseUrl as string);

      // Update configuration if needed
      if (!config.embedCode) {
        await widgetConfigService.updateWidgetConfig(widgetId, {
          embedCode: embedResult.embedCode,
        });
      }
    }

    res.json({
      success: true,
      data: embedResult,
    });
  } catch (error) {
    console.error('Error getting embed code:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to get embed code',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

export default router;
