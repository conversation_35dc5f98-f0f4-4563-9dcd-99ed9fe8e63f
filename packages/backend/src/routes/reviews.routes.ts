import { Router, Request, Response } from 'express';
import { ReviewsService, ReviewsFetchOptions } from '../lib/services/reviews.service.js';
import { GoogleApiCache } from '../lib/google-api/cache.js';
import { logger } from '../lib/logger.js';

const router = Router();

// Initialize reviews service
const googleApiCache = new GoogleApiCache();
const reviewsService = new ReviewsService(googleApiCache);

/**
 * GET /api/reviews/place/:placeId
 * Get reviews for a business by Google Place ID
 */
router.get('/place/:placeId', async (req: Request, res: Response) => {
  try {
    const { placeId } = req.params;
    const options: ReviewsFetchOptions = {};

    // Parse query parameters
    if (req.query.forceRefresh === 'true') {
      options.forceRefresh = true;
    }
    
    if (req.query.maxReviews) {
      const maxReviews = parseInt(req.query.maxReviews as string);
      if (!isNaN(maxReviews) && maxReviews > 0) {
        options.maxReviews = maxReviews;
      }
    }

    if (req.query.minRating) {
      const minRating = parseInt(req.query.minRating as string);
      if (!isNaN(minRating) && minRating >= 1 && minRating <= 5) {
        options.minRating = minRating;
      }
    }

    if (req.query.sortBy) {
      const sortBy = req.query.sortBy as string;
      if (['newest', 'oldest', 'rating_high', 'rating_low'].includes(sortBy)) {
        options.sortBy = sortBy as any;
      }
    }

    const reviews = await reviewsService.getReviewsByPlaceId(placeId, options);

    res.json({
      success: true,
      data: reviews
    });

  } catch (error) {
    logger.error('Failed to get reviews by place ID', { 
      placeId: req.params.placeId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'REVIEWS_FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch reviews',
        timestamp: new Date()
      }
    });
  }
});

/**
 * GET /api/reviews/business/:businessId
 * Get reviews for a business by internal business ID
 */
router.get('/business/:businessId', async (req: Request, res: Response) => {
  try {
    const { businessId } = req.params;
    const options: ReviewsFetchOptions = {};

    // Parse query parameters (same as above)
    if (req.query.forceRefresh === 'true') {
      options.forceRefresh = true;
    }
    
    if (req.query.maxReviews) {
      const maxReviews = parseInt(req.query.maxReviews as string);
      if (!isNaN(maxReviews) && maxReviews > 0) {
        options.maxReviews = maxReviews;
      }
    }

    if (req.query.minRating) {
      const minRating = parseInt(req.query.minRating as string);
      if (!isNaN(minRating) && minRating >= 1 && minRating <= 5) {
        options.minRating = minRating;
      }
    }

    if (req.query.sortBy) {
      const sortBy = req.query.sortBy as string;
      if (['newest', 'oldest', 'rating_high', 'rating_low'].includes(sortBy)) {
        options.sortBy = sortBy as any;
      }
    }

    const reviews = await reviewsService.getReviewsByBusinessId(businessId, options);

    res.json({
      success: true,
      data: reviews
    });

  } catch (error) {
    logger.error('Failed to get reviews by business ID', { 
      businessId: req.params.businessId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'REVIEWS_FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch reviews',
        timestamp: new Date()
      }
    });
  }
});

/**
 * POST /api/reviews/refresh
 * Refresh reviews for multiple businesses (background job endpoint)
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { businessIds } = req.body;

    if (!Array.isArray(businessIds) || businessIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'businessIds must be a non-empty array',
          timestamp: new Date()
        }
      });
    }

    const result = await reviewsService.refreshReviewsForBusinesses(businessIds);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Failed to refresh reviews', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'REFRESH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to refresh reviews',
        timestamp: new Date()
      }
    });
  }
});

/**
 * GET /api/reviews/cache/stats
 * Get cache statistics
 */
router.get('/cache/stats', async (req: Request, res: Response) => {
  try {
    const stats = await reviewsService.getCacheStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Failed to get cache stats', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'CACHE_STATS_ERROR',
        message: error instanceof Error ? error.message : 'Failed to get cache stats',
        timestamp: new Date()
      }
    });
  }
});

/**
 * DELETE /api/reviews/cache/cleanup
 * Clean up expired cache entries
 */
router.delete('/cache/cleanup', async (req: Request, res: Response) => {
  try {
    const cleanedCount = await reviewsService.cleanupExpiredCache();

    res.json({
      success: true,
      data: {
        cleanedCount,
        message: `Cleaned up ${cleanedCount} expired cache entries`
      }
    });

  } catch (error) {
    logger.error('Failed to cleanup cache', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'CACHE_CLEANUP_ERROR',
        message: error instanceof Error ? error.message : 'Failed to cleanup cache',
        timestamp: new Date()
      }
    });
  }
});

/**
 * GET /api/reviews/refresh/needed
 * Get businesses that need review refresh
 */
router.get('/refresh/needed', async (req: Request, res: Response) => {
  try {
    const bufferMinutes = req.query.bufferMinutes ? 
      parseInt(req.query.bufferMinutes as string) : 30;

    const businessIds = await reviewsService.getBusinessesNeedingRefresh(bufferMinutes);

    res.json({
      success: true,
      data: {
        businessIds,
        count: businessIds.length
      }
    });

  } catch (error) {
    logger.error('Failed to get businesses needing refresh', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'REFRESH_NEEDED_ERROR',
        message: error instanceof Error ? error.message : 'Failed to get businesses needing refresh',
        timestamp: new Date()
      }
    });
  }
});

export default router;