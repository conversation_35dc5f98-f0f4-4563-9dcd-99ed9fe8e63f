import { describe, it, expect } from 'vitest';
import { 
  BusinessSchema, 
  BusinessCreateSchema, 
  BusinessUpdateSchema,
  BusinessSearchRequestSchema,
  BusinessSearchResponseSchema 
} from '../../types/business.js';

describe('Business Type Validation', () => {
  describe('BusinessSchema', () => {
    it('should validate a complete business object', () => {
      const validBusiness = {
        id: '123e4567-e89b-12d3-a456-************',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        address: '123 Test Street, Test City',
        rating: 4.5,
        reviewCount: 100,
        photoUrl: 'https://example.com/photo.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(validBusiness);
      expect(result.success).toBe(true);
    });

    it('should validate business with optional fields missing', () => {
      const minimalBusiness = {
        id: '123e4567-e89b-12d3-a456-************',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(minimalBusiness);
      expect(result.success).toBe(true);
    });

    it('should reject invalid UUID', () => {
      const invalidBusiness = {
        id: 'invalid-uuid',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(invalidBusiness);
      expect(result.success).toBe(false);
    });

    it('should reject empty name', () => {
      const invalidBusiness = {
        id: '123e4567-e89b-12d3-a456-************',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: '',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(invalidBusiness);
      expect(result.success).toBe(false);
    });

    it('should reject rating outside valid range', () => {
      const invalidBusiness = {
        id: '123e4567-e89b-12d3-a456-************',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        rating: 6.0, // Invalid: > 5
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(invalidBusiness);
      expect(result.success).toBe(false);
    });

    it('should reject negative review count', () => {
      const invalidBusiness = {
        id: '123e4567-e89b-12d3-a456-************',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        reviewCount: -1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(invalidBusiness);
      expect(result.success).toBe(false);
    });

    it('should reject invalid photo URL', () => {
      const invalidBusiness = {
        id: '123e4567-e89b-12d3-a456-************',
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        photoUrl: 'not-a-url',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = BusinessSchema.safeParse(invalidBusiness);
      expect(result.success).toBe(false);
    });
  });

  describe('BusinessCreateSchema', () => {
    it('should validate valid business creation data', () => {
      const validData = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        address: '123 Test Street',
        rating: 4.5,
        reviewCount: 100,
        photoUrl: 'https://example.com/photo.jpg',
      };

      const result = BusinessCreateSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate minimal business creation data', () => {
      const minimalData = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
      };

      const result = BusinessCreateSchema.safeParse(minimalData);
      expect(result.success).toBe(true);
    });
  });

  describe('BusinessUpdateSchema', () => {
    it('should validate partial update data', () => {
      const updateData = {
        name: 'Updated Business Name',
        rating: 4.8,
      };

      const result = BusinessUpdateSchema.safeParse(updateData);
      expect(result.success).toBe(true);
    });

    it('should validate empty update data', () => {
      const updateData = {};

      const result = BusinessUpdateSchema.safeParse(updateData);
      expect(result.success).toBe(true);
    });
  });

  describe('BusinessSearchRequestSchema', () => {
    it('should validate search with query only', () => {
      const searchData = {
        query: 'coffee shop',
      };

      const result = BusinessSearchRequestSchema.safeParse(searchData);
      expect(result.success).toBe(true);
    });

    it('should validate search with all fields', () => {
      const searchData = {
        query: 'coffee shop',
        location: 'New York, NY',
        googleMapsUrl: 'https://maps.google.com/place/123',
      };

      const result = BusinessSearchRequestSchema.safeParse(searchData);
      expect(result.success).toBe(true);
    });

    it('should reject empty query', () => {
      const searchData = {
        query: '',
      };

      const result = BusinessSearchRequestSchema.safeParse(searchData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid Google Maps URL', () => {
      const searchData = {
        query: 'coffee shop',
        googleMapsUrl: 'not-a-url',
      };

      const result = BusinessSearchRequestSchema.safeParse(searchData);
      expect(result.success).toBe(false);
    });
  });

  describe('BusinessSearchResponseSchema', () => {
    it('should validate valid search response', () => {
      const responseData = {
        businesses: [
          {
            id: '123e4567-e89b-12d3-a456-************',
            placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
            name: 'Test Business',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        totalResults: 1,
      };

      const result = BusinessSearchResponseSchema.safeParse(responseData);
      expect(result.success).toBe(true);
    });

    it('should validate empty search response', () => {
      const responseData = {
        businesses: [],
        totalResults: 0,
      };

      const result = BusinessSearchResponseSchema.safeParse(responseData);
      expect(result.success).toBe(true);
    });

    it('should reject negative total results', () => {
      const responseData = {
        businesses: [],
        totalResults: -1,
      };

      const result = BusinessSearchResponseSchema.safeParse(responseData);
      expect(result.success).toBe(false);
    });
  });
});