import { describe, it, expect } from 'vitest';
import { 
  ReviewSchema, 
  BusinessInfoSchema, 
  ReviewsResponseSchema,
  ReviewsCacheDataSchema 
} from '../../types/review.js';

describe('Review Type Validation', () => {
  describe('ReviewSchema', () => {
    it('should validate a complete review object', () => {
      const validReview = {
        id: 'review-123',
        authorName: '<PERSON>',
        authorPhotoUrl: 'https://example.com/photo.jpg',
        rating: 5,
        text: 'Great service and food!',
        publishedDate: new Date(),
        isVerified: true,
      };

      const result = ReviewSchema.safeParse(validReview);
      expect(result.success).toBe(true);
    });

    it('should validate review with optional fields missing', () => {
      const minimalReview = {
        id: 'review-123',
        authorName: '<PERSON>',
        rating: 5,
        text: 'Great service!',
        publishedDate: new Date(),
      };

      const result = ReviewSchema.safeParse(minimalReview);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.isVerified).toBe(false); // Default value
      }
    });

    it('should reject empty review ID', () => {
      const invalidReview = {
        id: '',
        authorName: 'John Doe',
        rating: 5,
        text: 'Great service!',
        publishedDate: new Date(),
      };

      const result = ReviewSchema.safeParse(invalidReview);
      expect(result.success).toBe(false);
    });

    it('should reject empty author name', () => {
      const invalidReview = {
        id: 'review-123',
        authorName: '',
        rating: 5,
        text: 'Great service!',
        publishedDate: new Date(),
      };

      const result = ReviewSchema.safeParse(invalidReview);
      expect(result.success).toBe(false);
    });

    it('should reject rating outside valid range', () => {
      const invalidReview = {
        id: 'review-123',
        authorName: 'John Doe',
        rating: 0, // Invalid: < 1
        text: 'Great service!',
        publishedDate: new Date(),
      };

      const result = ReviewSchema.safeParse(invalidReview);
      expect(result.success).toBe(false);
    });

    it('should reject rating above maximum', () => {
      const invalidReview = {
        id: 'review-123',
        authorName: 'John Doe',
        rating: 6, // Invalid: > 5
        text: 'Great service!',
        publishedDate: new Date(),
      };

      const result = ReviewSchema.safeParse(invalidReview);
      expect(result.success).toBe(false);
    });

    it('should reject invalid author photo URL', () => {
      const invalidReview = {
        id: 'review-123',
        authorName: 'John Doe',
        authorPhotoUrl: 'not-a-url',
        rating: 5,
        text: 'Great service!',
        publishedDate: new Date(),
      };

      const result = ReviewSchema.safeParse(invalidReview);
      expect(result.success).toBe(false);
    });
  });

  describe('BusinessInfoSchema', () => {
    it('should validate complete business info', () => {
      const validBusinessInfo = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
        address: '123 Test Street',
        rating: 4.5,
        reviewCount: 100,
        photoUrl: 'https://example.com/photo.jpg',
      };

      const result = BusinessInfoSchema.safeParse(validBusinessInfo);
      expect(result.success).toBe(true);
    });

    it('should validate minimal business info', () => {
      const minimalBusinessInfo = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Business',
      };

      const result = BusinessInfoSchema.safeParse(minimalBusinessInfo);
      expect(result.success).toBe(true);
    });

    it('should reject empty place ID', () => {
      const invalidBusinessInfo = {
        placeId: '',
        name: 'Test Business',
      };

      const result = BusinessInfoSchema.safeParse(invalidBusinessInfo);
      expect(result.success).toBe(false);
    });

    it('should reject empty name', () => {
      const invalidBusinessInfo = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: '',
      };

      const result = BusinessInfoSchema.safeParse(invalidBusinessInfo);
      expect(result.success).toBe(false);
    });
  });

  describe('ReviewsResponseSchema', () => {
    it('should validate complete reviews response', () => {
      const validResponse = {
        reviews: [
          {
            id: 'review-123',
            authorName: 'John Doe',
            rating: 5,
            text: 'Great service!',
            publishedDate: new Date(),
            isVerified: true,
          },
        ],
        businessInfo: {
          placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Business',
          rating: 4.5,
          reviewCount: 100,
        },
        totalReviews: 100,
        averageRating: 4.5,
        lastUpdated: new Date(),
      };

      const result = ReviewsResponseSchema.safeParse(validResponse);
      expect(result.success).toBe(true);
    });

    it('should validate empty reviews response', () => {
      const emptyResponse = {
        reviews: [],
        businessInfo: {
          placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Business',
        },
        totalReviews: 0,
        averageRating: 0,
        lastUpdated: new Date(),
      };

      const result = ReviewsResponseSchema.safeParse(emptyResponse);
      expect(result.success).toBe(true);
    });

    it('should reject negative total reviews', () => {
      const invalidResponse = {
        reviews: [],
        businessInfo: {
          placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Business',
        },
        totalReviews: -1,
        averageRating: 0,
        lastUpdated: new Date(),
      };

      const result = ReviewsResponseSchema.safeParse(invalidResponse);
      expect(result.success).toBe(false);
    });

    it('should reject average rating outside valid range', () => {
      const invalidResponse = {
        reviews: [],
        businessInfo: {
          placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Business',
        },
        totalReviews: 0,
        averageRating: 6.0, // Invalid: > 5
        lastUpdated: new Date(),
      };

      const result = ReviewsResponseSchema.safeParse(invalidResponse);
      expect(result.success).toBe(false);
    });
  });

  describe('ReviewsCacheDataSchema', () => {
    it('should validate complete cache data', () => {
      const validCacheData = {
        reviews: [
          {
            id: 'review-123',
            authorName: 'John Doe',
            rating: 5,
            text: 'Great service!',
            publishedDate: new Date(),
            isVerified: true,
          },
        ],
        businessInfo: {
          placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Business',
          rating: 4.5,
          reviewCount: 100,
        },
        totalReviews: 100,
        averageRating: 4.5,
        lastUpdated: new Date(),
        expiresAt: new Date(),
      };

      const result = ReviewsCacheDataSchema.safeParse(validCacheData);
      expect(result.success).toBe(true);
    });

    it('should validate empty cache data', () => {
      const emptyCacheData = {
        reviews: [],
        businessInfo: {
          placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Business',
        },
        totalReviews: 0,
        averageRating: 0,
        lastUpdated: new Date(),
        expiresAt: new Date(),
      };

      const result = ReviewsCacheDataSchema.safeParse(emptyCacheData);
      expect(result.success).toBe(true);
    });
  });
});