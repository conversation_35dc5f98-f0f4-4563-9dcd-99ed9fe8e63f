import { describe, it, expect } from 'vitest';
import { 
  TemplateTypeSchema,
  ColorSchemeSchema,
  FontSettingsSchema,
  DimensionSettingsSchema,
  SpacingSettingsSchema,
  WidgetStylingSchema,
  SortOptionSchema,
  WidgetSettingsSchema,
  WidgetConfigSchema,
  WidgetConfigCreateSchema,
  WidgetConfigUpdateSchema,
  DEFAULT_WIDGET_STYLING,
  DEFAULT_WIDGET_SETTINGS
} from '../../types/widget.js';

describe('Widget Type Validation', () => {
  describe('TemplateTypeSchema', () => {
    it('should validate all template types', () => {
      const validTemplates = [
        'carousel',
        'badge',
        'grid-with-summary',
        'simple-carousel',
        'slider',
        'floating-badge'
      ];

      validTemplates.forEach(template => {
        const result = TemplateTypeSchema.safeParse(template);
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid template type', () => {
      const result = TemplateTypeSchema.safeParse('invalid-template');
      expect(result.success).toBe(false);
    });
  });

  describe('ColorSchemeSchema', () => {
    it('should validate valid color scheme', () => {
      const validColors = {
        primary: '#4285f4',
        secondary: '#34a853',
        background: '#ffffff',
        text: '#333333',
        accent: '#fbbc04',
      };

      const result = ColorSchemeSchema.safeParse(validColors);
      expect(result.success).toBe(true);
    });

    it('should reject invalid hex colors', () => {
      const invalidColors = {
        primary: 'blue', // Not hex
        secondary: '#34a853',
        background: '#ffffff',
        text: '#333333',
        accent: '#fbbc04',
      };

      const result = ColorSchemeSchema.safeParse(invalidColors);
      expect(result.success).toBe(false);
    });

    it('should reject short hex colors', () => {
      const invalidColors = {
        primary: '#fff', // Too short
        secondary: '#34a853',
        background: '#ffffff',
        text: '#333333',
        accent: '#fbbc04',
      };

      const result = ColorSchemeSchema.safeParse(invalidColors);
      expect(result.success).toBe(false);
    });
  });

  describe('FontSettingsSchema', () => {
    it('should validate valid font settings', () => {
      const validFonts = {
        family: 'Arial, sans-serif',
        size: 14,
        weight: 'normal',
        lineHeight: 1.4,
      };

      const result = FontSettingsSchema.safeParse(validFonts);
      expect(result.success).toBe(true);
    });

    it('should reject font size outside valid range', () => {
      const invalidFonts = {
        family: 'Arial, sans-serif',
        size: 5, // Too small
        weight: 'normal',
        lineHeight: 1.4,
      };

      const result = FontSettingsSchema.safeParse(invalidFonts);
      expect(result.success).toBe(false);
    });

    it('should validate numeric font weights', () => {
      const validFonts = {
        family: 'Arial, sans-serif',
        size: 14,
        weight: '600',
        lineHeight: 1.4,
      };

      const result = FontSettingsSchema.safeParse(validFonts);
      expect(result.success).toBe(true);
    });
  });

  describe('DimensionSettingsSchema', () => {
    it('should validate valid dimensions', () => {
      const validDimensions = {
        width: 400,
        height: 300,
        maxWidth: 800,
        maxHeight: 600,
      };

      const result = DimensionSettingsSchema.safeParse(validDimensions);
      expect(result.success).toBe(true);
    });

    it('should validate minimal dimensions', () => {
      const minimalDimensions = {
        width: 200,
        height: 150,
      };

      const result = DimensionSettingsSchema.safeParse(minimalDimensions);
      expect(result.success).toBe(true);
    });

    it('should reject dimensions outside valid range', () => {
      const invalidDimensions = {
        width: 100, // Too small
        height: 300,
      };

      const result = DimensionSettingsSchema.safeParse(invalidDimensions);
      expect(result.success).toBe(false);
    });
  });

  describe('SpacingSettingsSchema', () => {
    it('should validate valid spacing settings', () => {
      const validSpacing = {
        padding: 16,
        margin: 8,
        borderRadius: 8,
        itemSpacing: 12,
      };

      const result = SpacingSettingsSchema.safeParse(validSpacing);
      expect(result.success).toBe(true);
    });

    it('should reject negative spacing values', () => {
      const invalidSpacing = {
        padding: -5, // Negative
        margin: 8,
        borderRadius: 8,
        itemSpacing: 12,
      };

      const result = SpacingSettingsSchema.safeParse(invalidSpacing);
      expect(result.success).toBe(false);
    });
  });

  describe('WidgetStylingSchema', () => {
    it('should validate complete widget styling', () => {
      const result = WidgetStylingSchema.safeParse(DEFAULT_WIDGET_STYLING);
      expect(result.success).toBe(true);
    });
  });

  describe('SortOptionSchema', () => {
    it('should validate all sort options', () => {
      const validSortOptions = [
        'newest',
        'oldest',
        'highest-rating',
        'lowest-rating',
        'most-helpful'
      ];

      validSortOptions.forEach(option => {
        const result = SortOptionSchema.safeParse(option);
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid sort option', () => {
      const result = SortOptionSchema.safeParse('invalid-sort');
      expect(result.success).toBe(false);
    });
  });

  describe('WidgetSettingsSchema', () => {
    it('should validate complete widget settings', () => {
      const result = WidgetSettingsSchema.safeParse(DEFAULT_WIDGET_SETTINGS);
      expect(result.success).toBe(true);
    });

    it('should validate minimal widget settings', () => {
      const minimalSettings = {
        maxReviews: 5,
        minRating: 1,
        sortBy: 'newest',
        showPhotos: true,
        showDates: true,
        autoRefresh: false,
      };

      const result = WidgetSettingsSchema.safeParse(minimalSettings);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.showBusinessInfo).toBe(true); // Default value
      }
    });

    it('should reject maxReviews outside valid range', () => {
      const invalidSettings = {
        maxReviews: 0, // Too small
        minRating: 1,
        sortBy: 'newest',
        showPhotos: true,
        showDates: true,
        autoRefresh: false,
      };

      const result = WidgetSettingsSchema.safeParse(invalidSettings);
      expect(result.success).toBe(false);
    });

    it('should reject minRating outside valid range', () => {
      const invalidSettings = {
        maxReviews: 10,
        minRating: 0, // Too small
        sortBy: 'newest',
        showPhotos: true,
        showDates: true,
        autoRefresh: false,
      };

      const result = WidgetSettingsSchema.safeParse(invalidSettings);
      expect(result.success).toBe(false);
    });

    it('should reject refreshInterval outside valid range', () => {
      const invalidSettings = {
        maxReviews: 10,
        minRating: 1,
        sortBy: 'newest',
        showPhotos: true,
        showDates: true,
        autoRefresh: true,
        refreshInterval: 100, // Too small (< 300 seconds)
      };

      const result = WidgetSettingsSchema.safeParse(invalidSettings);
      expect(result.success).toBe(false);
    });

    it('should reject headerText that is too long', () => {
      const invalidSettings = {
        maxReviews: 10,
        minRating: 1,
        sortBy: 'newest',
        showPhotos: true,
        showDates: true,
        autoRefresh: false,
        headerText: 'a'.repeat(101), // Too long (> 100 chars)
      };

      const result = WidgetSettingsSchema.safeParse(invalidSettings);
      expect(result.success).toBe(false);
    });
  });

  describe('WidgetConfigSchema', () => {
    it('should validate complete widget config', () => {
      const validConfig = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        businessId: '123e4567-e89b-12d3-a456-************',
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS,
        embedCode: '<div>embed code</div>',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = WidgetConfigSchema.safeParse(validConfig);
      expect(result.success).toBe(true);
    });

    it('should validate widget config without embed code', () => {
      const validConfig = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        businessId: '123e4567-e89b-12d3-a456-************',
        templateType: 'badge',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = WidgetConfigSchema.safeParse(validConfig);
      expect(result.success).toBe(true);
    });
  });

  describe('WidgetConfigCreateSchema', () => {
    it('should validate widget config creation data', () => {
      const validCreateData = {
        businessId: '123e4567-e89b-12d3-a456-************',
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS,
      };

      const result = WidgetConfigCreateSchema.safeParse(validCreateData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid business ID UUID', () => {
      const invalidCreateData = {
        businessId: 'invalid-uuid',
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS,
      };

      const result = WidgetConfigCreateSchema.safeParse(invalidCreateData);
      expect(result.success).toBe(false);
    });
  });

  describe('WidgetConfigUpdateSchema', () => {
    it('should validate partial widget config update', () => {
      const validUpdateData = {
        templateType: 'badge',
        embedCode: '<div>new embed code</div>',
      };

      const result = WidgetConfigUpdateSchema.safeParse(validUpdateData);
      expect(result.success).toBe(true);
    });

    it('should validate empty update data', () => {
      const emptyUpdateData = {};

      const result = WidgetConfigUpdateSchema.safeParse(emptyUpdateData);
      expect(result.success).toBe(true);
    });
  });

  describe('Default Values', () => {
    it('should have valid default widget styling', () => {
      const result = WidgetStylingSchema.safeParse(DEFAULT_WIDGET_STYLING);
      expect(result.success).toBe(true);
    });

    it('should have valid default widget settings', () => {
      const result = WidgetSettingsSchema.safeParse(DEFAULT_WIDGET_SETTINGS);
      expect(result.success).toBe(true);
    });
  });
});