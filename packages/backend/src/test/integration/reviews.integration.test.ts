import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { ReviewsService } from '../../lib/services/reviews.service.js';
import { GoogleApiCache } from '../../lib/google-api/cache.js';
import { ReviewsCacheRepository } from '../../lib/repositories/reviews-cache.repository.js';
import { BusinessRepository } from '../../lib/repositories/business.repository.js';
import { getPrismaClient } from '../../lib/database.js';
import { PrismaClient } from '@prisma/client';

// Integration tests for ReviewsService
// These tests use real database connections but mock external APIs

describe('ReviewsService Integration Tests', () => {
  let reviewsService: ReviewsService;
  let prisma: PrismaClient;
  let googleApiCache: GoogleApiCache;
  let reviewsCacheRepository: ReviewsCacheRepository;
  let businessRepository: BusinessRepository;

  const testPlaceId = 'ChIJTest123456789';
  const testBusinessData = {
    placeId: testPlaceId,
    name: 'Test Integration Business',
    address: '123 Integration Test St',
    rating: 4.2,
    reviewCount: 50
  };

  beforeAll(async () => {
    // Initialize database connection
    prisma = getPrismaClient();
    
    // Initialize services
    googleApiCache = new GoogleApiCache(); // Memory cache for tests
    reviewsCacheRepository = new ReviewsCacheRepository();
    businessRepository = new BusinessRepository();
    reviewsService = new ReviewsService(googleApiCache);

    // Mock Google API client to avoid real API calls
    const mockGoogleApiClient = {
      getPlaceDetails: vi.fn().mockResolvedValue({
        status: 'OK',
        result: {
          place_id: testPlaceId,
          name: testBusinessData.name,
          formatted_address: testBusinessData.address,
          rating: testBusinessData.rating,
          user_ratings_total: testBusinessData.reviewCount,
          reviews: [
            {
              author_name: 'Integration Test User',
              rating: 5,
              text: 'Great integration test!',
              time: Math.floor(Date.now() / 1000),
              profile_photo_url: 'https://example.com/photo.jpg'
            },
            {
              author_name: 'Another Test User',
              rating: 4,
              text: 'Good service for testing.',
              time: Math.floor(Date.now() / 1000) - 86400 // 1 day ago
            }
          ]
        }
      })
    };

    // Inject mock client
    (reviewsService as any).googleApiClient = mockGoogleApiClient;
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    
    // Close database connection
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await cleanupTestData();
  });

  afterEach(async () => {
    // Clean up after each test
    await cleanupTestData();
  });

  async function cleanupTestData() {
    try {
      // Delete test reviews cache
      await prisma.reviewsCache.deleteMany({
        where: {
          business: {
            placeId: testPlaceId
          }
        }
      });

      // Delete test business
      await prisma.business.deleteMany({
        where: {
          placeId: testPlaceId
        }
      });
    } catch (error) {
      console.warn('Cleanup warning:', error);
    }
  }

  describe('End-to-End Review Fetching', () => {
    it('should fetch and cache reviews for a new business', async () => {
      // Execute: Fetch reviews for a place that doesn't exist in database
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId);

      // Verify: Response structure
      expect(result).toMatchObject({
        reviews: expect.any(Array),
        businessInfo: expect.objectContaining({
          placeId: testPlaceId,
          name: testBusinessData.name
        }),
        totalReviews: expect.any(Number),
        averageRating: expect.any(Number),
        lastUpdated: expect.any(Date)
      });

      expect(result.reviews.length).toBeGreaterThan(0);
      expect(result.reviews[0]).toMatchObject({
        id: expect.any(String),
        authorName: expect.any(String),
        rating: expect.any(Number),
        text: expect.any(String),
        publishedDate: expect.any(Date),
        isVerified: expect.any(Boolean)
      });

      // Verify: Business was created in database
      const businessResult = await businessRepository.findByPlaceId(testPlaceId);
      expect(businessResult.success).toBe(true);
      expect(businessResult.data).toMatchObject({
        placeId: testPlaceId,
        name: testBusinessData.name
      });

      // Verify: Reviews were cached in database
      const cacheResult = await reviewsCacheRepository.findValidByBusinessId(businessResult.data!.id);
      expect(cacheResult.success).toBe(true);
      expect(cacheResult.data).toBeTruthy();
      expect(cacheResult.data!.reviewsData.reviews.length).toBe(result.reviews.length);
    });

    it('should return cached reviews on subsequent requests', async () => {
      // Setup: First request to populate cache
      const firstResult = await reviewsService.getReviewsByPlaceId(testPlaceId);
      
      // Clear the Google API cache to ensure we're testing database cache
      await googleApiCache.clear();

      // Execute: Second request should use cached data
      const secondResult = await reviewsService.getReviewsByPlaceId(testPlaceId);

      // Verify: Results are identical
      expect(secondResult).toEqual(firstResult);
      expect(secondResult.lastUpdated).toEqual(firstResult.lastUpdated);
    });

    it('should force refresh when requested', async () => {
      // Setup: First request to populate cache
      const firstResult = await reviewsService.getReviewsByPlaceId(testPlaceId);
      
      // Wait a moment to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      // Execute: Force refresh
      const refreshedResult = await reviewsService.getReviewsByPlaceId(testPlaceId, { 
        forceRefresh: true 
      });

      // Verify: Data was refreshed (timestamps should be different)
      expect(refreshedResult.businessInfo.placeId).toBe(firstResult.businessInfo.placeId);
      expect(refreshedResult.lastUpdated.getTime()).toBeGreaterThan(firstResult.lastUpdated.getTime());
    });
  });

  describe('Filtering and Sorting', () => {
    beforeEach(async () => {
      // Setup: Populate cache with test data
      await reviewsService.getReviewsByPlaceId(testPlaceId);
    });

    it('should filter reviews by minimum rating', async () => {
      // Execute: Get reviews with minimum rating of 5
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId, {
        minRating: 5
      });

      // Verify: All reviews have rating >= 5
      expect(result.reviews.every(review => review.rating >= 5)).toBe(true);
    });

    it('should limit number of reviews returned', async () => {
      // Execute: Get only 1 review
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId, {
        maxReviews: 1
      });

      // Verify: Only 1 review returned
      expect(result.reviews).toHaveLength(1);
    });

    it('should sort reviews by rating (high to low)', async () => {
      // Execute: Get reviews sorted by rating (high to low)
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId, {
        sortBy: 'rating_high'
      });

      // Verify: Reviews are sorted by rating (descending)
      for (let i = 1; i < result.reviews.length; i++) {
        expect(result.reviews[i-1].rating).toBeGreaterThanOrEqual(result.reviews[i].rating);
      }
    });

    it('should sort reviews by date (newest first)', async () => {
      // Execute: Get reviews sorted by date (newest first)
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId, {
        sortBy: 'newest'
      });

      // Verify: Reviews are sorted by date (descending)
      for (let i = 1; i < result.reviews.length; i++) {
        expect(result.reviews[i-1].publishedDate.getTime())
          .toBeGreaterThanOrEqual(result.reviews[i].publishedDate.getTime());
      }
    });
  });

  describe('Business ID Operations', () => {
    let businessId: string;

    beforeEach(async () => {
      // Setup: Create business and get its ID
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId);
      const businessResult = await businessRepository.findByPlaceId(testPlaceId);
      businessId = businessResult.data!.id;
    });

    it('should get reviews by business ID', async () => {
      // Execute
      const result = await reviewsService.getReviewsByBusinessId(businessId);

      // Verify
      expect(result.businessInfo.placeId).toBe(testPlaceId);
      expect(result.reviews.length).toBeGreaterThan(0);
    });

    it('should throw error for non-existent business ID', async () => {
      // Execute and verify
      await expect(reviewsService.getReviewsByBusinessId('non-existent-id'))
        .rejects.toThrow('Business not found');
    });
  });

  describe('Bulk Operations', () => {
    let businessIds: string[];

    beforeEach(async () => {
      // Setup: Create multiple businesses
      await reviewsService.getReviewsByPlaceId(testPlaceId);
      
      const businessResult = await businessRepository.findByPlaceId(testPlaceId);
      businessIds = [businessResult.data!.id];
    });

    it('should refresh reviews for multiple businesses', async () => {
      // Execute
      const result = await reviewsService.refreshReviewsForBusinesses(businessIds);

      // Verify
      expect(result.successful).toEqual(businessIds);
      expect(result.failed).toHaveLength(0);
    });

    it('should handle mixed success/failure in bulk refresh', async () => {
      // Add a non-existent business ID
      const mixedIds = [...businessIds, 'non-existent-id'];

      // Execute
      const result = await reviewsService.refreshReviewsForBusinesses(mixedIds);

      // Verify
      expect(result.successful).toEqual(businessIds);
      expect(result.failed).toHaveLength(1);
      expect(result.failed[0].businessId).toBe('non-existent-id');
    });
  });

  describe('Cache Management', () => {
    beforeEach(async () => {
      // Setup: Populate cache
      await reviewsService.getReviewsByPlaceId(testPlaceId);
    });

    it('should identify businesses needing refresh', async () => {
      // Execute: Get businesses needing refresh (with 0 buffer to include current data)
      const businessIds = await reviewsService.getBusinessesNeedingRefresh(0);

      // Verify: Should be empty since we just cached data
      expect(businessIds).toEqual([]);
    });

    it('should get cache statistics', async () => {
      // Execute
      const stats = await reviewsService.getCacheStats();

      // Verify
      expect(stats).toMatchObject({
        database: {
          total: expect.any(Number),
          expired: expect.any(Number),
          valid: expect.any(Number)
        },
        redis: expect.any(Object)
      });

      expect(stats.database.total).toBeGreaterThan(0);
    });

    it('should cleanup expired cache entries', async () => {
      // Setup: Manually expire cache entry
      const businessResult = await businessRepository.findByPlaceId(testPlaceId);
      const businessId = businessResult.data!.id;
      
      // Update cache to be expired
      await prisma.reviewsCache.updateMany({
        where: { businessId },
        data: { expiresAt: new Date(Date.now() - 1000) } // 1 second ago
      });

      // Execute
      const cleanedCount = await reviewsService.cleanupExpiredCache();

      // Verify
      expect(cleanedCount).toBeGreaterThan(0);

      // Verify cache was actually deleted
      const cacheResult = await reviewsCacheRepository.findByBusinessId(businessId);
      expect(cacheResult.success).toBe(true);
      expect(cacheResult.data).toBeNull();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle database connection issues gracefully', async () => {
      // This test would require more complex setup to simulate DB issues
      // For now, we'll test that the service handles repository errors
      
      // Mock a repository method to fail
      const originalMethod = reviewsCacheRepository.findValidByBusinessId;
      reviewsCacheRepository.findValidByBusinessId = vi.fn().mockRejectedValue(new Error('DB Connection Error'));

      try {
        // Execute: This should still work by falling back to API
        const result = await reviewsService.getReviewsByPlaceId(testPlaceId);
        
        // Verify: Should still get results from API
        expect(result.reviews.length).toBeGreaterThan(0);
      } finally {
        // Restore original method
        reviewsCacheRepository.findValidByBusinessId = originalMethod;
      }
    });

    it('should validate data integrity', async () => {
      // Execute: Normal operation should produce valid data
      const result = await reviewsService.getReviewsByPlaceId(testPlaceId);

      // Verify: All reviews have required fields
      result.reviews.forEach(review => {
        expect(review.id).toBeTruthy();
        expect(review.authorName).toBeTruthy();
        expect(review.rating).toBeGreaterThanOrEqual(1);
        expect(review.rating).toBeLessThanOrEqual(5);
        expect(review.publishedDate).toBeInstanceOf(Date);
        expect(typeof review.isVerified).toBe('boolean');
      });

      // Verify: Business info is valid
      expect(result.businessInfo.placeId).toBeTruthy();
      expect(result.businessInfo.name).toBeTruthy();
      expect(result.totalReviews).toBeGreaterThanOrEqual(0);
      expect(result.averageRating).toBeGreaterThanOrEqual(0);
      expect(result.averageRating).toBeLessThanOrEqual(5);
    });
  });

  describe('Performance and Caching', () => {
    it('should demonstrate caching performance improvement', async () => {
      // First request (cold cache)
      const start1 = Date.now();
      const result1 = await reviewsService.getReviewsByPlaceId(testPlaceId);
      const duration1 = Date.now() - start1;

      // Second request (warm cache)
      const start2 = Date.now();
      const result2 = await reviewsService.getReviewsByPlaceId(testPlaceId);
      const duration2 = Date.now() - start2;

      // Verify: Results are the same
      expect(result2).toEqual(result1);

      // Verify: Second request should be faster (cached)
      // Note: This might not always be true in test environment, but it's a good indicator
      console.log(`Cold cache: ${duration1}ms, Warm cache: ${duration2}ms`);
      expect(duration2).toBeLessThan(duration1 * 2); // At least not significantly slower
    });
  });
});