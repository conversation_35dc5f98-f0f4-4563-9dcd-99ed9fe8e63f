import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { loadGoogleApiConfig } from '../../lib/google-api/config.js';
import { GoogleApiClient, getGoogleApiClient, resetGoogleApiClient } from '../../lib/google-api/client.js';
import { GoogleApiRateLimiter } from '../../lib/google-api/rate-limiter.js';
import { GoogleApiCache } from '../../lib/google-api/cache.js';

// Mock environment variables for testing
vi.mock('process', () => ({
  env: {
    GOOGLE_PLACES_API_KEY: 'test-places-key',
    GOOGLE_MY_BUSINESS_API_KEY: 'test-mybusiness-key',
    NODE_ENV: 'test',
  },
}));

describe('Google API Integration', () => {
  let config: any;
  let client: GoogleApiClient;
  let rateLimiter: GoogleApiRateLimiter;
  let cache: GoogleApiCache;

  beforeAll(() => {
    // Set up test environment
    process.env.GOOGLE_PLACES_API_KEY = 'test-places-key';
    process.env.GOOGLE_MY_BUSINESS_API_KEY = 'test-mybusiness-key';
    
    config = loadGoogleApiConfig();
    client = getGoogleApiClient(config);
    rateLimiter = new GoogleApiRateLimiter();
    cache = new GoogleApiCache();
  });

  afterAll(() => {
    resetGoogleApiClient();
  });

  describe('Configuration Integration', () => {
    it('should load configuration and create client successfully', () => {
      expect(config).toBeDefined();
      expect(config.placesApiKey).toBe('test-places-key');
      expect(config.myBusinessApiKey).toBe('test-mybusiness-key');
      expect(client).toBeInstanceOf(GoogleApiClient);
    });

    it('should create singleton client instance', () => {
      const client2 = getGoogleApiClient();
      expect(client).toBe(client2);
    });
  });

  describe('Rate Limiter Integration', () => {
    it('should create rate limiter without Redis', () => {
      expect(rateLimiter).toBeInstanceOf(GoogleApiRateLimiter);
    });

    it('should handle rate limit checks', async () => {
      await expect(rateLimiter.checkPlacesApiLimit()).resolves.toBeUndefined();
      await expect(rateLimiter.checkMyBusinessApiLimit()).resolves.toBeUndefined();
    });

    it('should get remaining points', async () => {
      const placesPoints = await rateLimiter.getRemainingPoints('places');
      const myBusinessPoints = await rateLimiter.getRemainingPoints('mybusiness');
      
      expect(typeof placesPoints).toBe('number');
      expect(typeof myBusinessPoints).toBe('number');
    });
  });

  describe('Cache Integration', () => {
    it('should create cache without Redis', () => {
      expect(cache).toBeInstanceOf(GoogleApiCache);
    });

    it('should handle cache operations', async () => {
      const testData = { test: 'data' };
      
      // Set cache entry
      await cache.set('test', 'key1', testData, 60);
      
      // Get cache entry
      const retrieved = await cache.get('test', 'key1');
      expect(retrieved).toEqual(testData);
      
      // Delete cache entry
      await cache.delete('test', 'key1');
      
      // Verify deletion
      const afterDelete = await cache.get('test', 'key1');
      expect(afterDelete).toBeNull();
    });

    it('should handle specific Google API cache methods', async () => {
      const placeDetails = { place_id: 'test', name: 'Test Place' };
      const searchResults = { results: [] };
      const reviews = { reviews: [] };
      
      // Test place details cache
      await cache.setPlaceDetails('test-place-id', placeDetails);
      const cachedDetails = await cache.getPlaceDetails('test-place-id');
      expect(cachedDetails).toEqual(placeDetails);
      
      // Test place search cache
      await cache.setPlaceSearch('test query', searchResults);
      const cachedSearch = await cache.getPlaceSearch('test query');
      expect(cachedSearch).toEqual(searchResults);
      
      // Test business reviews cache
      await cache.setBusinessReviews('test-location-id', reviews);
      const cachedReviews = await cache.getBusinessReviews('test-location-id');
      expect(cachedReviews).toEqual(reviews);
    });

    it('should get cache statistics', async () => {
      const stats = await cache.getStats();
      expect(stats).toBeDefined();
      expect(typeof stats.memoryEntries).toBe('number');
    });
  });

  describe('Component Integration', () => {
    it('should work together for a typical workflow', async () => {
      // 1. Check rate limits
      await rateLimiter.checkPlacesApiLimit('integration-test');
      
      // 2. Check cache first (should be empty)
      const cachedResult = await cache.getPlaceSearch('integration test query');
      expect(cachedResult).toBeNull();
      
      // 3. Cache some test data
      const mockSearchResult = {
        results: [
          {
            place_id: 'test-place-id',
            name: 'Test Business',
            formatted_address: '123 Test St',
          },
        ],
      };
      
      await cache.setPlaceSearch('integration test query', mockSearchResult);
      
      // 4. Retrieve from cache
      const retrievedResult = await cache.getPlaceSearch('integration test query');
      expect(retrievedResult).toEqual(mockSearchResult);
      
      // 5. Clear cache
      await cache.clear('place_search');
      
      // 6. Verify cache is cleared
      const afterClear = await cache.getPlaceSearch('integration test query');
      expect(afterClear).toBeNull();
    });
  });
});