import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import widgetDataRoutes from '../../routes/widget-data.routes.js';
import { WidgetDataService } from '../../lib/services/widget-data.service.js';

// Mock the WidgetDataService
vi.mock('../../lib/services/widget-data.service.js');
vi.mock('../../lib/logger.js', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
}));

describe('Widget Data Routes Integration', () => {
  let app: express.Application;
  let mockWidgetDataService: vi.Mocked<WidgetDataService>;

  const mockWidgetData = {
    reviews: [
      {
        id: 'review-1',
        authorName: '<PERSON>',
        authorPhotoUrl: 'https://example.com/john.jpg',
        rating: 5,
        text: 'Great service!',
        publishedDate: new Date('2023-01-01'),
        isVerified: true
      }
    ],
    businessInfo: {
      id: 'business-456',
      placeId: 'place-789',
      name: 'Test Business',
      address: '123 Test St',
      rating: 4.5,
      reviewCount: 100,
      photoUrl: 'https://example.com/photo.jpg',
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01')
    },
    totalReviews: 100,
    averageRating: 4.5,
    lastUpdated: new Date('2023-01-01'),
    widgetConfig: {
      id: 'widget-123',
      businessId: 'business-456',
      template: 'carousel' as const,
      styling: {
        colors: { primary: '#000000', secondary: '#ffffff' },
        fonts: { family: 'Arial', size: 14 },
        dimensions: { width: 300, height: 400 },
        spacing: { padding: 16, margin: 8 }
      },
      settings: {
        maxReviews: 10,
        minRating: 4,
        sortBy: 'newest' as const,
        showPhotos: true,
        showDates: true,
        autoRefresh: true
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-02')
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Create mock instance
    mockWidgetDataService = {
      getWidgetDataWithFallback: vi.fn(),
      preloadWidgetData: vi.fn(),
      clearWidgetCache: vi.fn(),
      clearAllCache: vi.fn(),
      getCacheStats: vi.fn()
    } as any;

    // Mock the constructor
    vi.mocked(WidgetDataService).mockImplementation(() => mockWidgetDataService);

    // Create Express app with routes
    app = express();
    app.use(express.json());
    app.use('/api/widget', widgetDataRoutes);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /api/widget/:widgetId/data', () => {
    it('should return widget data successfully', async () => {
      // Arrange
      mockWidgetDataService.getWidgetDataWithFallback.mockResolvedValue(mockWidgetData);

      // Act
      const response = await request(app)
        .get('/api/widget/widget-123/data')
        .expect(200);

      // Assert
      expect(response.body).toEqual(mockWidgetData);
      expect(response.headers['cache-control']).toContain('public');
      expect(response.headers['etag']).toBeDefined();
      expect(response.headers['last-modified']).toBeDefined();
      expect(mockWidgetDataService.getWidgetDataWithFallback).toHaveBeenCalledWith('widget-123');
    });

    it('should return 400 when widget ID is missing', async () => {
      // Act
      const response = await request(app)
        .get('/api/widget//data')
        .expect(404); // Express returns 404 for missing route params

      // Assert - Route doesn't match, so we get 404
    });

    it('should return 404 when widget not found', async () => {
      // Arrange
      mockWidgetDataService.getWidgetDataWithFallback.mockRejectedValue(
        new Error('Widget configuration not found')
      );

      // Act
      const response = await request(app)
        .get('/api/widget/nonexistent/data')
        .expect(404);

      // Assert
      expect(response.body.error.code).toBe('WIDGET_NOT_FOUND');
      expect(response.body.error.message).toBe('Widget configuration not found');
    });

    it('should return 500 for other errors', async () => {
      // Arrange
      mockWidgetDataService.getWidgetDataWithFallback.mockRejectedValue(
        new Error('Database connection failed')
      );

      // Act
      const response = await request(app)
        .get('/api/widget/widget-123/data')
        .expect(500);

      // Assert
      expect(response.body.error.code).toBe('WIDGET_DATA_ERROR');
      expect(response.body.error.message).toBe('Failed to retrieve widget data');
    });

    it('should handle conditional requests with ETag', async () => {
      // Arrange
      mockWidgetDataService.getWidgetDataWithFallback.mockResolvedValue(mockWidgetData);

      // First request to get ETag
      const firstResponse = await request(app)
        .get('/api/widget/widget-123/data')
        .expect(200);

      const etag = firstResponse.headers['etag'];

      // Second request with If-None-Match header
      const secondResponse = await request(app)
        .get('/api/widget/widget-123/data')
        .set('If-None-Match', etag)
        .expect(200); // Our mock doesn't handle 304, but headers are set

      // Assert
      expect(etag).toBeDefined();
    });
  });

  describe('POST /api/widget/:widgetId/preload', () => {
    it('should preload widget data successfully', async () => {
      // Arrange
      mockWidgetDataService.preloadWidgetData.mockResolvedValue();

      // Act
      const response = await request(app)
        .post('/api/widget/widget-123/preload')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Widget data preloaded successfully');
      expect(mockWidgetDataService.preloadWidgetData).toHaveBeenCalledWith('widget-123');
    });

    it('should return 500 when preload fails', async () => {
      // Arrange
      mockWidgetDataService.preloadWidgetData.mockRejectedValue(new Error('Preload failed'));

      // Act
      const response = await request(app)
        .post('/api/widget/widget-123/preload')
        .expect(500);

      // Assert
      expect(response.body.error.code).toBe('PRELOAD_ERROR');
      expect(response.body.error.message).toBe('Failed to preload widget data');
    });
  });

  describe('DELETE /api/widget/:widgetId/cache', () => {
    it('should clear widget cache successfully', async () => {
      // Arrange
      mockWidgetDataService.clearWidgetCache.mockReturnValue();

      // Act
      const response = await request(app)
        .delete('/api/widget/widget-123/cache')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Widget cache cleared successfully');
      expect(mockWidgetDataService.clearWidgetCache).toHaveBeenCalledWith('widget-123');
    });

    it('should return 500 when cache clear fails', async () => {
      // Arrange
      mockWidgetDataService.clearWidgetCache.mockImplementation(() => {
        throw new Error('Cache clear failed');
      });

      // Act
      const response = await request(app)
        .delete('/api/widget/widget-123/cache')
        .expect(500);

      // Assert
      expect(response.body.error.code).toBe('CACHE_CLEAR_ERROR');
      expect(response.body.error.message).toBe('Failed to clear widget cache');
    });
  });

  describe('GET /api/widget/cache/stats', () => {
    it('should return cache statistics', async () => {
      // Arrange
      const mockStats = {
        size: 5,
        entries: ['widget-1', 'widget-2', 'widget-3']
      };
      mockWidgetDataService.getCacheStats.mockReturnValue(mockStats);

      // Act
      const response = await request(app)
        .get('/api/widget/cache/stats')
        .expect(200);

      // Assert
      expect(response.body.size).toBe(5);
      expect(response.body.entries).toEqual(['widget-1', 'widget-2', 'widget-3']);
      expect(response.body.timestamp).toBeDefined();
    });

    it('should return 500 when stats retrieval fails', async () => {
      // Arrange
      mockWidgetDataService.getCacheStats.mockImplementation(() => {
        throw new Error('Stats failed');
      });

      // Act
      const response = await request(app)
        .get('/api/widget/cache/stats')
        .expect(500);

      // Assert
      expect(response.body.error.code).toBe('CACHE_STATS_ERROR');
      expect(response.body.error.message).toBe('Failed to retrieve cache statistics');
    });
  });

  describe('DELETE /api/widget/cache', () => {
    it('should clear all widget cache successfully', async () => {
      // Arrange
      mockWidgetDataService.clearAllCache.mockReturnValue();

      // Act
      const response = await request(app)
        .delete('/api/widget/cache')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('All widget cache cleared successfully');
      expect(mockWidgetDataService.clearAllCache).toHaveBeenCalled();
    });

    it('should return 500 when clear all cache fails', async () => {
      // Arrange
      mockWidgetDataService.clearAllCache.mockImplementation(() => {
        throw new Error('Clear all failed');
      });

      // Act
      const response = await request(app)
        .delete('/api/widget/cache')
        .expect(500);

      // Assert
      expect(response.body.error.code).toBe('CACHE_CLEAR_ALL_ERROR');
      expect(response.body.error.message).toBe('Failed to clear all widget cache');
    });
  });

  describe('Error handling', () => {
    it('should handle malformed widget IDs gracefully', async () => {
      // Arrange
      mockWidgetDataService.getWidgetDataWithFallback.mockRejectedValue(
        new Error('Invalid widget ID format')
      );

      // Act
      const response = await request(app)
        .get('/api/widget/invalid-widget-id/data')
        .expect(500);

      // Assert
      expect(response.body.error.code).toBe('WIDGET_DATA_ERROR');
    });

    it('should include timestamp in all error responses', async () => {
      // Arrange
      mockWidgetDataService.getWidgetDataWithFallback.mockRejectedValue(
        new Error('Test error')
      );

      // Act
      const response = await request(app)
        .get('/api/widget/widget-123/data')
        .expect(500);

      // Assert
      expect(response.body.error.timestamp).toBeDefined();
      expect(new Date(response.body.error.timestamp)).toBeInstanceOf(Date);
    });
  });
});