import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import widgetConfigRoutes from '../../routes/widget-config.routes.js';
import { getPrismaClient } from '../../lib/database.js';
import { 
  WidgetConfigCreate, 
  WidgetConfigUpdate,
  DEFAULT_WIDGET_STYLING,
  DEFAULT_WIDGET_SETTINGS 
} from '../../types/index.js';

const app = express();
app.use(express.json());
app.use('/api/widget-configs', widgetConfigRoutes);

const prisma = getPrismaClient();

describe('Widget Configuration API Integration Tests', () => {
  let testBusinessId: string;
  let testWidgetId: string;

  beforeEach(async () => {
    // Clean up existing test data
    await prisma.widgetConfig.deleteMany({
      where: {
        business: {
          name: { contains: 'Test Business' }
        }
      }
    });
    
    await prisma.business.deleteMany({
      where: {
        name: { contains: 'Test Business' }
      }
    });

    // Create test business
    const business = await prisma.business.create({
      data: {
        placeId: 'test-place-id-' + Date.now(),
        name: 'Test Business for Widget Config',
        address: '123 Test Street',
        rating: 4.5,
        reviewCount: 100,
        photoUrl: 'https://example.com/photo.jpg'
      }
    });
    testBusinessId = business.id;
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.widgetConfig.deleteMany({
      where: {
        business: {
          name: { contains: 'Test Business' }
        }
      }
    });
    
    await prisma.business.deleteMany({
      where: {
        name: { contains: 'Test Business' }
      }
    });
  });

  describe('POST /api/widget-configs', () => {
    it('should create a new widget configuration', async () => {
      const widgetData: WidgetConfigCreate = {
        businessId: testBusinessId,
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS
      };

      const response = await request(app)
        .post('/api/widget-configs')
        .send(widgetData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        businessId: testBusinessId,
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS
      });
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.createdAt).toBeDefined();
      expect(response.body.data.updatedAt).toBeDefined();

      testWidgetId = response.body.data.id;
    });

    it('should return 400 for invalid widget data', async () => {
      const invalidData = {
        businessId: 'invalid-uuid',
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS
      };

      const response = await request(app)
        .post('/api/widget-configs')
        .send(invalidData)
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 404 for non-existent business', async () => {
      const widgetData: WidgetConfigCreate = {
        businessId: '00000000-0000-0000-0000-000000000000',
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS
      };

      const response = await request(app)
        .post('/api/widget-configs')
        .send(widgetData)
        .expect(404);

      expect(response.body.error.code).toBe('BUSINESS_NOT_FOUND');
    });
  });

  describe('GET /api/widget-configs/:id', () => {
    beforeEach(async () => {
      // Create test widget configuration
      const widget = await prisma.widgetConfig.create({
        data: {
          businessId: testBusinessId,
          templateType: 'carousel',
          stylingConfig: DEFAULT_WIDGET_STYLING,
          widgetSettings: DEFAULT_WIDGET_SETTINGS
        }
      });
      testWidgetId = widget.id;
    });

    it('should get widget configuration by ID', async () => {
      const response = await request(app)
        .get(`/api/widget-configs/${testWidgetId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(testWidgetId);
      expect(response.body.data.businessId).toBe(testBusinessId);
      expect(response.body.data.templateType).toBe('carousel');
    });

    it('should return 404 for non-existent widget', async () => {
      const response = await request(app)
        .get('/api/widget-configs/00000000-0000-0000-0000-000000000000')
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_NOT_FOUND');
    });

    it('should return 400 for invalid widget ID', async () => {
      const response = await request(app)
        .get('/api/widget-configs/invalid-id')
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('PUT /api/widget-configs/:id', () => {
    beforeEach(async () => {
      // Create test widget configuration
      const widget = await prisma.widgetConfig.create({
        data: {
          businessId: testBusinessId,
          templateType: 'carousel',
          stylingConfig: DEFAULT_WIDGET_STYLING,
          widgetSettings: DEFAULT_WIDGET_SETTINGS
        }
      });
      testWidgetId = widget.id;
    });

    it('should update widget configuration', async () => {
      const updateData: WidgetConfigUpdate = {
        templateType: 'grid-with-summary',
        widgetSettings: {
          ...DEFAULT_WIDGET_SETTINGS,
          maxReviews: 5
        }
      };

      const response = await request(app)
        .put(`/api/widget-configs/${testWidgetId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.templateType).toBe('grid-with-summary');
      expect(response.body.data.widgetSettings.maxReviews).toBe(5);
    });

    it('should return 404 for non-existent widget', async () => {
      const updateData: WidgetConfigUpdate = {
        templateType: 'grid-with-summary'
      };

      const response = await request(app)
        .put('/api/widget-configs/00000000-0000-0000-0000-000000000000')
        .send(updateData)
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_NOT_FOUND');
    });

    it('should return 400 for invalid update data', async () => {
      const invalidUpdateData = {
        templateType: 'invalid-template'
      };

      const response = await request(app)
        .put(`/api/widget-configs/${testWidgetId}`)
        .send(invalidUpdateData)
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('DELETE /api/widget-configs/:id', () => {
    beforeEach(async () => {
      // Create test widget configuration
      const widget = await prisma.widgetConfig.create({
        data: {
          businessId: testBusinessId,
          templateType: 'carousel',
          stylingConfig: DEFAULT_WIDGET_STYLING,
          widgetSettings: DEFAULT_WIDGET_SETTINGS
        }
      });
      testWidgetId = widget.id;
    });

    it('should delete widget configuration', async () => {
      const response = await request(app)
        .delete(`/api/widget-configs/${testWidgetId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.deleted).toBe(true);

      // Verify widget is deleted
      const widget = await prisma.widgetConfig.findUnique({
        where: { id: testWidgetId }
      });
      expect(widget).toBeNull();
    });

    it('should return 404 for non-existent widget', async () => {
      const response = await request(app)
        .delete('/api/widget-configs/00000000-0000-0000-0000-000000000000')
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_NOT_FOUND');
    });
  });

  describe('GET /api/widget-configs', () => {
    beforeEach(async () => {
      // Create multiple test widget configurations
      await prisma.widgetConfig.createMany({
        data: [
          {
            businessId: testBusinessId,
            templateType: 'carousel',
            stylingConfig: DEFAULT_WIDGET_STYLING,
            widgetSettings: DEFAULT_WIDGET_SETTINGS
          },
          {
            businessId: testBusinessId,
            templateType: 'badge',
            stylingConfig: DEFAULT_WIDGET_STYLING,
            widgetSettings: DEFAULT_WIDGET_SETTINGS
          }
        ]
      });
    });

    it('should get all widget configurations with pagination', async () => {
      const response = await request(app)
        .get('/api/widget-configs?page=1&limit=10')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThanOrEqual(2);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(10);
    });

    it('should return 400 for invalid pagination parameters', async () => {
      const response = await request(app)
        .get('/api/widget-configs?page=0&limit=101')
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/widget-configs/business/:businessId', () => {
    beforeEach(async () => {
      // Create test widget configurations for the business
      await prisma.widgetConfig.createMany({
        data: [
          {
            businessId: testBusinessId,
            templateType: 'carousel',
            stylingConfig: DEFAULT_WIDGET_STYLING,
            widgetSettings: DEFAULT_WIDGET_SETTINGS
          },
          {
            businessId: testBusinessId,
            templateType: 'badge',
            stylingConfig: DEFAULT_WIDGET_STYLING,
            widgetSettings: DEFAULT_WIDGET_SETTINGS
          }
        ]
      });
    });

    it('should get widget configurations by business ID', async () => {
      const response = await request(app)
        .get(`/api/widget-configs/business/${testBusinessId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(2);
      expect(response.body.data[0].businessId).toBe(testBusinessId);
      expect(response.body.pagination).toBeDefined();
    });

    it('should return 404 for non-existent business', async () => {
      const response = await request(app)
        .get('/api/widget-configs/business/00000000-0000-0000-0000-000000000000')
        .expect(404);

      expect(response.body.error.code).toBe('BUSINESS_NOT_FOUND');
    });
  });

  describe('POST /api/widget-configs/:id/embed-code', () => {
    beforeEach(async () => {
      // Create test widget configuration
      const widget = await prisma.widgetConfig.create({
        data: {
          businessId: testBusinessId,
          templateType: 'carousel',
          stylingConfig: DEFAULT_WIDGET_STYLING,
          widgetSettings: DEFAULT_WIDGET_SETTINGS
        }
      });
      testWidgetId = widget.id;
    });

    it('should generate embed code for widget', async () => {
      const response = await request(app)
        .post(`/api/widget-configs/${testWidgetId}/embed-code`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.embedCode).toBeDefined();
      expect(response.body.data.embedCode).toContain(`google-reviews-widget-${testWidgetId}`);
      expect(response.body.data.embedCode).toContain(`data-widget-id="${testWidgetId}"`);
      expect(response.body.data.embedCode).toContain('GoogleReviewsWidget.init');
    });

    it('should return 404 for non-existent widget', async () => {
      const response = await request(app)
        .post('/api/widget-configs/00000000-0000-0000-0000-000000000000/embed-code')
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_NOT_FOUND');
    });
  });

  describe('POST /api/widget-configs/validate', () => {
    it('should validate widget configuration data', async () => {
      const validData: WidgetConfigCreate = {
        businessId: testBusinessId,
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS
      };

      const response = await request(app)
        .post('/api/widget-configs/validate')
        .send(validData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.valid).toBe(true);
    });

    it('should return validation error for invalid data', async () => {
      const invalidData = {
        businessId: 'invalid-uuid',
        templateType: 'carousel',
        stylingConfig: DEFAULT_WIDGET_STYLING,
        widgetSettings: DEFAULT_WIDGET_SETTINGS
      };

      const response = await request(app)
        .post('/api/widget-configs/validate')
        .send(invalidData)
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should validate update data when update=true', async () => {
      const updateData: WidgetConfigUpdate = {
        templateType: 'grid-with-summary'
      };

      const response = await request(app)
        .post('/api/widget-configs/validate?update=true')
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.valid).toBe(true);
    });
  });
});