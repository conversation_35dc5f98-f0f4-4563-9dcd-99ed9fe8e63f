import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { createApp } from '../../index.js';
import { testPrisma } from '../setup.js';

describe('API Endpoints Integration Tests', () => {
  let app: any;
  let prisma: PrismaClient;

  beforeAll(async () => {
    app = createApp();
    prisma = testPrisma;
  });

  beforeEach(async () => {
    // Clean database before each test
    await prisma.reviewsCache.deleteMany();
    await prisma.widgetConfig.deleteMany();
    await prisma.business.deleteMany();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('Business Search API', () => {
    it('should search for businesses successfully', async () => {
      const searchRequest = {
        query: 'Test Restaurant',
        location: 'New York, NY',
      };

      const response = await request(app)
        .post('/api/business/search')
        .send(searchRequest)
        .expect(200);

      expect(response.body).toHaveProperty('businesses');
      expect(response.body).toHaveProperty('totalResults');
      expect(Array.isArray(response.body.businesses)).toBe(true);
    });

    it('should validate search request parameters', async () => {
      const invalidRequest = {
        query: '', // Empty query should fail
      };

      const response = await request(app)
        .post('/api/business/search')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('query');
    });

    it('should handle Google Maps URL parsing', async () => {
      const searchRequest = {
        query: 'Test Restaurant',
        googleMapsUrl: 'https://maps.google.com/maps?place_id=ChIJ123456789',
      };

      const response = await request(app)
        .post('/api/business/search')
        .send(searchRequest)
        .expect(200);

      expect(response.body.businesses).toBeDefined();
    });

    it('should cache business data in database', async () => {
      const searchRequest = {
        query: 'Test Restaurant',
      };

      await request(app)
        .post('/api/business/search')
        .send(searchRequest)
        .expect(200);

      // Check if business was saved to database
      const businesses = await prisma.business.findMany();
      expect(businesses.length).toBeGreaterThan(0);
    });
  });

  describe('Reviews API', () => {
    let businessId: string;

    beforeEach(async () => {
      // Create a test business
      const business = await prisma.business.create({
        data: {
          placeId: 'ChIJ123456789',
          name: 'Test Restaurant',
          address: '123 Test St, Test City, TC 12345',
          rating: 4.5,
          reviewCount: 100,
        },
      });
      businessId = business.id;
    });

    it('should fetch reviews for a business', async () => {
      const response = await request(app)
        .get(`/api/reviews/${businessId}`)
        .expect(200);

      expect(response.body).toHaveProperty('reviews');
      expect(response.body).toHaveProperty('businessInfo');
      expect(response.body).toHaveProperty('totalReviews');
      expect(response.body).toHaveProperty('averageRating');
      expect(Array.isArray(response.body.reviews)).toBe(true);
    });

    it('should return 404 for non-existent business', async () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-446655440999';
      
      await request(app)
        .get(`/api/reviews/${nonExistentId}`)
        .expect(404);
    });

    it('should cache reviews data', async () => {
      await request(app)
        .get(`/api/reviews/${businessId}`)
        .expect(200);

      // Check if reviews were cached
      const cachedReviews = await prisma.reviewsCache.findFirst({
        where: { businessId },
      });

      expect(cachedReviews).toBeTruthy();
      expect(cachedReviews?.reviewsData).toBeDefined();
    });

    it('should serve cached data when available', async () => {
      // First request to populate cache
      const firstResponse = await request(app)
        .get(`/api/reviews/${businessId}`)
        .expect(200);

      // Second request should use cache
      const secondResponse = await request(app)
        .get(`/api/reviews/${businessId}`)
        .expect(200);

      expect(firstResponse.body).toEqual(secondResponse.body);
    });
  });

  describe('Widget Configuration API', () => {
    let businessId: string;

    beforeEach(async () => {
      const business = await prisma.business.create({
        data: {
          placeId: 'ChIJ123456789',
          name: 'Test Restaurant',
          address: '123 Test St, Test City, TC 12345',
          rating: 4.5,
          reviewCount: 100,
        },
      });
      businessId = business.id;
    });

    it('should create widget configuration', async () => {
      const widgetConfig = {
        businessId,
        template: 'carousel',
        styling: {
          colors: {
            primary: '#007bff',
            secondary: '#6c757d',
            background: '#ffffff',
            text: '#212529',
          },
          fonts: {
            family: 'Arial, sans-serif',
            size: '14px',
          },
          dimensions: {
            width: '100%',
            height: 'auto',
          },
          spacing: {
            padding: '16px',
            margin: '8px',
          },
        },
        settings: {
          maxReviews: 5,
          minRating: 1,
          sortBy: 'newest',
          showPhotos: true,
          showDates: true,
          autoRefresh: false,
        },
      };

      const response = await request(app)
        .post('/api/widget/config')
        .send(widgetConfig)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.businessId).toBe(businessId);
      expect(response.body.template).toBe('carousel');
    });

    it('should retrieve widget configuration', async () => {
      // Create widget config first
      const widgetConfig = await prisma.widgetConfig.create({
        data: {
          businessId,
          templateType: 'carousel',
          stylingConfig: {
            colors: { primary: '#007bff' },
          },
          widgetSettings: {
            maxReviews: 5,
          },
        },
      });

      const response = await request(app)
        .get(`/api/widget/config/${widgetConfig.id}`)
        .expect(200);

      expect(response.body.id).toBe(widgetConfig.id);
      expect(response.body.businessId).toBe(businessId);
    });

    it('should update widget configuration', async () => {
      // Create widget config first
      const widgetConfig = await prisma.widgetConfig.create({
        data: {
          businessId,
          templateType: 'carousel',
          stylingConfig: {},
          widgetSettings: {},
        },
      });

      const updateData = {
        template: 'grid',
        styling: {
          colors: { primary: '#ff0000' },
        },
      };

      const response = await request(app)
        .put(`/api/widget/config/${widgetConfig.id}`)
        .send(updateData)
        .expect(200);

      expect(response.body.template).toBe('grid');
      expect(response.body.styling.colors.primary).toBe('#ff0000');
    });

    it('should delete widget configuration', async () => {
      // Create widget config first
      const widgetConfig = await prisma.widgetConfig.create({
        data: {
          businessId,
          templateType: 'carousel',
          stylingConfig: {},
          widgetSettings: {},
        },
      });

      await request(app)
        .delete(`/api/widget/config/${widgetConfig.id}`)
        .expect(204);

      // Verify deletion
      const deletedConfig = await prisma.widgetConfig.findUnique({
        where: { id: widgetConfig.id },
      });

      expect(deletedConfig).toBeNull();
    });

    it('should validate widget configuration data', async () => {
      const invalidConfig = {
        businessId: 'invalid-uuid',
        template: 'invalid-template',
        styling: 'not-an-object',
      };

      await request(app)
        .post('/api/widget/config')
        .send(invalidConfig)
        .expect(400);
    });
  });

  describe('Embed Code Generation API', () => {
    let widgetId: string;

    beforeEach(async () => {
      const business = await prisma.business.create({
        data: {
          placeId: 'ChIJ123456789',
          name: 'Test Restaurant',
          address: '123 Test St',
          rating: 4.5,
          reviewCount: 100,
        },
      });

      const widgetConfig = await prisma.widgetConfig.create({
        data: {
          businessId: business.id,
          templateType: 'carousel',
          stylingConfig: {},
          widgetSettings: {},
        },
      });

      widgetId = widgetConfig.id;
    });

    it('should generate embed code for widget', async () => {
      const response = await request(app)
        .post('/api/embed/generate')
        .send({ widgetId })
        .expect(200);

      expect(response.body).toHaveProperty('embedCode');
      expect(response.body).toHaveProperty('widgetUrl');
      expect(response.body).toHaveProperty('previewUrl');
      expect(response.body.embedCode).toContain(widgetId);
    });

    it('should return 404 for non-existent widget', async () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-446655440999';

      await request(app)
        .post('/api/embed/generate')
        .send({ widgetId: nonExistentId })
        .expect(404);
    });

    it('should include widget configuration in embed code', async () => {
      const response = await request(app)
        .post('/api/embed/generate')
        .send({ widgetId })
        .expect(200);

      expect(response.body.embedCode).toContain('data-widget-id');
      expect(response.body.embedCode).toContain(widgetId);
    });
  });

  describe('Widget Data API (for embedded widgets)', () => {
    let widgetId: string;

    beforeEach(async () => {
      const business = await prisma.business.create({
        data: {
          placeId: 'ChIJ123456789',
          name: 'Test Restaurant',
          address: '123 Test St',
          rating: 4.5,
          reviewCount: 100,
        },
      });

      const widgetConfig = await prisma.widgetConfig.create({
        data: {
          businessId: business.id,
          templateType: 'carousel',
          stylingConfig: {},
          widgetSettings: {},
        },
      });

      widgetId = widgetConfig.id;
    });

    it('should serve widget data for embedded widgets', async () => {
      const response = await request(app)
        .get(`/api/widget/${widgetId}/data`)
        .expect(200);

      expect(response.body).toHaveProperty('reviews');
      expect(response.body).toHaveProperty('businessInfo');
      expect(response.body).toHaveProperty('config');
      expect(Array.isArray(response.body.reviews)).toBe(true);
    });

    it('should include CORS headers for cross-origin requests', async () => {
      const response = await request(app)
        .get(`/api/widget/${widgetId}/data`)
        .set('Origin', 'https://example.com')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });

    it('should support conditional requests with ETag', async () => {
      // First request to get ETag
      const firstResponse = await request(app)
        .get(`/api/widget/${widgetId}/data`)
        .expect(200);

      const etag = firstResponse.headers.etag;
      expect(etag).toBeDefined();

      // Second request with If-None-Match header
      await request(app)
        .get(`/api/widget/${widgetId}/data`)
        .set('If-None-Match', etag)
        .expect(304);
    });

    it('should apply widget settings to returned data', async () => {
      // Update widget settings
      await prisma.widgetConfig.update({
        where: { id: widgetId },
        data: {
          widgetSettings: {
            maxReviews: 3,
            minRating: 4,
            sortBy: 'rating',
          },
        },
      });

      const response = await request(app)
        .get(`/api/widget/${widgetId}/data`)
        .expect(200);

      // Verify settings are applied
      expect(response.body.reviews.length).toBeLessThanOrEqual(3);
      expect(response.body.config.settings.maxReviews).toBe(3);
      expect(response.body.config.settings.minRating).toBe(4);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      // This would require mocking database connection failures
      // For now, we'll test general error handling
      
      await request(app)
        .get('/api/nonexistent-endpoint')
        .expect(404);
    });

    it('should handle malformed JSON requests', async () => {
      await request(app)
        .post('/api/business/search')
        .send('invalid-json')
        .set('Content-Type', 'application/json')
        .expect(400);
    });

    it('should handle rate limiting', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/business/search')
          .send({ query: 'Test' })
      );

      const responses = await Promise.all(requests);
      
      // At least one should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should log errors appropriately', async () => {
      // This would require mocking the logger
      // For now, we'll verify error responses include proper structure
      
      const response = await request(app)
        .get('/api/reviews/invalid-uuid')
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message');
      expect(response.body.error).toHaveProperty('timestamp');
    });
  });

  describe('Performance and Caching', () => {
    it('should respond within acceptable time limits', async () => {
      const startTime = Date.now();
      
      await request(app)
        .post('/api/business/search')
        .send({ query: 'Test Restaurant' })
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(5000); // 5 seconds max
    });

    it('should implement proper caching headers', async () => {
      const business = await prisma.business.create({
        data: {
          placeId: 'ChIJ123456789',
          name: 'Test Restaurant',
          address: '123 Test St',
          rating: 4.5,
          reviewCount: 100,
        },
      });

      const response = await request(app)
        .get(`/api/reviews/${business.id}`)
        .expect(200);

      expect(response.headers['cache-control']).toBeDefined();
      expect(response.headers.etag).toBeDefined();
    });
  });
});