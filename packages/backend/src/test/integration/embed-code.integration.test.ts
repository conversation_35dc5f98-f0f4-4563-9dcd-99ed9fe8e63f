import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import embedCodeRoutes from '../../routes/embed-code.routes.js';
import { WidgetConfig, DEFAULT_WIDGET_STYLING, DEFAULT_WIDGET_SETTINGS } from '../../types/widget.js';

// Mock the widget config service
vi.mock('../../lib/services/widget-config.service.js', () => ({
  widgetConfigService: {
    getWidgetConfig: vi.fn(),
    updateWidgetConfig: vi.fn(),
  },
}));

import { widgetConfigService } from '../../lib/services/widget-config.service.js';

describe('Embed Code API Integration Tests', () => {
  let app: express.Application;

  beforeEach(() => {
    // Setup mock implementations
    vi.mocked(widgetConfigService.getWidgetConfig).mockImplementation(async (id: string) => {
      if (id === 'valid-widget-id') {
        return {
          id: 'valid-widget-id',
          businessId: 'business-123',
          templateType: 'carousel',
          stylingConfig: DEFAULT_WIDGET_STYLING,
          widgetSettings: DEFAULT_WIDGET_SETTINGS,
          createdAt: new Date('2023-01-01T00:00:00Z'),
          updatedAt: new Date('2023-01-01T00:00:00Z'),
        } as WidgetConfig;
      }
      return null;
    });

    vi.mocked(widgetConfigService.updateWidgetConfig).mockImplementation(async (id: string, updates: any) => {
      if (id === 'valid-widget-id') {
        return {
          id: 'valid-widget-id',
          businessId: 'business-123',
          templateType: 'carousel',
          stylingConfig: DEFAULT_WIDGET_STYLING,
          widgetSettings: DEFAULT_WIDGET_SETTINGS,
          embedCode: updates.embedCode,
          createdAt: new Date('2023-01-01T00:00:00Z'),
          updatedAt: new Date(),
        } as WidgetConfig;
      }
      return null;
    });

    app = express();
    app.use(express.json());
    app.use('/api/embed-code', embedCodeRoutes);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /api/embed-code/generate', () => {
    it('should generate embed code for valid widget configuration', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'valid-widget-id',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('embedCode');
      expect(response.body.data).toHaveProperty('widgetId');
      expect(response.body.data).toHaveProperty('embedUrl');
      expect(response.body.data).toHaveProperty('previewUrl');

      const { embedCode, widgetId } = response.body.data;
      expect(embedCode).toContain('reviews-widget-container');
      expect(embedCode).toContain(`data-widget-id="${widgetId}"`);
      expect(embedCode).toContain('data-config=');
      expect(embedCode).toContain('<script>');
    });

    it('should generate embed code with custom base URL', async () => {
      const customBaseUrl = 'https://custom.example.com';
      
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'valid-widget-id',
          baseUrl: customBaseUrl,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.embedUrl).toContain(customBaseUrl);
      expect(response.body.data.previewUrl).toContain(customBaseUrl);
    });

    it('should return 404 for non-existent widget configuration', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'non-existent-id',
        })
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_CONFIG_NOT_FOUND');
    });

    it('should return 400 for invalid request data', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'invalid-uuid',
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for missing configId', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({})
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid base URL', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'valid-widget-id',
          baseUrl: 'not-a-valid-url',
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/embed-code/generate/iframe', () => {
    it('should generate iframe embed code', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate/iframe')
        .send({
          configId: 'valid-widget-id',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('embedCode');
      expect(response.body.data).toHaveProperty('widgetId');
      expect(response.body.data.type).toBe('iframe');

      const { embedCode } = response.body.data;
      expect(embedCode).toContain('<iframe');
      expect(embedCode).toContain('frameborder="0"');
      expect(embedCode).toContain('scrolling="no"');
    });

    it('should generate iframe embed code with custom base URL', async () => {
      const customBaseUrl = 'https://custom.example.com';
      
      const response = await request(app)
        .post('/api/embed-code/generate/iframe')
        .send({
          configId: 'valid-widget-id',
          baseUrl: customBaseUrl,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.embedCode).toContain(customBaseUrl);
    });

    it('should return 404 for non-existent widget configuration', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate/iframe')
        .send({
          configId: 'non-existent-id',
        })
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_CONFIG_NOT_FOUND');
    });
  });

  describe('POST /api/embed-code/validate', () => {
    it('should validate correct embed code', async () => {
      // First generate a valid embed code
      const generateResponse = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'valid-widget-id',
        });

      const { embedCode } = generateResponse.body.data;

      // Then validate it
      const response = await request(app)
        .post('/api/embed-code/validate')
        .send({
          embedCode,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(true);
      expect(response.body.data.hasWidgetId).toBe(true);
      expect(response.body.data.widgetId).toBe('valid-widget-id');
    });

    it('should reject invalid embed code', async () => {
      const response = await request(app)
        .post('/api/embed-code/validate')
        .send({
          embedCode: '<div>Invalid code</div>',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.data.hasWidgetId).toBe(false);
      expect(response.body.data.widgetId).toBeNull();
    });

    it('should return 400 for missing embed code', async () => {
      const response = await request(app)
        .post('/api/embed-code/validate')
        .send({})
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for empty embed code', async () => {
      const response = await request(app)
        .post('/api/embed-code/validate')
        .send({
          embedCode: '',
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/embed-code/:widgetId', () => {
    it('should get embed code for existing widget', async () => {
      const response = await request(app)
        .get('/api/embed-code/valid-widget-id')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('embedCode');
      expect(response.body.data).toHaveProperty('widgetId');
      expect(response.body.data).toHaveProperty('embedUrl');
      expect(response.body.data).toHaveProperty('previewUrl');
    });

    it('should get embed code with custom base URL', async () => {
      const customBaseUrl = 'https://custom.example.com';
      
      const response = await request(app)
        .get(`/api/embed-code/valid-widget-id?baseUrl=${encodeURIComponent(customBaseUrl)}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.embedUrl).toContain(customBaseUrl);
      expect(response.body.data.previewUrl).toContain(customBaseUrl);
    });

    it('should return 404 for non-existent widget', async () => {
      const response = await request(app)
        .get('/api/embed-code/non-existent-id')
        .expect(404);

      expect(response.body.error.code).toBe('WIDGET_CONFIG_NOT_FOUND');
    });

    it('should return 400 for invalid widget ID format', async () => {
      const response = await request(app)
        .get('/api/embed-code/invalid-uuid')
        .expect(400);

      expect(response.body.error.code).toBe('INVALID_WIDGET_ID');
    });
  });

  describe('Error handling', () => {
    it('should handle malformed JSON in request body', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      // Express should handle this before our validation
    });

    it('should handle very large embed code validation', async () => {
      const largeEmbedCode = 'x'.repeat(100000);
      
      const response = await request(app)
        .post('/api/embed-code/validate')
        .send({
          embedCode: largeEmbedCode,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(false);
    });
  });

  describe('Response format consistency', () => {
    it('should return consistent success response format', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'valid-widget-id',
        })
        .expect(200);

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('data');
      expect(response.body.success).toBe(true);
    });

    it('should return consistent error response format', async () => {
      const response = await request(app)
        .post('/api/embed-code/generate')
        .send({
          configId: 'non-existent-id',
        })
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code');
      expect(response.body.error).toHaveProperty('message');
      expect(response.body.error).toHaveProperty('timestamp');
    });
  });
});