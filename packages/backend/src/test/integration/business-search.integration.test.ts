import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { BusinessSearchRequest } from '../../types/business.js';

// Mock environment variables for testing
process.env.GOOGLE_PLACES_API_KEY = 'test_places_key';
process.env.GOOGLE_MY_BUSINESS_API_KEY = 'test_my_business_key';

// Mock the Google API configuration
vi.mock('../../lib/google-api/config.js', () => ({
  loadGoogleApiConfig: vi.fn(() => ({
    placesApiKey: 'test_places_key',
    myBusinessApiKey: 'test_my_business_key',
    baseUrl: 'https://maps.googleapis.com',
    myBusinessBaseUrl: 'https://mybusinessbusinessinformation.googleapis.com',
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000,
  })),
}));

// Mock the Google API client
vi.mock('../../lib/google-api/client.js', () => ({
  getGoogleApiClient: vi.fn(() => ({
    findPlace: vi.fn().mockResolvedValue({ status: 'ZERO_RESULTS', candidates: [] }),
    getPlaceDetails: vi.fn().mockResolvedValue({ status: 'NOT_FOUND' }),
    searchPlaces: vi.fn().mockResolvedValue({ status: 'ZERO_RESULTS', results: [] }),
  })),
  GoogleApiClient: vi.fn(),
  GoogleApiError: class GoogleApiError extends Error {
    status?: string;
    code?: number;
    details?: any;
  },
}));

// Mock the business repository
vi.mock('../../lib/repositories/business.repository.js', () => ({
  BusinessRepository: vi.fn(() => ({
    findByPlaceId: vi.fn().mockResolvedValue({ success: true, data: null }),
    create: vi.fn().mockResolvedValue({ success: true, data: {} }),
    update: vi.fn().mockResolvedValue({ success: true, data: {} }),
  })),
}));

// Mock the database
vi.mock('../../lib/database.js', () => ({
  getPrismaClient: vi.fn(() => ({})),
  handleDatabaseError: vi.fn(),
}));

// Import routes after mocking
const { default: businessRoutes } = await import('../../routes/business.routes.js');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/business', businessRoutes);

describe('Business Search Integration Tests', () => {
  beforeAll(async () => {
    // Setup test database or any other initialization
  });

  afterAll(async () => {
    // Cleanup test database or any other cleanup
  });

  beforeEach(async () => {
    // Reset test data before each test
  });

  describe('POST /api/business/search', () => {
    it('should return 400 for missing query', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.message).toBe('Invalid request data');
    });

    it('should return 400 for empty query', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: '' });

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid location type', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ 
          query: 'Test Business',
          location: 123 
        });

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid Google Maps URL', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ 
          query: 'Test Business',
          googleMapsUrl: 'not-a-valid-url'
        });

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should accept valid search request with query only', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
      };

      // Note: This test will likely fail in CI/CD without proper Google API setup
      // In a real scenario, you would mock the Google API calls or use test credentials
      const response = await request(app)
        .post('/api/business/search')
        .send(searchRequest);

      // The response might be 502 (Google API error) or 200 (success) depending on setup
      expect([200, 502, 500]).toContain(response.status);
      
      if (response.status === 200) {
        expect(response.body).toHaveProperty('businesses');
        expect(response.body).toHaveProperty('totalResults');
        expect(Array.isArray(response.body.businesses)).toBe(true);
        expect(typeof response.body.totalResults).toBe('number');
      }
    });

    it('should accept valid search request with all parameters', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
        location: 'New York, NY',
        googleMapsUrl: 'https://maps.google.com/maps?place_id=ChIJ123456789',
      };

      const response = await request(app)
        .post('/api/business/search')
        .send(searchRequest);

      // The response might be 502 (Google API error) or 200 (success) depending on setup
      expect([200, 502, 500]).toContain(response.status);
      
      if (response.status === 200) {
        expect(response.body).toHaveProperty('businesses');
        expect(response.body).toHaveProperty('totalResults');
      }
    });

    it('should handle server errors gracefully', async () => {
      // This test simulates a server error by sending a request that might cause issues
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: 'Test Business' });

      // Should return either success or a proper error response
      if (response.status >= 400) {
        expect(response.body).toHaveProperty('error');
        expect(response.body.error).toHaveProperty('code');
        expect(response.body.error).toHaveProperty('message');
        expect(response.body.error).toHaveProperty('timestamp');
      }
    });

    it('should return proper error structure for Google API errors', async () => {
      // This test would ideally mock a Google API error
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: 'Test Business' });

      if (response.status === 502) {
        expect(response.body.error.code).toBe('GOOGLE_API_ERROR');
        expect(response.body.error.message).toBe('Failed to fetch data from Google API');
      }
    });
  });

  describe('GET /api/business/:id', () => {
    it('should return 400 for missing business ID', async () => {
      const response = await request(app)
        .get('/api/business/');

      expect(response.status).toBe(404); // Express returns 404 for missing route
    });

    it('should return 501 for not implemented endpoint', async () => {
      const response = await request(app)
        .get('/api/business/550e8400-e29b-41d4-a716-446655440000');

      expect(response.status).toBe(501);
      expect(response.body.error.code).toBe('NOT_IMPLEMENTED');
    });
  });

  describe('Error handling', () => {
    it('should return consistent error format', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({});

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code');
      expect(response.body.error).toHaveProperty('message');
      expect(response.body.error).toHaveProperty('timestamp');
      
      // Timestamp should be a valid ISO string
      expect(() => new Date(response.body.error.timestamp)).not.toThrow();
    });

    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}');

      expect(response.status).toBe(400);
    });

    it('should handle large request bodies', async () => {
      const largeQuery = 'a'.repeat(10000);
      
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: largeQuery });

      // Should either process successfully or return appropriate error
      expect([200, 400, 413, 502, 500]).toContain(response.status);
    });
  });

  describe('Request validation edge cases', () => {
    it('should trim whitespace from query', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: '   Test Business   ' });

      // Should accept the request (trimmed query is valid)
      expect([200, 502, 500]).toContain(response.status);
    });

    it('should reject query with only whitespace', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: '   ' });

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should handle special characters in query', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: 'Test & Business "Special" Characters!' });

      expect([200, 502, 500]).toContain(response.status);
    });

    it('should handle Unicode characters in query', async () => {
      const response = await request(app)
        .post('/api/business/search')
        .send({ query: 'Café München 北京烤鸭' });

      expect([200, 502, 500]).toContain(response.status);
    });
  });
});