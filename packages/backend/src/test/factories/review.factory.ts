import { faker } from '@faker-js/faker';
import { Review } from '../../types/review.js';

export class ReviewFactory {
  static create(overrides: Partial<Review> = {}): Review {
    const rating = faker.number.int({ min: 1, max: 5 });
    
    return {
      id: faker.string.uuid(),
      authorName: faker.person.fullName(),
      authorPhotoUrl: faker.helpers.maybe(() => faker.image.avatar(), { probability: 0.7 }),
      rating,
      text: this.generateReviewText(rating),
      publishedDate: faker.date.past({ years: 2 }).toISOString(),
      isVerified: faker.datatype.boolean({ probability: 0.8 }),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<Review> = {}): Review[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  static createPositive(overrides: Partial<Review> = {}): Review {
    const rating = faker.number.int({ min: 4, max: 5 });
    return this.create({
      rating,
      text: this.generatePositiveReviewText(),
      isVerified: true,
      ...overrides,
    });
  }

  static createNegative(overrides: Partial<Review> = {}): Review {
    const rating = faker.number.int({ min: 1, max: 2 });
    return this.create({
      rating,
      text: this.generateNegativeReviewText(),
      ...overrides,
    });
  }

  static createNeutral(overrides: Partial<Review> = {}): Review {
    return this.create({
      rating: 3,
      text: this.generateNeutralReviewText(),
      ...overrides,
    });
  }

  static createWithPhotos(overrides: Partial<Review> = {}): Review {
    return this.create({
      authorPhotoUrl: faker.image.avatar(),
      isVerified: true,
      ...overrides,
    });
  }

  static createRecent(overrides: Partial<Review> = {}): Review {
    return this.create({
      publishedDate: faker.date.recent({ days: 30 }).toISOString(),
      ...overrides,
    });
  }

  static createOld(overrides: Partial<Review> = {}): Review {
    return this.create({
      publishedDate: faker.date.past({ years: 2 }).toISOString(),
      ...overrides,
    });
  }

  private static generateReviewText(rating: number): string {
    const positiveWords = [
      'excellent', 'amazing', 'fantastic', 'wonderful', 'outstanding',
      'great', 'good', 'nice', 'pleasant', 'satisfying'
    ];

    const negativeWords = [
      'terrible', 'awful', 'horrible', 'disappointing', 'poor',
      'bad', 'unacceptable', 'frustrating', 'unsatisfactory'
    ];

    const neutralWords = [
      'okay', 'average', 'decent', 'acceptable', 'standard',
      'typical', 'normal', 'reasonable', 'adequate'
    ];

    if (rating >= 4) {
      return this.generatePositiveReviewText();
    } else if (rating <= 2) {
      return this.generateNegativeReviewText();
    } else {
      return this.generateNeutralReviewText();
    }
  }

  private static generatePositiveReviewText(): string {
    const templates = [
      'Excellent service and great food! Highly recommend this place.',
      'Amazing experience! The staff was friendly and the atmosphere was perfect.',
      'Outstanding quality and wonderful presentation. Will definitely come back!',
      'Great place with fantastic food. The service was top-notch.',
      'Wonderful dining experience. Everything was perfect from start to finish.',
      'Fantastic restaurant! The food was delicious and the service was excellent.',
      'Amazing food and great atmosphere. Highly recommended!',
      'Excellent quality and outstanding service. One of the best places in town!',
    ];

    return faker.helpers.arrayElement(templates);
  }

  private static generateNegativeReviewText(): string {
    const templates = [
      'Very disappointing experience. The service was slow and the food was cold.',
      'Poor quality food and terrible service. Would not recommend.',
      'Awful experience. The staff was rude and the food was overpriced.',
      'Disappointing visit. The food was bland and the service was poor.',
      'Terrible service and unacceptable food quality. Very frustrated.',
      'Poor experience overall. The food was not fresh and service was slow.',
      'Disappointing meal. Expected much better based on the reviews.',
      'Unacceptable service and poor food quality. Will not return.',
    ];

    return faker.helpers.arrayElement(templates);
  }

  private static generateNeutralReviewText(): string {
    const templates = [
      'Decent place with okay food. Nothing special but acceptable.',
      'Average experience. The food was okay and service was standard.',
      'Reasonable place with decent food. Could be better but not bad.',
      'Okay restaurant. The food was acceptable and service was average.',
      'Standard dining experience. Nothing outstanding but adequate.',
      'Decent food and reasonable service. Average overall experience.',
      'Acceptable place with okay food. Nothing to complain about.',
      'Average restaurant with standard food and service.',
    ];

    return faker.helpers.arrayElement(templates);
  }
}