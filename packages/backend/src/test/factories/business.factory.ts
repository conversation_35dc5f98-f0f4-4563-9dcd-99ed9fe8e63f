import { faker } from '@faker-js/faker';
import { Business } from '../../types/business.js';

export class BusinessFactory {
  static create(overrides: Partial<Business> = {}): Business {
    return {
      id: faker.string.uuid(),
      placeId: `ChIJ${faker.string.alphanumeric(27)}`,
      name: faker.company.name(),
      address: `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state({ abbreviated: true })} ${faker.location.zipCode()}`,
      rating: parseFloat(faker.number.float({ min: 1, max: 5, fractionDigits: 1 }).toFixed(1)),
      reviewCount: faker.number.int({ min: 1, max: 1000 }),
      photoUrl: faker.image.url(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<Business> = {}): Business[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }

  static createRestaurant(overrides: Partial<Business> = {}): Business {
    return this.create({
      name: `${faker.company.name()} Restaurant`,
      rating: parseFloat(faker.number.float({ min: 3.5, max: 5, fractionDigits: 1 }).toFixed(1)),
      reviewCount: faker.number.int({ min: 50, max: 500 }),
      ...overrides,
    });
  }

  static createCafe(overrides: Partial<Business> = {}): Business {
    return this.create({
      name: `${faker.company.name()} Cafe`,
      rating: parseFloat(faker.number.float({ min: 3.0, max: 4.8, fractionDigits: 1 }).toFixed(1)),
      reviewCount: faker.number.int({ min: 20, max: 200 }),
      ...overrides,
    });
  }

  static createWithHighRating(overrides: Partial<Business> = {}): Business {
    return this.create({
      rating: parseFloat(faker.number.float({ min: 4.5, max: 5, fractionDigits: 1 }).toFixed(1)),
      reviewCount: faker.number.int({ min: 100, max: 1000 }),
      ...overrides,
    });
  }

  static createWithLowRating(overrides: Partial<Business> = {}): Business {
    return this.create({
      rating: parseFloat(faker.number.float({ min: 1, max: 2.5, fractionDigits: 1 }).toFixed(1)),
      reviewCount: faker.number.int({ min: 5, max: 50 }),
      ...overrides,
    });
  }
}