import { faker } from '@faker-js/faker';
import { WidgetConfig, WidgetStyling, WidgetSettings } from '../../types/widget.js';

export class WidgetFactory {
  static createConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return {
      id: faker.string.uuid(),
      businessId: faker.string.uuid(),
      template: faker.helpers.arrayElement(['carousel', 'grid', 'list', 'badge', 'slider']),
      styling: this.createStyling(),
      settings: this.createSettings(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static createStyling(overrides: Partial<WidgetStyling> = {}): WidgetStyling {
    return {
      colors: {
        primary: faker.internet.color(),
        secondary: faker.internet.color(),
        background: '#ffffff',
        text: '#212529',
        border: '#dee2e6',
        ...overrides.colors,
      },
      fonts: {
        family: faker.helpers.arrayElement([
          'Arial, sans-serif',
          'Helvetica, sans-serif',
          'Georgia, serif',
          'Times New Roman, serif',
          'Roboto, sans-serif',
          'Open Sans, sans-serif',
        ]),
        size: faker.helpers.arrayElement(['12px', '14px', '16px', '18px']),
        weight: faker.helpers.arrayElement(['normal', 'bold', '300', '400', '500', '600']),
        ...overrides.fonts,
      },
      dimensions: {
        width: faker.helpers.arrayElement(['100%', '300px', '400px', '500px']),
        height: faker.helpers.arrayElement(['auto', '200px', '300px', '400px']),
        maxWidth: faker.helpers.arrayElement(['none', '600px', '800px', '1000px']),
        ...overrides.dimensions,
      },
      spacing: {
        padding: faker.helpers.arrayElement(['8px', '12px', '16px', '20px', '24px']),
        margin: faker.helpers.arrayElement(['0px', '8px', '12px', '16px']),
        gap: faker.helpers.arrayElement(['8px', '12px', '16px', '20px']),
        ...overrides.spacing,
      },
      borders: {
        radius: faker.helpers.arrayElement(['0px', '4px', '8px', '12px', '16px']),
        width: faker.helpers.arrayElement(['0px', '1px', '2px', '3px']),
        style: faker.helpers.arrayElement(['none', 'solid', 'dashed', 'dotted']),
        ...overrides.borders,
      },
      shadows: {
        enabled: faker.datatype.boolean(),
        blur: faker.helpers.arrayElement(['0px', '4px', '8px', '12px']),
        spread: faker.helpers.arrayElement(['0px', '2px', '4px']),
        color: 'rgba(0, 0, 0, 0.1)',
        ...overrides.shadows,
      },
      ...overrides,
    };
  }

  static createSettings(overrides: Partial<WidgetSettings> = {}): WidgetSettings {
    return {
      maxReviews: faker.number.int({ min: 3, max: 20 }),
      minRating: faker.number.int({ min: 1, max: 4 }),
      sortBy: faker.helpers.arrayElement(['newest', 'oldest', 'rating', 'helpful']),
      showPhotos: faker.datatype.boolean({ probability: 0.7 }),
      showDates: faker.datatype.boolean({ probability: 0.8 }),
      showRatings: faker.datatype.boolean({ probability: 0.9 }),
      showAuthorPhotos: faker.datatype.boolean({ probability: 0.6 }),
      autoRefresh: faker.datatype.boolean({ probability: 0.3 }),
      refreshInterval: faker.number.int({ min: 300, max: 3600 }), // 5 minutes to 1 hour
      animationSpeed: faker.helpers.arrayElement(['slow', 'normal', 'fast']),
      showBusinessInfo: faker.datatype.boolean({ probability: 0.8 }),
      headerText: faker.helpers.maybe(() => faker.lorem.words(3), { probability: 0.6 }),
      footerText: faker.helpers.maybe(() => faker.lorem.words(2), { probability: 0.4 }),
      ...overrides,
    };
  }

  static createCarouselConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      template: 'carousel',
      settings: this.createSettings({
        maxReviews: faker.number.int({ min: 5, max: 15 }),
        animationSpeed: 'normal',
        autoRefresh: true,
        ...overrides.settings,
      }),
      ...overrides,
    });
  }

  static createGridConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      template: 'grid',
      settings: this.createSettings({
        maxReviews: faker.number.int({ min: 6, max: 12 }),
        showPhotos: true,
        showAuthorPhotos: true,
        ...overrides.settings,
      }),
      styling: this.createStyling({
        dimensions: {
          width: '100%',
          height: 'auto',
          maxWidth: '800px',
        },
        spacing: {
          gap: '16px',
          padding: '20px',
          margin: '0px',
        },
        ...overrides.styling,
      }),
      ...overrides,
    });
  }

  static createBadgeConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      template: 'badge',
      settings: this.createSettings({
        maxReviews: 1,
        showBusinessInfo: true,
        showRatings: true,
        showPhotos: false,
        ...overrides.settings,
      }),
      styling: this.createStyling({
        dimensions: {
          width: '200px',
          height: '100px',
          maxWidth: '300px',
        },
        ...overrides.styling,
      }),
      ...overrides,
    });
  }

  static createListConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      template: 'list',
      settings: this.createSettings({
        maxReviews: faker.number.int({ min: 5, max: 10 }),
        showDates: true,
        showAuthorPhotos: true,
        ...overrides.settings,
      }),
      ...overrides,
    });
  }

  static createSliderConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      template: 'slider',
      settings: this.createSettings({
        maxReviews: faker.number.int({ min: 3, max: 8 }),
        animationSpeed: 'slow',
        autoRefresh: false,
        ...overrides.settings,
      }),
      ...overrides,
    });
  }

  static createManyConfigs(count: number, overrides: Partial<WidgetConfig> = {}): WidgetConfig[] {
    return Array.from({ length: count }, () => this.createConfig(overrides));
  }

  static createConfigsForBusiness(businessId: string, count: number = 3): WidgetConfig[] {
    const templates = ['carousel', 'grid', 'list', 'badge', 'slider'];
    
    return Array.from({ length: count }, (_, index) => {
      const template = templates[index % templates.length];
      
      switch (template) {
        case 'carousel':
          return this.createCarouselConfig({ businessId });
        case 'grid':
          return this.createGridConfig({ businessId });
        case 'badge':
          return this.createBadgeConfig({ businessId });
        case 'list':
          return this.createListConfig({ businessId });
        case 'slider':
          return this.createSliderConfig({ businessId });
        default:
          return this.createConfig({ businessId, template });
      }
    });
  }

  static createHighPerformanceConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      settings: this.createSettings({
        maxReviews: 5, // Limit for performance
        showPhotos: false, // Reduce image loading
        autoRefresh: false, // Reduce API calls
        animationSpeed: 'fast',
        ...overrides.settings,
      }),
      styling: this.createStyling({
        shadows: {
          enabled: false, // Reduce CSS complexity
        },
        ...overrides.styling,
      }),
      ...overrides,
    });
  }

  static createAccessibleConfig(overrides: Partial<WidgetConfig> = {}): WidgetConfig {
    return this.createConfig({
      styling: this.createStyling({
        colors: {
          primary: '#0066cc', // High contrast blue
          secondary: '#666666',
          background: '#ffffff',
          text: '#000000', // High contrast text
          border: '#cccccc',
        },
        fonts: {
          family: 'Arial, sans-serif', // Accessible font
          size: '16px', // Minimum readable size
          weight: 'normal',
        },
        ...overrides.styling,
      }),
      settings: this.createSettings({
        showRatings: true,
        showDates: true,
        showAuthorPhotos: true, // For better user identification
        ...overrides.settings,
      }),
      ...overrides,
    });
  }
}