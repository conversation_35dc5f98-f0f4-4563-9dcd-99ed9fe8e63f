import { describe, it, expect, beforeEach } from 'vitest';
import { BusinessRepository } from '../../lib/repositories/business.repository.js';
import { BusinessCreate, BusinessUpdate } from '../../types/index.js';
import '../setup.js';

describe('BusinessRepository Integration Tests', () => {
  let businessRepository: BusinessRepository;

  beforeEach(() => {
    businessRepository = new BusinessRepository();
  });

  describe('create', () => {
    it('should create a new business successfully', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
        name: 'Test Coffee Shop',
        address: '123 Main Street, Test City',
        rating: 4.5,
        reviewCount: 100,
        photoUrl: 'https://example.com/photo.jpg',
      };

      const result = await businessRepository.create(businessData);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.name).toBe(businessData.name);
      expect(result.data?.placeId).toBe(businessData.placeId);
      expect(result.data?.id).toBeDefined();
      expect(result.data?.createdAt).toBeDefined();
      expect(result.data?.updatedAt).toBeDefined();
    });

    it('should create business with minimal data', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY5',
        name: 'Minimal Business',
      };

      const result = await businessRepository.create(businessData);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.name).toBe(businessData.name);
      expect(result.data?.placeId).toBe(businessData.placeId);
      expect(result.data?.address).toBeNull();
      expect(result.data?.rating).toBeNull();
      expect(result.data?.reviewCount).toBeNull();
      expect(result.data?.photoUrl).toBeNull();
    });

    it('should fail to create business with duplicate place ID', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY6',
        name: 'First Business',
      };

      // Create first business
      const firstResult = await businessRepository.create(businessData);
      expect(firstResult.success).toBe(true);

      // Try to create second business with same place ID
      const duplicateData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY6',
        name: 'Duplicate Business',
      };

      const secondResult = await businessRepository.create(duplicateData);
      expect(secondResult.success).toBe(false);
      expect(secondResult.error).toBeDefined();
    });
  });

  describe('findById', () => {
    it('should find business by ID', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY7',
        name: 'Findable Business',
      };

      const createResult = await businessRepository.create(businessData);
      expect(createResult.success).toBe(true);
      
      const businessId = createResult.data!.id;
      const findResult = await businessRepository.findById(businessId);

      expect(findResult.success).toBe(true);
      expect(findResult.data).toBeDefined();
      expect(findResult.data?.id).toBe(businessId);
      expect(findResult.data?.name).toBe(businessData.name);
    });

    it('should return null for non-existent business ID', async () => {
      const nonExistentId = '123e4567-e89b-12d3-a456-426614174000';
      const result = await businessRepository.findById(nonExistentId);

      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
    });
  });

  describe('findByPlaceId', () => {
    it('should find business by place ID', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY8',
        name: 'Place ID Business',
      };

      const createResult = await businessRepository.create(businessData);
      expect(createResult.success).toBe(true);

      const findResult = await businessRepository.findByPlaceId(businessData.placeId);

      expect(findResult.success).toBe(true);
      expect(findResult.data).toBeDefined();
      expect(findResult.data?.placeId).toBe(businessData.placeId);
      expect(findResult.data?.name).toBe(businessData.name);
    });

    it('should return null for non-existent place ID', async () => {
      const nonExistentPlaceId = 'ChIJNonExistent';
      const result = await businessRepository.findByPlaceId(nonExistentPlaceId);

      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
    });
  });

  describe('update', () => {
    it('should update business successfully', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY9',
        name: 'Original Business',
        rating: 4.0,
      };

      const createResult = await businessRepository.create(businessData);
      expect(createResult.success).toBe(true);

      const businessId = createResult.data!.id;
      const updateData: BusinessUpdate = {
        name: 'Updated Business',
        rating: 4.8,
        reviewCount: 150,
      };

      const updateResult = await businessRepository.update(businessId, updateData);

      expect(updateResult.success).toBe(true);
      expect(updateResult.data).toBeDefined();
      expect(updateResult.data?.name).toBe(updateData.name);
      expect(updateResult.data?.rating).toBe(updateData.rating);
      expect(updateResult.data?.reviewCount).toBe(updateData.reviewCount);
      expect(updateResult.data?.placeId).toBe(businessData.placeId); // Should remain unchanged
    });

    it('should fail to update non-existent business', async () => {
      const nonExistentId = '123e4567-e89b-12d3-a456-426614174000';
      const updateData: BusinessUpdate = {
        name: 'Updated Business',
      };

      const result = await businessRepository.update(nonExistentId, updateData);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('delete', () => {
    it('should delete business successfully', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY10',
        name: 'Deletable Business',
      };

      const createResult = await businessRepository.create(businessData);
      expect(createResult.success).toBe(true);

      const businessId = createResult.data!.id;
      const deleteResult = await businessRepository.delete(businessId);

      expect(deleteResult.success).toBe(true);
      expect(deleteResult.data).toBe(true);

      // Verify business is deleted
      const findResult = await businessRepository.findById(businessId);
      expect(findResult.success).toBe(true);
      expect(findResult.data).toBeNull();
    });

    it('should fail to delete non-existent business', async () => {
      const nonExistentId = '123e4567-e89b-12d3-a456-426614174000';
      const result = await businessRepository.delete(nonExistentId);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('searchByName', () => {
    beforeEach(async () => {
      // Create test businesses
      const businesses: BusinessCreate[] = [
        { placeId: 'place1', name: 'Coffee Shop Alpha' },
        { placeId: 'place2', name: 'Coffee Shop Beta' },
        { placeId: 'place3', name: 'Tea House Gamma' },
        { placeId: 'place4', name: 'Restaurant Delta' },
      ];

      for (const business of businesses) {
        await businessRepository.create(business);
      }
    });

    it('should search businesses by name', async () => {
      const result = await businessRepository.searchByName('Coffee');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.data).toHaveLength(2);
      expect(result.data?.pagination.total).toBe(2);
      expect(result.data?.data.every(b => b.name.includes('Coffee'))).toBe(true);
    });

    it('should return empty results for non-matching search', async () => {
      const result = await businessRepository.searchByName('NonExistent');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.data).toHaveLength(0);
      expect(result.data?.pagination.total).toBe(0);
    });

    it('should handle pagination correctly', async () => {
      const result = await businessRepository.searchByName('', { page: 1, limit: 2 });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.data).toHaveLength(2);
      expect(result.data?.pagination.page).toBe(1);
      expect(result.data?.pagination.limit).toBe(2);
      expect(result.data?.pagination.total).toBe(4);
      expect(result.data?.pagination.totalPages).toBe(2);
      expect(result.data?.pagination.hasNext).toBe(true);
      expect(result.data?.pagination.hasPrev).toBe(false);
    });
  });

  describe('existsByPlaceId', () => {
    it('should return true for existing place ID', async () => {
      const businessData: BusinessCreate = {
        placeId: 'ChIJN1t_tDeuEmsRUsoyG83frY11',
        name: 'Existing Business',
      };

      await businessRepository.create(businessData);
      const result = await businessRepository.existsByPlaceId(businessData.placeId);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
    });

    it('should return false for non-existing place ID', async () => {
      const result = await businessRepository.existsByPlaceId('ChIJNonExistent');

      expect(result.success).toBe(true);
      expect(result.data).toBe(false);
    });
  });
});