import { beforeAll, afterAll, beforeEach } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Test database instance
let testPrisma: PrismaClient;

// Setup test database
beforeAll(async () => {
  // Use a test database URL or in-memory database
  const testDatabaseUrl = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL;
  
  if (!testDatabaseUrl) {
    throw new Error('TEST_DATABASE_URL or DATABASE_URL must be set for testing');
  }

  // Only create Prisma client for integration tests
  if (process.env.VITEST_POOL_ID && process.env.VITEST_POOL_ID.includes('integration')) {
    testPrisma = new PrismaClient({
      datasources: {
        db: {
          url: testDatabaseUrl,
        },
      },
    });

    await testPrisma.$connect();
  }
});

// Clean up after all tests
afterAll(async () => {
  if (testPrisma) {
    await testPrisma.$disconnect();
  }
});

// Clean database before each test
beforeEach(async () => {
  if (testPrisma) {
    // Clean up in reverse order of dependencies
    await testPrisma.reviewsCache.deleteMany();
    await testPrisma.widgetConfig.deleteMany();
    await testPrisma.business.deleteMany();
  }
});

export { testPrisma };