import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { WidgetDataService } from '../../../lib/services/widget-data.service.js';
import { ReviewsService } from '../../../lib/services/reviews.service.js';
import { WidgetConfigService } from '../../../lib/services/widget-config.service.js';

// Mock the dependencies
vi.mock('../../../lib/services/reviews.service.js');
vi.mock('../../../lib/services/widget-config.service.js');
vi.mock('../../../lib/logger.js', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
}));

describe('WidgetDataService', () => {
  let widgetDataService: WidgetDataService;
  let mockReviewsService: vi.Mocked<ReviewsService>;
  let mockWidgetConfigService: vi.Mocked<WidgetConfigService>;

  const mockWidgetConfig = {
    id: 'widget-123',
    businessId: 'business-456',
    template: 'carousel' as const,
    styling: {
      colors: { primary: '#000000', secondary: '#ffffff' },
      fonts: { family: 'Arial', size: 14 },
      dimensions: { width: 300, height: 400 },
      spacing: { padding: 16, margin: 8 }
    },
    settings: {
      maxReviews: 10,
      minRating: 4,
      sortBy: 'newest' as const,
      showPhotos: true,
      showDates: true,
      autoRefresh: true
    },
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02')
  };

  const mockBusiness = {
    id: 'business-456',
    placeId: 'place-789',
    name: 'Test Business',
    address: '123 Test St',
    rating: 4.5,
    reviewCount: 100,
    photoUrl: 'https://example.com/photo.jpg',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  };

  const mockReviews = [
    {
      id: 'review-1',
      authorName: 'John Doe',
      authorPhotoUrl: 'https://example.com/john.jpg',
      rating: 5,
      text: 'Great service!',
      publishedDate: new Date('2023-01-01'),
      isVerified: true
    },
    {
      id: 'review-2',
      authorName: 'Jane Smith',
      rating: 4,
      text: 'Good experience overall.',
      publishedDate: new Date('2023-01-02'),
      isVerified: false
    },
    {
      id: 'review-3',
      authorName: 'Bob Johnson',
      rating: 3,
      text: 'Average service.',
      publishedDate: new Date('2023-01-03'),
      isVerified: true
    }
  ];

  const mockReviewsData = {
    reviews: mockReviews,
    totalReviews: 100,
    averageRating: 4.5,
    lastUpdated: new Date('2023-01-01')
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mocked instances
    mockReviewsService = {
      getReviews: vi.fn(),
      getBusinessInfo: vi.fn()
    } as any;

    mockWidgetConfigService = {
      getWidgetConfig: vi.fn()
    } as any;

    // Mock the constructors to return our mocked instances
    vi.mocked(ReviewsService).mockImplementation(() => mockReviewsService);
    vi.mocked(WidgetConfigService).mockImplementation(() => mockWidgetConfigService);

    widgetDataService = new WidgetDataService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getWidgetData', () => {
    it('should return widget data successfully', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(mockWidgetConfig);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetData('widget-123');

      // Assert
      const expectedReviews = mockReviews
        .filter(r => r.rating >= 4)
        .sort((a, b) => b.publishedDate.getTime() - a.publishedDate.getTime()); // Sorted by newest
      
      expect(result).toEqual({
        reviews: expectedReviews,
        businessInfo: mockBusiness,
        totalReviews: 100,
        averageRating: 4.5,
        lastUpdated: mockReviewsData.lastUpdated,
        widgetConfig: mockWidgetConfig
      });

      expect(mockWidgetConfigService.getWidgetConfig).toHaveBeenCalledWith('widget-123');
      expect(mockReviewsService.getReviews).toHaveBeenCalledWith('business-456');
      expect(mockReviewsService.getBusinessInfo).toHaveBeenCalledWith('business-456');
    });

    it('should cache widget data', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(mockWidgetConfig);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      await widgetDataService.getWidgetData('widget-123');
      await widgetDataService.getWidgetData('widget-123'); // Second call should use cache

      // Assert
      expect(mockWidgetConfigService.getWidgetConfig).toHaveBeenCalledTimes(1);
      expect(mockReviewsService.getReviews).toHaveBeenCalledTimes(1);
      expect(mockReviewsService.getBusinessInfo).toHaveBeenCalledTimes(1);
    });

    it('should throw error when widget config not found', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(null);

      // Act & Assert
      await expect(widgetDataService.getWidgetData('nonexistent-widget'))
        .rejects.toThrow('Widget configuration not found for ID: nonexistent-widget');
    });

    it('should apply rating filter correctly', async () => {
      // Arrange
      const configWithHighMinRating = {
        ...mockWidgetConfig,
        settings: { ...mockWidgetConfig.settings, minRating: 5 }
      };
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(configWithHighMinRating);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetData('widget-123');

      // Assert
      expect(result.reviews).toHaveLength(1);
      expect(result.reviews[0].rating).toBe(5);
    });

    it('should apply max reviews limit', async () => {
      // Arrange
      const configWithLimit = {
        ...mockWidgetConfig,
        settings: { ...mockWidgetConfig.settings, maxReviews: 1, minRating: 1 }
      };
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(configWithLimit);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetData('widget-123');

      // Assert
      expect(result.reviews).toHaveLength(1);
    });

    it('should sort reviews by newest', async () => {
      // Arrange
      const configWithSorting = {
        ...mockWidgetConfig,
        settings: { ...mockWidgetConfig.settings, sortBy: 'newest' as const, minRating: 1 }
      };
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(configWithSorting);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetData('widget-123');

      // Assert
      expect(result.reviews[0].publishedDate.getTime())
        .toBeGreaterThanOrEqual(result.reviews[1]?.publishedDate.getTime() || 0);
    });

    it('should sort reviews by highest rating', async () => {
      // Arrange
      const configWithSorting = {
        ...mockWidgetConfig,
        settings: { ...mockWidgetConfig.settings, sortBy: 'highest_rating' as const, minRating: 1 }
      };
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(configWithSorting);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetData('widget-123');

      // Assert
      expect(result.reviews[0].rating).toBeGreaterThanOrEqual(result.reviews[1]?.rating || 0);
    });
  });

  describe('getWidgetDataWithFallback', () => {
    it('should return data normally when no error occurs', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(mockWidgetConfig);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetDataWithFallback('widget-123');

      // Assert
      expect(result).toBeDefined();
      expect(result.reviews).toBeDefined();
    });

    it('should return stale data when error occurs and stale data exists', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(mockWidgetConfig);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // First call to populate cache
      await widgetDataService.getWidgetData('widget-123');

      // Second call with error
      mockWidgetConfigService.getWidgetConfig.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await widgetDataService.getWidgetDataWithFallback('widget-123');

      // Assert
      expect(result).toBeDefined();
      expect(result.reviews).toBeDefined();
    });

    it('should throw error when no stale data available', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockRejectedValue(new Error('API Error'));

      // Act & Assert
      await expect(widgetDataService.getWidgetDataWithFallback('widget-123'))
        .rejects.toThrow('API Error');
    });
  });

  describe('preloadWidgetData', () => {
    it('should preload data successfully', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(mockWidgetConfig);
      mockReviewsService.getReviews.mockResolvedValue(mockReviewsData);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      await widgetDataService.preloadWidgetData('widget-123');

      // Assert
      expect(mockWidgetConfigService.getWidgetConfig).toHaveBeenCalledWith('widget-123');
    });

    it('should handle preload errors gracefully', async () => {
      // Arrange
      mockWidgetConfigService.getWidgetConfig.mockRejectedValue(new Error('Preload Error'));

      // Act & Assert
      await expect(widgetDataService.preloadWidgetData('widget-123'))
        .resolves.toBeUndefined(); // Should not throw
    });
  });

  describe('cache management', () => {
    it('should clear specific widget cache', () => {
      // Act
      widgetDataService.clearWidgetCache('widget-123');

      // Assert - No error should be thrown
      expect(() => widgetDataService.clearWidgetCache('widget-123')).not.toThrow();
    });

    it('should clear all cache', () => {
      // Act
      widgetDataService.clearAllCache();

      // Assert - No error should be thrown
      expect(() => widgetDataService.clearAllCache()).not.toThrow();
    });

    it('should return cache stats', () => {
      // Act
      const stats = widgetDataService.getCacheStats();

      // Assert
      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('entries');
      expect(typeof stats.size).toBe('number');
      expect(Array.isArray(stats.entries)).toBe(true);
    });
  });

  describe('date range filtering', () => {
    it('should filter reviews by date range', async () => {
      // Arrange
      const configWithDateRange = {
        ...mockWidgetConfig,
        settings: { 
          ...mockWidgetConfig.settings, 
          dateRange: 1, // Last 1 day
          minRating: 1 
        }
      };
      
      const recentReview = {
        ...mockReviews[0],
        publishedDate: new Date() // Today
      };
      
      const oldReview = {
        ...mockReviews[1],
        publishedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
      };

      const reviewsWithDateRange = {
        ...mockReviewsData,
        reviews: [recentReview, oldReview]
      };

      mockWidgetConfigService.getWidgetConfig.mockResolvedValue(configWithDateRange);
      mockReviewsService.getReviews.mockResolvedValue(reviewsWithDateRange);
      mockReviewsService.getBusinessInfo.mockResolvedValue(mockBusiness);

      // Act
      const result = await widgetDataService.getWidgetData('widget-123');

      // Assert
      expect(result.reviews).toHaveLength(1);
      expect(result.reviews[0].id).toBe(recentReview.id);
    });
  });
});