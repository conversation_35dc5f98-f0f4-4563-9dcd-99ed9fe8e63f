import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { ReviewsService, ReviewsFetchOptions, GoogleReviewData } from '../../../lib/services/reviews.service.js';
import { GoogleApiClient } from '../../../lib/google-api/client.js';
import { GoogleApiCache } from '../../../lib/google-api/cache.js';
import { ReviewsCacheRepository } from '../../../lib/repositories/reviews-cache.repository.js';
import { BusinessRepository } from '../../../lib/repositories/business.repository.js';
import { Review, BusinessInfo, ReviewsResponse, ReviewsCacheData } from '../../../types/index.js';

// Mock dependencies
vi.mock('../../../lib/google-api/client.js', () => ({
  GoogleApiClient: vi.fn(),
  getGoogleApiClient: vi.fn(),
  GoogleApiError: class extends Error {}
}));

vi.mock('../../../lib/google-api/cache.js', () => ({
  GoogleApiCache: vi.fn()
}));

vi.mock('../../../lib/google-api/config.js', () => ({
  loadGoogleApiConfig: vi.fn()
}));

vi.mock('../../../lib/repositories/reviews-cache.repository.js', () => ({
  ReviewsCacheRepository: vi.fn()
}));

vi.mock('../../../lib/repositories/business.repository.js', () => ({
  BusinessRepository: vi.fn()
}));

vi.mock('../../../lib/logger.js', () => ({
  logger: {
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }
}));

describe('ReviewsService', () => {
  let reviewsService: ReviewsService;
  let mockGoogleApiClient: any;
  let mockGoogleApiCache: any;
  let mockReviewsCacheRepository: any;
  let mockBusinessRepository: any;

  const mockPlaceId = 'ChIJN1t_tDeuEmsRUsoyG83frY4';
  const mockBusinessId = 'business-123';
  
  const mockBusiness = {
    id: mockBusinessId,
    placeId: mockPlaceId,
    name: 'Test Business',
    address: '123 Test St',
    rating: 4.5,
    reviewCount: 100,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockGoogleReviews: GoogleReviewData[] = [
    {
      author_name: 'John Doe',
      author_url: 'https://maps.google.com/user123',
      profile_photo_url: 'https://example.com/photo1.jpg',
      rating: 5,
      relative_time_description: '2 weeks ago',
      text: 'Great service!',
      time: 1640995200 // Unix timestamp
    },
    {
      author_name: 'Jane Smith',
      rating: 4,
      relative_time_description: '1 month ago',
      text: 'Good experience overall.',
      time: 1638316800
    }
  ];

  const mockReviews: Review[] = [
    {
      id: '1640995200_0',
      authorName: 'John Doe',
      authorPhotoUrl: 'https://example.com/photo1.jpg',
      rating: 5,
      text: 'Great service!',
      publishedDate: new Date(1640995200 * 1000),
      isVerified: true
    },
    {
      id: '1638316800_1',
      authorName: 'Jane Smith',
      rating: 4,
      text: 'Good experience overall.',
      publishedDate: new Date(1638316800 * 1000),
      isVerified: true
    }
  ];

  const mockBusinessInfo: BusinessInfo = {
    placeId: mockPlaceId,
    name: 'Test Business',
    address: '123 Test St',
    rating: 4.5,
    reviewCount: 100
  };

  const mockReviewsCacheData: ReviewsCacheData = {
    reviews: mockReviews,
    businessInfo: mockBusinessInfo,
    totalReviews: 100,
    averageRating: 4.5,
    lastUpdated: new Date(),
    expiresAt: new Date(Date.now() + 1800000) // 30 minutes from now
  };

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup mock implementations
    mockGoogleApiClient = {
      getPlaceDetails: vi.fn()
    };

    mockGoogleApiCache = {
      getPlaceDetails: vi.fn(),
      setPlaceDetails: vi.fn(),
      getStats: vi.fn().mockResolvedValue({ redisConnected: true })
    };

    mockReviewsCacheRepository = {
      findValidByBusinessId: vi.fn(),
      findByBusinessId: vi.fn(),
      upsert: vi.fn(),
      findNeedingRefresh: vi.fn(),
      deleteExpired: vi.fn(),
      getStats: vi.fn()
    };

    mockBusinessRepository = {
      findByPlaceId: vi.fn(),
      findById: vi.fn(),
      create: vi.fn()
    };

    // Mock the constructor functions
    vi.mocked(GoogleApiCache).mockImplementation(() => mockGoogleApiCache);
    vi.mocked(ReviewsCacheRepository).mockImplementation(() => mockReviewsCacheRepository);
    vi.mocked(BusinessRepository).mockImplementation(() => mockBusinessRepository);
    
    // Mock the getGoogleApiClient function
    const { getGoogleApiClient } = await import('../../../lib/google-api/client.js');
    vi.mocked(getGoogleApiClient).mockReturnValue(mockGoogleApiClient);
    
    // Mock config loading
    const { loadGoogleApiConfig } = await import('../../../lib/google-api/config.js');
    vi.mocked(loadGoogleApiConfig).mockReturnValue({
      placesApiKey: 'test-key',
      baseUrl: 'https://maps.googleapis.com/maps/api',
      timeout: 5000
    });

    // Create service instance with mocked dependencies
    reviewsService = new ReviewsService(mockGoogleApiCache);
    
    // Inject mocked repositories
    (reviewsService as any).reviewsCacheRepository = mockReviewsCacheRepository;
    (reviewsService as any).businessRepository = mockBusinessRepository;
    (reviewsService as any).googleApiClient = mockGoogleApiClient;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getReviewsByPlaceId', () => {
    it('should return cached reviews when available and not forcing refresh', async () => {
      // Setup mocks
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: { reviewsData: mockReviewsCacheData }
      });

      // Execute
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId);

      // Verify
      expect(result).toEqual({
        reviews: mockReviews,
        businessInfo: mockBusinessInfo,
        totalReviews: 100,
        averageRating: 4.5,
        lastUpdated: mockReviewsCacheData.lastUpdated
      });

      expect(mockBusinessRepository.findByPlaceId).toHaveBeenCalledWith(mockPlaceId);
      expect(mockReviewsCacheRepository.findValidByBusinessId).toHaveBeenCalledWith(mockBusinessId);
      expect(mockGoogleApiClient.getPlaceDetails).not.toHaveBeenCalled();
    });

    it('should fetch fresh reviews when cache is empty', async () => {
      // Setup mocks
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);
      
      mockGoogleApiClient.getPlaceDetails.mockResolvedValue({
        status: 'OK',
        result: {
          place_id: mockPlaceId,
          name: 'Test Business',
          formatted_address: '123 Test St',
          rating: 4.5,
          user_ratings_total: 100,
          reviews: mockGoogleReviews
        }
      });

      mockReviewsCacheRepository.upsert.mockResolvedValue({
        success: true,
        data: {}
      });

      // Execute
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId);

      // Verify
      expect(result.reviews).toHaveLength(2);
      expect(result.businessInfo.name).toBe('Test Business');
      expect(mockGoogleApiClient.getPlaceDetails).toHaveBeenCalled();
      expect(mockReviewsCacheRepository.upsert).toHaveBeenCalled();
    });

    it('should force refresh when forceRefresh option is true', async () => {
      // Setup mocks
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);
      
      mockGoogleApiClient.getPlaceDetails.mockResolvedValue({
        status: 'OK',
        result: {
          place_id: mockPlaceId,
          name: 'Test Business',
          formatted_address: '123 Test St',
          rating: 4.5,
          user_ratings_total: 100,
          reviews: mockGoogleReviews
        }
      });

      mockReviewsCacheRepository.upsert.mockResolvedValue({
        success: true,
        data: {}
      });

      // Execute
      const options: ReviewsFetchOptions = { forceRefresh: true };
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId, options);

      // Verify
      expect(result.reviews).toHaveLength(2);
      expect(mockReviewsCacheRepository.findValidByBusinessId).not.toHaveBeenCalled();
      expect(mockGoogleApiClient.getPlaceDetails).toHaveBeenCalled();
    });

    it('should create business if not found', async () => {
      // Setup mocks
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiClient.getPlaceDetails
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            place_id: mockPlaceId,
            name: 'Test Business',
            formatted_address: '123 Test St',
            rating: 4.5,
            user_ratings_total: 100
          }
        })
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            place_id: mockPlaceId,
            name: 'Test Business',
            formatted_address: '123 Test St',
            rating: 4.5,
            user_ratings_total: 100,
            reviews: mockGoogleReviews
          }
        });

      mockBusinessRepository.create.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);

      mockReviewsCacheRepository.upsert.mockResolvedValue({
        success: true,
        data: {}
      });

      // Execute
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId);

      // Verify
      expect(mockBusinessRepository.create).toHaveBeenCalledWith({
        placeId: mockPlaceId,
        name: 'Test Business',
        address: '123 Test St',
        rating: 4.5,
        reviewCount: 100
      });
      expect(result.reviews).toHaveLength(2);
    });

    it('should fallback to expired cache on API error', async () => {
      // Setup mocks
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);
      mockGoogleApiClient.getPlaceDetails.mockRejectedValue(new Error('API Error'));

      // Setup fallback cache
      mockReviewsCacheRepository.findByBusinessId.mockResolvedValue({
        success: true,
        data: { reviewsData: mockReviewsCacheData }
      });

      // Execute
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId);

      // Verify
      expect(result.reviews).toHaveLength(2);
      expect(mockReviewsCacheRepository.findByBusinessId).toHaveBeenCalledWith(mockBusinessId);
    });

    it('should apply filtering and sorting options', async () => {
      // Setup mocks with multiple reviews of different ratings
      const extendedReviews = [
        ...mockReviews,
        {
          id: '1635724800_2',
          authorName: 'Bob Wilson',
          rating: 3,
          text: 'Average experience.',
          publishedDate: new Date(1635724800 * 1000),
          isVerified: true
        }
      ];

      const extendedCacheData = {
        ...mockReviewsCacheData,
        reviews: extendedReviews
      };

      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: { reviewsData: extendedCacheData }
      });

      // Execute with filtering and sorting
      const options: ReviewsFetchOptions = {
        minRating: 4,
        sortBy: 'rating_high',
        maxReviews: 1
      };
      
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId, options);

      // Verify
      expect(result.reviews).toHaveLength(1);
      expect(result.reviews[0].rating).toBe(5); // Highest rating first
      expect(result.reviews[0].authorName).toBe('John Doe');
    });
  });

  describe('getReviewsByBusinessId', () => {
    it('should get reviews by business ID', async () => {
      // Setup mocks
      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      // Mock the findByPlaceId call that happens in getReviewsByPlaceId
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: { reviewsData: mockReviewsCacheData }
      });

      // Execute
      const result = await reviewsService.getReviewsByBusinessId(mockBusinessId);

      // Verify
      expect(result.reviews).toHaveLength(2);
      expect(mockBusinessRepository.findById).toHaveBeenCalledWith(mockBusinessId);
    });

    it('should throw error if business not found', async () => {
      // Setup mocks
      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: null
      });

      // Execute and verify
      await expect(reviewsService.getReviewsByBusinessId(mockBusinessId))
        .rejects.toThrow('Business not found: business-123');
    });
  });

  describe('refreshReviewsForBusinesses', () => {
    it('should refresh reviews for multiple businesses', async () => {
      const businessIds = ['business-1', 'business-2'];
      
      // Setup mocks
      mockBusinessRepository.findById
        .mockResolvedValueOnce({
          success: true,
          data: { ...mockBusiness, id: 'business-1' }
        })
        .mockResolvedValueOnce({
          success: true,
          data: { ...mockBusiness, id: 'business-2' }
        });

      // Mock findByPlaceId calls that happen in getReviewsByPlaceId
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);
      
      mockGoogleApiClient.getPlaceDetails.mockResolvedValue({
        status: 'OK',
        result: {
          place_id: mockPlaceId,
          name: 'Test Business',
          formatted_address: '123 Test St',
          rating: 4.5,
          user_ratings_total: 100,
          reviews: mockGoogleReviews
        }
      });

      mockReviewsCacheRepository.upsert.mockResolvedValue({
        success: true,
        data: {}
      });

      // Execute
      const result = await reviewsService.refreshReviewsForBusinesses(businessIds);

      // Verify
      expect(result.successful).toEqual(['business-1', 'business-2']);
      expect(result.failed).toHaveLength(0);
    });

    it('should handle failures gracefully', async () => {
      const businessIds = ['business-1', 'business-2'];
      
      // Setup mocks - first business succeeds, second fails
      mockBusinessRepository.findById
        .mockResolvedValueOnce({
          success: true,
          data: { ...mockBusiness, id: 'business-1' }
        })
        .mockResolvedValueOnce({
          success: false,
          data: null,
          error: 'Business not found'
        });

      // Mock successful path for first business
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: { reviewsData: mockReviewsCacheData }
      });

      // Execute
      const result = await reviewsService.refreshReviewsForBusinesses(businessIds);

      // Verify - at least one should succeed and one should fail
      expect(result.successful.length + result.failed.length).toBe(2);
      expect(result.failed.length).toBeGreaterThan(0);
    });
  });

  describe('getBusinessesNeedingRefresh', () => {
    it('should return businesses needing refresh', async () => {
      // Setup mocks
      mockReviewsCacheRepository.findNeedingRefresh.mockResolvedValue({
        success: true,
        data: [
          { businessId: 'business-1' },
          { businessId: 'business-2' }
        ]
      });

      // Execute
      const result = await reviewsService.getBusinessesNeedingRefresh(30);

      // Verify
      expect(result).toEqual(['business-1', 'business-2']);
      expect(mockReviewsCacheRepository.findNeedingRefresh).toHaveBeenCalledWith(30);
    });

    it('should return empty array on error', async () => {
      // Setup mocks
      mockReviewsCacheRepository.findNeedingRefresh.mockRejectedValue(new Error('DB Error'));

      // Execute
      const result = await reviewsService.getBusinessesNeedingRefresh();

      // Verify
      expect(result).toEqual([]);
    });
  });

  describe('cleanupExpiredCache', () => {
    it('should cleanup expired cache entries', async () => {
      // Setup mocks
      mockReviewsCacheRepository.deleteExpired.mockResolvedValue({
        success: true,
        data: 5
      });

      // Execute
      const result = await reviewsService.cleanupExpiredCache();

      // Verify
      expect(result).toBe(5);
      expect(mockReviewsCacheRepository.deleteExpired).toHaveBeenCalled();
    });

    it('should return 0 on error', async () => {
      // Setup mocks
      mockReviewsCacheRepository.deleteExpired.mockRejectedValue(new Error('DB Error'));

      // Execute
      const result = await reviewsService.cleanupExpiredCache();

      // Verify
      expect(result).toBe(0);
    });
  });

  describe('getCacheStats', () => {
    it('should return cache statistics', async () => {
      // Setup mocks
      mockReviewsCacheRepository.getStats.mockResolvedValue({
        success: true,
        data: { total: 10, expired: 2, valid: 8 }
      });

      mockGoogleApiCache.getStats.mockResolvedValue({
        redisConnected: true
      });

      // Execute
      const result = await reviewsService.getCacheStats();

      // Verify
      expect(result).toEqual({
        database: { total: 10, expired: 2, valid: 8 },
        redis: { redisConnected: true }
      });
    });

    it('should handle errors gracefully', async () => {
      // Setup mocks
      mockReviewsCacheRepository.getStats.mockRejectedValue(new Error('DB Error'));
      mockGoogleApiCache.getStats.mockRejectedValue(new Error('Redis Error'));

      // Execute
      const result = await reviewsService.getCacheStats();

      // Verify
      expect(result).toEqual({
        database: { total: 0, expired: 0, valid: 0 },
        redis: {}
      });
    });
  });

  describe('error handling', () => {
    it('should handle Google API errors gracefully', async () => {
      // Setup mocks
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);
      mockGoogleApiClient.getPlaceDetails.mockRejectedValue(new Error('Rate limit exceeded'));

      // No fallback cache available
      mockReviewsCacheRepository.findByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      // Execute and verify
      await expect(reviewsService.getReviewsByPlaceId(mockPlaceId))
        .rejects.toThrow('Failed to fetch reviews for place');
    });

    it('should handle invalid review data gracefully', async () => {
      // Setup mocks with invalid review data
      const invalidGoogleReviews = [
        {
          author_name: '', // Invalid: empty name
          rating: 10, // Invalid: rating > 5
          text: 'Test review',
          time: 1640995200
        }
      ];

      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockReviewsCacheRepository.findValidByBusinessId.mockResolvedValue({
        success: true,
        data: null
      });

      mockGoogleApiCache.getPlaceDetails.mockResolvedValue(null);
      
      mockGoogleApiClient.getPlaceDetails.mockResolvedValue({
        status: 'OK',
        result: {
          place_id: mockPlaceId,
          name: 'Test Business',
          formatted_address: '123 Test St',
          rating: 4.5,
          user_ratings_total: 100,
          reviews: invalidGoogleReviews
        }
      });

      mockReviewsCacheRepository.upsert.mockResolvedValue({
        success: true,
        data: {}
      });

      // Execute
      const result = await reviewsService.getReviewsByPlaceId(mockPlaceId);

      // Verify that invalid data was handled and corrected
      expect(result.reviews).toHaveLength(1);
      expect(result.reviews[0].authorName).toBe('Anonymous'); // Corrected empty name
      expect(result.reviews[0].rating).toBe(5); // Corrected invalid rating
    });
  });
});