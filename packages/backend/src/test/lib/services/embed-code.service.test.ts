import { describe, it, expect, beforeEach } from 'vitest';
import { EmbedCodeService } from '../../../lib/services/embed-code.service.js';
import { WidgetConfig, DEFAULT_WIDGET_STYLING, DEFAULT_WIDGET_SETTINGS } from '../../../types/widget.js';

describe('EmbedCodeService', () => {
  let embedCodeService: EmbedCodeService;
  let mockWidgetConfig: WidgetConfig;

  beforeEach(() => {
    embedCodeService = new EmbedCodeService(
      'https://test-widgets.example.com',
      'https://test-cdn.example.com/widget-runtime.js'
    );

    mockWidgetConfig = {
      id: '123e4567-e89b-12d3-a456-************',
      businessId: '987fcdeb-51a2-43d7-8f9e-123456789abc',
      templateType: 'carousel',
      stylingConfig: DEFAULT_WIDGET_STYLING,
      widgetSettings: DEFAULT_WIDGET_SETTINGS,
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
    };
  });

  describe('generateWidgetId', () => {
    it('should generate a valid UUID', () => {
      const widgetId = embedCodeService.generateWidgetId();
      
      expect(widgetId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('should generate unique IDs', () => {
      const id1 = embedCodeService.generateWidgetId();
      const id2 = embedCodeService.generateWidgetId();
      
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateEmbedCode', () => {
    it('should generate embed code with correct structure', () => {
      const result = embedCodeService.generateEmbedCode({
        widgetId: mockWidgetConfig.id,
        config: mockWidgetConfig,
      });

      expect(result.embedCode).toContain('reviews-widget-container');
      expect(result.embedCode).toContain(`data-widget-id="${mockWidgetConfig.id}"`);
      expect(result.embedCode).toContain('data-config=');
      expect(result.embedCode).toContain('<script>');
      expect(result.embedCode).toContain('ReviewsWidget.init');
      expect(result.widgetId).toBe(mockWidgetConfig.id);
      expect(result.embedUrl).toBe(`https://test-widgets.example.com/widget/${mockWidgetConfig.id}`);
      expect(result.previewUrl).toBe(`https://test-widgets.example.com/preview/${mockWidgetConfig.id}`);
    });

    it('should include widget dimensions in embed code', () => {
      const result = embedCodeService.generateEmbedCode({
        widgetId: mockWidgetConfig.id,
        config: mockWidgetConfig,
      });

      expect(result.embedCode).toContain(`width: ${mockWidgetConfig.stylingConfig.dimensions.width}px`);
      expect(result.embedCode).toContain(`height: ${mockWidgetConfig.stylingConfig.dimensions.height}px`);
    });

    it('should serialize configuration data correctly', () => {
      const result = embedCodeService.generateEmbedCode({
        widgetId: mockWidgetConfig.id,
        config: mockWidgetConfig,
      });

      const configMatch = result.embedCode.match(/data-config='([^']+)'/);
      expect(configMatch).toBeTruthy();
      
      if (configMatch) {
        const configData = JSON.parse(configMatch[1]);
        expect(configData.businessId).toBe(mockWidgetConfig.businessId);
        expect(configData.templateType).toBe(mockWidgetConfig.templateType);
        expect(configData.styling).toEqual(mockWidgetConfig.stylingConfig);
        expect(configData.settings).toEqual(mockWidgetConfig.widgetSettings);
      }
    });

    it('should use custom base URL when provided', () => {
      const customBaseUrl = 'https://custom.example.com';
      const result = embedCodeService.generateEmbedCode({
        widgetId: mockWidgetConfig.id,
        config: mockWidgetConfig,
        baseUrl: customBaseUrl,
      });

      expect(result.embedUrl).toBe(`${customBaseUrl}/widget/${mockWidgetConfig.id}`);
      expect(result.previewUrl).toBe(`${customBaseUrl}/preview/${mockWidgetConfig.id}`);
    });
  });

  describe('generateEmbedCodeForConfig', () => {
    it('should generate embed code using config ID as widget ID', () => {
      const result = embedCodeService.generateEmbedCodeForConfig(mockWidgetConfig);

      expect(result.widgetId).toBe(mockWidgetConfig.id);
      expect(result.embedCode).toContain(`data-widget-id="${mockWidgetConfig.id}"`);
    });
  });

  describe('updateEmbedCode', () => {
    it('should update config with new embed code and timestamp', () => {
      const originalUpdatedAt = mockWidgetConfig.updatedAt;
      
      const updatedConfig = embedCodeService.updateEmbedCode(mockWidgetConfig);

      expect(updatedConfig.embedCode).toBeDefined();
      expect(updatedConfig.embedCode).toContain('reviews-widget-container');
      expect(updatedConfig.updatedAt).not.toEqual(originalUpdatedAt);
      expect(updatedConfig.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe('generateIframeEmbedCode', () => {
    it('should generate iframe embed code', () => {
      const iframeCode = embedCodeService.generateIframeEmbedCode(
        mockWidgetConfig.id,
        mockWidgetConfig
      );

      expect(iframeCode).toContain('<iframe');
      expect(iframeCode).toContain(`src="https://test-widgets.example.com/embed/${mockWidgetConfig.id}"`);
      expect(iframeCode).toContain(`width="${mockWidgetConfig.stylingConfig.dimensions.width}"`);
      expect(iframeCode).toContain(`height="${mockWidgetConfig.stylingConfig.dimensions.height}"`);
      expect(iframeCode).toContain('frameborder="0"');
      expect(iframeCode).toContain('scrolling="no"');
    });

    it('should use custom base URL for iframe', () => {
      const customBaseUrl = 'https://custom.example.com';
      const iframeCode = embedCodeService.generateIframeEmbedCode(
        mockWidgetConfig.id,
        mockWidgetConfig,
        customBaseUrl
      );

      expect(iframeCode).toContain(`src="${customBaseUrl}/embed/${mockWidgetConfig.id}"`);
    });
  });

  describe('validateEmbedCode', () => {
    it('should validate correct embed code', () => {
      const result = embedCodeService.generateEmbedCode({
        widgetId: mockWidgetConfig.id,
        config: mockWidgetConfig,
      });

      const isValid = embedCodeService.validateEmbedCode(result.embedCode);
      expect(isValid).toBe(true);
    });

    it('should reject invalid embed code', () => {
      const invalidCodes = [
        '<div>Invalid code</div>',
        '<script>alert("test")</script>',
        '',
        'not html at all',
      ];

      invalidCodes.forEach(code => {
        const isValid = embedCodeService.validateEmbedCode(code);
        expect(isValid).toBe(false);
      });
    });

    it('should reject embed code missing required elements', () => {
      const incompleteCode = '<div class="reviews-widget-container"></div>';
      const isValid = embedCodeService.validateEmbedCode(incompleteCode);
      expect(isValid).toBe(false);
    });
  });

  describe('extractWidgetIdFromEmbedCode', () => {
    it('should extract widget ID from valid embed code', () => {
      const result = embedCodeService.generateEmbedCode({
        widgetId: mockWidgetConfig.id,
        config: mockWidgetConfig,
      });

      const extractedId = embedCodeService.extractWidgetIdFromEmbedCode(result.embedCode);
      expect(extractedId).toBe(mockWidgetConfig.id);
    });

    it('should return null for embed code without widget ID', () => {
      const codeWithoutId = '<div class="reviews-widget-container"></div>';
      const extractedId = embedCodeService.extractWidgetIdFromEmbedCode(codeWithoutId);
      expect(extractedId).toBeNull();
    });

    it('should return null for invalid embed code', () => {
      const invalidCode = 'not html';
      const extractedId = embedCodeService.extractWidgetIdFromEmbedCode(invalidCode);
      expect(extractedId).toBeNull();
    });
  });

  describe('generateCustomStyledEmbedCode', () => {
    it('should include custom CSS in embed code', () => {
      const customCss = '.custom-style { color: red; }';
      const result = embedCodeService.generateCustomStyledEmbedCode(
        mockWidgetConfig.id,
        mockWidgetConfig,
        customCss
      );

      expect(result.embedCode).toContain('<style>');
      expect(result.embedCode).toContain(customCss);
      expect(result.embedCode).toContain('</style>');
    });

    it('should work without custom CSS', () => {
      const result = embedCodeService.generateCustomStyledEmbedCode(
        mockWidgetConfig.id,
        mockWidgetConfig
      );

      expect(result.embedCode).not.toContain('<style>');
      expect(result.embedCode).toContain('reviews-widget-container');
    });
  });

  describe('edge cases', () => {
    it('should handle widget config with minimal dimensions', () => {
      const minimalConfig = {
        ...mockWidgetConfig,
        stylingConfig: {
          ...mockWidgetConfig.stylingConfig,
          dimensions: {
            width: 200,
            height: 150,
          },
        },
      };

      const result = embedCodeService.generateEmbedCode({
        widgetId: minimalConfig.id,
        config: minimalConfig,
      });

      expect(result.embedCode).toContain('width: 200px');
      expect(result.embedCode).toContain('height: 150px');
    });

    it('should handle widget config with maximum dimensions', () => {
      const maximalConfig = {
        ...mockWidgetConfig,
        stylingConfig: {
          ...mockWidgetConfig.stylingConfig,
          dimensions: {
            width: 1200,
            height: 800,
          },
        },
      };

      const result = embedCodeService.generateEmbedCode({
        widgetId: maximalConfig.id,
        config: maximalConfig,
      });

      expect(result.embedCode).toContain('width: 1200px');
      expect(result.embedCode).toContain('height: 800px');
    });

    it('should handle special characters in configuration', () => {
      const configWithSpecialChars = {
        ...mockWidgetConfig,
        widgetSettings: {
          ...mockWidgetConfig.widgetSettings,
          headerText: 'Reviews & "Testimonials" <Test>',
        },
      };

      const result = embedCodeService.generateEmbedCode({
        widgetId: configWithSpecialChars.id,
        config: configWithSpecialChars,
      });

      expect(result.embedCode).toContain('data-config=');
      // Should not break JSON structure
      const configMatch = result.embedCode.match(/data-config='([^']+)'/);
      expect(configMatch).toBeTruthy();
      
      if (configMatch) {
        expect(() => JSON.parse(configMatch[1])).not.toThrow();
      }
    });
  });
});