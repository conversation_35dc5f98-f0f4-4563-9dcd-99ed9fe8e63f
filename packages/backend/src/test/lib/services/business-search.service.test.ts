import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { BusinessSearchService } from '../../../lib/services/business-search.service.js';
import { BusinessSearchRequest } from '../../../types/business.js';

// Mock dependencies
vi.mock('../../../lib/google-api/client.js', () => ({
  GoogleApiClient: vi.fn(),
  getGoogleApiClient: vi.fn(),
  GoogleApiError: class GoogleApiError extends Error {
    status?: string;
    code?: number;
    details?: any;
  },
}));

vi.mock('../../../lib/google-api/config.js', () => ({
  loadGoogleApiConfig: vi.fn(() => ({
    placesApiKey: 'test_key',
    myBusinessApiKey: 'test_my_business_key',
    baseUrl: 'https://maps.googleapis.com',
    myBusinessBaseUrl: 'https://mybusinessbusinessinformation.googleapis.com',
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000,
  })),
}));

vi.mock('../../../lib/repositories/business.repository.js', () => ({
  BusinessRepository: vi.fn(),
}));

vi.mock('../../../lib/logger.js', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('BusinessSearchService', () => {
  let businessSearchService: BusinessSearchService;
  let mockGoogleApiClient: any;
  let mockBusinessRepository: any;

  const mockGooglePlaceResult = {
    place_id: 'ChIJ123456789',
    name: 'Test Business',
    formatted_address: '123 Test St, Test City, TC 12345',
    rating: 4.5,
    user_ratings_total: 100,
    photos: [{
      photo_reference: 'test_photo_ref',
      height: 400,
      width: 400,
    }],
  };

  const mockBusiness = {
    id: '550e8400-e29b-41d4-a716-446655440000',
    placeId: 'ChIJ123456789',
    name: 'Test Business',
    address: '123 Test St, Test City, TC 12345',
    rating: 4.5,
    reviewCount: 100,
    photoUrl: 'https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photo_reference=test_photo_ref&key=test_key',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();

    // Mock GoogleApiClient
    mockGoogleApiClient = {
      findPlace: vi.fn(),
      getPlaceDetails: vi.fn(),
      searchPlaces: vi.fn(),
    };

    // Mock BusinessRepository
    mockBusinessRepository = {
      findByPlaceId: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    };

    // Mock the getGoogleApiClient function
    const { getGoogleApiClient } = await import('../../../lib/google-api/client.js');
    vi.mocked(getGoogleApiClient).mockReturnValue(mockGoogleApiClient);

    // Mock BusinessRepository constructor
    const { BusinessRepository } = await import('../../../lib/repositories/business.repository.js');
    vi.mocked(BusinessRepository).mockImplementation(() => mockBusinessRepository);

    businessSearchService = new BusinessSearchService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('searchBusinesses', () => {
    it('should search businesses successfully with text query', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
      };

      // Mock Google API responses
      mockGoogleApiClient.findPlace.mockResolvedValue({
        status: 'OK',
        candidates: [mockGooglePlaceResult],
      });

      // Mock repository responses
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: null, // Business doesn't exist
      });

      mockBusinessRepository.create.mockResolvedValue({
        success: true,
        data: mockBusiness,
      });

      const result = await businessSearchService.searchBusinesses(searchRequest);

      expect(result).toEqual({
        businesses: [mockBusiness],
        totalResults: 1,
      });

      expect(mockGoogleApiClient.findPlace).toHaveBeenCalledWith(
        'Test Business',
        'textquery',
        ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total']
      );
    });

    it('should handle Google Maps URL with place ID', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
        googleMapsUrl: 'https://maps.google.com/maps?place_id=ChIJ123456789',
      };

      // Mock place details response
      mockGoogleApiClient.getPlaceDetails.mockResolvedValue({
        status: 'OK',
        result: mockGooglePlaceResult,
      });

      // Mock repository responses
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: null,
      });

      mockBusinessRepository.create.mockResolvedValue({
        success: true,
        data: mockBusiness,
      });

      const result = await businessSearchService.searchBusinesses(searchRequest);

      expect(result.businesses).toHaveLength(1);
      expect(mockGoogleApiClient.getPlaceDetails).toHaveBeenCalledWith(
        'ChIJ123456789',
        ['place_id', 'name', 'formatted_address', 'rating', 'user_ratings_total', 'photos']
      );
    });

    it('should update existing business when found in database', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
      };

      mockGoogleApiClient.findPlace.mockResolvedValue({
        status: 'OK',
        candidates: [mockGooglePlaceResult],
      });

      // Mock existing business in database
      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: mockBusiness,
      });

      mockBusinessRepository.update.mockResolvedValue({
        success: true,
        data: { ...mockBusiness, rating: 4.6 },
      });

      const result = await businessSearchService.searchBusinesses(searchRequest);

      expect(result.businesses).toHaveLength(1);
      expect(mockBusinessRepository.update).toHaveBeenCalledWith(
        mockBusiness.id,
        expect.objectContaining({
          name: 'Test Business',
          address: '123 Test St, Test City, TC 12345',
          rating: 4.5,
          reviewCount: 100,
        })
      );
    });

    it('should fallback to text search when findPlace returns no results', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
      };

      // Mock findPlace with no results
      mockGoogleApiClient.findPlace.mockResolvedValue({
        status: 'ZERO_RESULTS',
        candidates: [],
      });

      // Mock text search with results
      mockGoogleApiClient.searchPlaces.mockResolvedValue({
        status: 'OK',
        results: [mockGooglePlaceResult],
      });

      mockBusinessRepository.findByPlaceId.mockResolvedValue({
        success: true,
        data: null,
      });

      mockBusinessRepository.create.mockResolvedValue({
        success: true,
        data: mockBusiness,
      });

      const result = await businessSearchService.searchBusinesses(searchRequest);

      expect(result.businesses).toHaveLength(1);
      expect(mockGoogleApiClient.findPlace).toHaveBeenCalled();
      expect(mockGoogleApiClient.searchPlaces).toHaveBeenCalledWith('Test Business', undefined);
    });

    it('should handle Google API errors gracefully', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Test Business',
      };

      mockGoogleApiClient.findPlace.mockRejectedValue(new Error('API Error'));

      await expect(businessSearchService.searchBusinesses(searchRequest))
        .rejects.toThrow('Business search failed');
    });

    it('should return empty results when no businesses found', async () => {
      const searchRequest: BusinessSearchRequest = {
        query: 'Nonexistent Business',
      };

      mockGoogleApiClient.findPlace.mockResolvedValue({
        status: 'ZERO_RESULTS',
        candidates: [],
      });

      mockGoogleApiClient.searchPlaces.mockResolvedValue({
        status: 'ZERO_RESULTS',
        results: [],
      });

      const result = await businessSearchService.searchBusinesses(searchRequest);

      expect(result).toEqual({
        businesses: [],
        totalResults: 0,
      });
    });
  });

  describe('parseGoogleMapsUrl', () => {
    it('should extract place ID from place_id parameter', () => {
      const service = businessSearchService as any;
      const url = 'https://maps.google.com/maps?place_id=ChIJ123456789&other=param';
      
      const result = service.parseGoogleMapsUrl(url);
      
      expect(result.placeId).toBe('ChIJ123456789');
    });

    it('should extract place ID from /place/ URL format', () => {
      const service = businessSearchService as any;
      const url = 'https://maps.google.com/maps/place/Test+Business/data=!3m1!4b1!4m5!3m4!1s0x123456789:0x987654321!8m2!3d40.7128!4d-74.0060';
      
      const result = service.parseGoogleMapsUrl(url);
      
      // This test would need the actual regex pattern to work correctly
      expect(result).toBeDefined();
    });

    it('should extract query from /place/ path', () => {
      const service = businessSearchService as any;
      const url = 'https://maps.google.com/maps/place/Test+Business+Name';
      
      const result = service.parseGoogleMapsUrl(url);
      
      expect(result.query).toBe('Test Business Name');
    });

    it('should extract coordinates from URL', () => {
      const service = businessSearchService as any;
      const url = 'https://maps.google.com/maps/@40.7128,-74.0060,15z';
      
      const result = service.parseGoogleMapsUrl(url);
      
      expect(result.coordinates).toEqual({
        lat: 40.7128,
        lng: -74.0060,
      });
    });

    it('should handle invalid URLs gracefully', () => {
      const service = businessSearchService as any;
      const url = 'not-a-valid-url';
      
      const result = service.parseGoogleMapsUrl(url);
      
      expect(result).toEqual({});
    });
  });

  describe('validateSearchRequest', () => {
    it('should validate valid search request', () => {
      const request = {
        query: 'Test Business',
        location: 'New York, NY',
        googleMapsUrl: 'https://maps.google.com/maps?place_id=ChIJ123456789',
      };

      const result = BusinessSearchService.validateSearchRequest(request);

      expect(result).toEqual({
        query: 'Test Business',
        location: 'New York, NY',
        googleMapsUrl: 'https://maps.google.com/maps?place_id=ChIJ123456789',
      });
    });

    it('should throw error for missing query', () => {
      const request = {
        location: 'New York, NY',
      };

      expect(() => BusinessSearchService.validateSearchRequest(request))
        .toThrow('Search query is required and must be a non-empty string');
    });

    it('should throw error for empty query', () => {
      const request = {
        query: '   ',
      };

      expect(() => BusinessSearchService.validateSearchRequest(request))
        .toThrow('Search query is required and must be a non-empty string');
    });

    it('should throw error for invalid location type', () => {
      const request = {
        query: 'Test Business',
        location: 123,
      };

      expect(() => BusinessSearchService.validateSearchRequest(request))
        .toThrow('Location must be a string');
    });

    it('should throw error for invalid Google Maps URL', () => {
      const request = {
        query: 'Test Business',
        googleMapsUrl: 'not-a-valid-url',
      };

      expect(() => BusinessSearchService.validateSearchRequest(request))
        .toThrow('Invalid Google Maps URL format');
    });

    it('should trim whitespace from inputs', () => {
      const request = {
        query: '  Test Business  ',
        location: '  New York, NY  ',
        googleMapsUrl: '  https://maps.google.com/maps?place_id=ChIJ123456789  ',
      };

      const result = BusinessSearchService.validateSearchRequest(request);

      expect(result.query).toBe('Test Business');
      expect(result.location).toBe('New York, NY');
      expect(result.googleMapsUrl).toBe('https://maps.google.com/maps?place_id=ChIJ123456789');
    });
  });
});