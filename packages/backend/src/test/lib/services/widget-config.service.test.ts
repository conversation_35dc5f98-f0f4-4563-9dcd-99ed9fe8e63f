import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { WidgetConfigService } from '../../../lib/services/widget-config.service.js';
import { WidgetConfigRepository } from '../../../lib/repositories/widget-config.repository.js';
import { BusinessRepository } from '../../../lib/repositories/business.repository.js';
import { 
  WidgetConfig, 
  WidgetConfigCreate, 
  WidgetConfigUpdate,
  Business,
  DEFAULT_WIDGET_STYLING,
  DEFAULT_WIDGET_SETTINGS
} from '../../../types/index.js';

// Mock the repositories
vi.mock('../../../lib/repositories/widget-config.repository.js');
vi.mock('../../../lib/repositories/business.repository.js');
vi.mock('../../../lib/logger.js', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }
}));

describe('WidgetConfigService', () => {
  let widgetConfigService: WidgetConfigService;
  let mockWidgetConfigRepository: any;
  let mockBusinessRepository: any;

  const mockBusiness: Business = {
    id: '550e8400-e29b-41d4-a716-************',
    placeId: 'place-123',
    name: 'Test Business',
    address: '123 Test St',
    rating: 4.5,
    reviewCount: 100,
    photoUrl: 'https://example.com/photo.jpg',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  };

  const mockWidgetConfigCreate: WidgetConfigCreate = {
    businessId: '550e8400-e29b-41d4-a716-************',
    templateType: 'carousel',
    stylingConfig: {
      colors: {
        primary: '#4285f4',
        secondary: '#34a853',
        background: '#ffffff',
        text: '#333333',
        accent: '#fbbc04'
      },
      fonts: {
        family: 'Arial, sans-serif',
        size: 14,
        weight: 'normal',
        lineHeight: 1.4
      },
      dimensions: {
        width: 400,
        height: 300
      },
      spacing: {
        padding: 16,
        margin: 8,
        borderRadius: 8,
        itemSpacing: 12
      }
    },
    widgetSettings: {
      maxReviews: 10,
      minRating: 1,
      sortBy: 'newest',
      showPhotos: true,
      showDates: true,
      autoRefresh: true,
      refreshInterval: 3600,
      showBusinessInfo: true
    }
  };

  const mockWidgetConfig: WidgetConfig = {
    id: '550e8400-e29b-41d4-a716-446655440001',
    ...mockWidgetConfigCreate,
    embedCode: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock the repository methods
    mockWidgetConfigRepository = {
      create: vi.fn(),
      findById: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      findByBusinessId: vi.fn(),
      findAll: vi.fn(),
    };
    
    mockBusinessRepository = {
      findById: vi.fn(),
    };
    
    // Mock the constructor to return our mocked instances
    vi.mocked(WidgetConfigRepository).mockImplementation(() => mockWidgetConfigRepository);
    vi.mocked(BusinessRepository).mockImplementation(() => mockBusinessRepository);
    
    // Create new instance for each test
    widgetConfigService = new WidgetConfigService();
  });

  describe('createWidgetConfig', () => {
    it('should create a widget configuration successfully', async () => {
      // Arrange
      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockWidgetConfigRepository.create.mockResolvedValue({
        success: true,
        data: mockWidgetConfig
      });
      
      mockWidgetConfigRepository.update.mockResolvedValue({
        success: true,
        data: { ...mockWidgetConfig, embedCode: '<!-- Google Reviews Widget -->' }
      });

      // Act
      const result = await widgetConfigService.createWidgetConfig(mockWidgetConfigCreate);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ ...mockWidgetConfig, embedCode: '<!-- Google Reviews Widget -->' });
      expect(mockBusinessRepository.findById).toHaveBeenCalledWith('550e8400-e29b-41d4-a716-************');
      expect(mockWidgetConfigRepository.create).toHaveBeenCalled();
    });

    it('should return validation error for invalid data', async () => {
      // Arrange
      const invalidData = {
        ...mockWidgetConfigCreate,
        businessId: 'invalid-uuid'
      };

      // Act
      const result = await widgetConfigService.createWidgetConfig(invalidData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe('Invalid widget configuration data');
    });

    it('should return error when business does not exist', async () => {
      // Arrange
      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: null
      });

      // Act
      const result = await widgetConfigService.createWidgetConfig(mockWidgetConfigCreate);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('BUSINESS_NOT_FOUND');
      expect(result.error?.message).toBe('Business not found');
    });

    it('should merge with default styling and settings', async () => {
      // Arrange
      const partialConfig: WidgetConfigCreate = {
        businessId: '550e8400-e29b-41d4-a716-************',
        templateType: 'carousel',
        stylingConfig: {
          colors: {
            primary: '#ff0000',
            secondary: '#34a853',
            background: '#ffffff',
            text: '#333333',
            accent: '#fbbc04'
          },
          fonts: DEFAULT_WIDGET_STYLING.fonts,
          dimensions: DEFAULT_WIDGET_STYLING.dimensions,
          spacing: DEFAULT_WIDGET_STYLING.spacing
        },
        widgetSettings: {
          maxReviews: 5,
          minRating: 1,
          sortBy: 'newest',
          showPhotos: true,
          showDates: true,
          autoRefresh: true,
          showBusinessInfo: true
        }
      };

      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockWidgetConfigRepository.create.mockResolvedValue({
        success: true,
        data: mockWidgetConfig
      });
      
      mockWidgetConfigRepository.update.mockResolvedValue({
        success: true,
        data: { ...mockWidgetConfig, embedCode: '<!-- Google Reviews Widget -->' }
      });

      // Act
      const result = await widgetConfigService.createWidgetConfig(partialConfig);

      // Assert
      expect(result.success).toBe(true);
      expect(mockWidgetConfigRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          stylingConfig: expect.objectContaining({
            colors: expect.objectContaining({
              primary: '#ff0000'
            })
          }),
          widgetSettings: expect.objectContaining({
            maxReviews: 5,
            refreshInterval: DEFAULT_WIDGET_SETTINGS.refreshInterval
          })
        })
      );
    });

    it('should handle database errors', async () => {
      // Arrange
      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: mockBusiness
      });
      
      mockWidgetConfigRepository.create.mockResolvedValue({
        success: false,
        error: 'Database connection failed'
      });

      // Act
      const result = await widgetConfigService.createWidgetConfig(mockWidgetConfigCreate);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
    });
  });

  describe('getWidgetConfig', () => {
    it('should get widget configuration successfully', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: mockWidgetConfig
      });

      // Act
      const result = await widgetConfigService.getWidgetConfigById('widget-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockWidgetConfig);
      expect(mockWidgetConfigRepository.findById).toHaveBeenCalledWith('widget-123');
    });

    it('should return validation error for invalid ID', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: false,
        error: 'Invalid ID'
      });

      // Act
      const result = await widgetConfigService.getWidgetConfigById('');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
    });

    it('should return not found error when widget does not exist', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: null
      });

      // Act
      const result = await widgetConfigService.getWidgetConfigById('widget-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('WIDGET_NOT_FOUND');
      expect(result.error?.message).toBe('Widget configuration not found');
    });
  });

  describe('updateWidgetConfig', () => {
    const updateData: WidgetConfigUpdate = {
      templateType: 'grid-with-summary',
      stylingConfig: {
        colors: {
          primary: '#ff0000',
          secondary: '#34a853',
          background: '#ffffff',
          text: '#333333',
          accent: '#fbbc04'
        },
        fonts: DEFAULT_WIDGET_STYLING.fonts,
        dimensions: DEFAULT_WIDGET_STYLING.dimensions,
        spacing: DEFAULT_WIDGET_STYLING.spacing
      }
    };

    it('should update widget configuration successfully', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: mockWidgetConfig
      });
      
      mockWidgetConfigRepository.update.mockResolvedValue({
        success: true,
        data: { ...mockWidgetConfig, ...updateData }
      });

      // Act
      const result = await widgetConfigService.updateWidgetConfig('widget-123', updateData);

      // Assert
      expect(result.success).toBe(true);
      expect(mockWidgetConfigRepository.findById).toHaveBeenCalledWith('widget-123');
      expect(mockWidgetConfigRepository.update).toHaveBeenCalledWith('widget-123', updateData);
    });

    it('should return validation error for invalid data', async () => {
      // Arrange
      const invalidUpdateData = {
        templateType: 'invalid-template'
      };

      // Act
      const result = await widgetConfigService.updateWidgetConfig('widget-123', invalidUpdateData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should verify business exists when businessId is updated', async () => {
      // Arrange
      const updateWithBusinessId: WidgetConfigUpdate = {
        businessId: '550e8400-e29b-41d4-a716-446655440002'
      };

      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: mockWidgetConfig
      });

      mockBusinessRepository.findById.mockResolvedValue({
        success: true,
        data: mockBusiness
      });

      mockWidgetConfigRepository.update.mockResolvedValue({
        success: true,
        data: { ...mockWidgetConfig, businessId: '550e8400-e29b-41d4-a716-446655440002' }
      });

      // Act
      const result = await widgetConfigService.updateWidgetConfig('550e8400-e29b-41d4-a716-446655440001', updateWithBusinessId);

      // Assert
      expect(result.success).toBe(true);
      expect(mockBusinessRepository.findById).toHaveBeenCalledWith('550e8400-e29b-41d4-a716-446655440002');
    });

    it('should return error when widget does not exist', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: null
      });

      // Act
      const result = await widgetConfigService.updateWidgetConfig('widget-123', updateData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('WIDGET_NOT_FOUND');
    });
  });

  describe('deleteWidgetConfig', () => {
    it('should delete widget configuration successfully', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: mockWidgetConfig
      });
      
      mockWidgetConfigRepository.delete.mockResolvedValue({
        success: true,
        data: true
      });

      // Act
      const result = await widgetConfigService.deleteWidgetConfig('widget-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockWidgetConfigRepository.findById).toHaveBeenCalledWith('widget-123');
      expect(mockWidgetConfigRepository.delete).toHaveBeenCalledWith('widget-123');
    });

    it('should return validation error for invalid ID', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: false,
        error: 'Invalid ID'
      });

      // Act
      const result = await widgetConfigService.deleteWidgetConfig('');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
    });

    it('should return error when widget does not exist', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: null
      });

      // Act
      const result = await widgetConfigService.deleteWidgetConfig('widget-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('WIDGET_NOT_FOUND');
    });
  });

  describe('getWidgetConfigsByBusinessId', () => {
    it('should get widget configurations by business ID successfully', async () => {
      // Arrange
      const mockPaginatedResponse = {
        data: [mockWidgetConfig],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      };

      mockWidgetConfigRepository.findByBusinessId.mockResolvedValue({
        success: true,
        data: mockPaginatedResponse
      });

      // Act
      const result = await widgetConfigService.getWidgetConfigsByBusinessId('business-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPaginatedResponse);
      expect(mockWidgetConfigRepository.findByBusinessId).toHaveBeenCalledWith('business-123', undefined);
    });

    it('should return error when database operation fails', async () => {
      // Arrange
      mockWidgetConfigRepository.findByBusinessId.mockResolvedValue({
        success: false,
        error: 'Database connection failed'
      });

      // Act
      const result = await widgetConfigService.getWidgetConfigsByBusinessId('business-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
    });
  });

  describe('generateEmbedCode', () => {
    it('should generate embed code successfully', async () => {
      // Arrange
      const testWidgetConfig = { ...mockWidgetConfig, id: 'widget-123' };
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: testWidgetConfig
      });

      // Act
      const result = await widgetConfigService.generateEmbedCode('widget-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toContain('grw-widget-widget-123');
      expect(result.data).toContain('data-widget-id="widget-123"');
      expect(result.data).toContain('Google Reviews Widget');
    });

    it('should return error when widget does not exist', async () => {
      // Arrange
      mockWidgetConfigRepository.findById.mockResolvedValue({
        success: true,
        data: null
      });

      // Act
      const result = await widgetConfigService.generateEmbedCode('widget-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('WIDGET_NOT_FOUND');
    });
  });

  describe('validateWidgetConfig', () => {
    it('should validate widget configuration successfully', () => {
      // Act
      const result = widgetConfigService.validateWidgetConfig(mockWidgetConfigCreate);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
      expect(result.warnings).toEqual([]);
    });

    it('should return validation error for invalid data', () => {
      // Arrange
      const invalidData = {
        ...mockWidgetConfigCreate,
        businessId: 'invalid-uuid'
      };

      // Act
      const result = widgetConfigService.validateWidgetConfig(invalidData);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('businessId');
    });

    it('should validate update data correctly', () => {
      // Arrange
      const updateData: WidgetConfigUpdate = {
        templateType: 'grid-with-summary'
      };

      // Act
      const result = widgetConfigService.validateWidgetConfig(updateData);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
      expect(result.warnings).toEqual([]);
    });
  });
});