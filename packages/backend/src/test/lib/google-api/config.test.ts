import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { loadGoogleApiConfig, GOOGLE_API_ENDPOINTS } from '../../../lib/google-api/config.js';

describe('Google API Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset environment
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('loadGoogleApiConfig', () => {
    it('should load valid configuration from environment variables', () => {
      process.env.GOOGLE_PLACES_API_KEY = 'test-places-key';
      process.env.GOOGLE_MY_BUSINESS_API_KEY = 'test-mybusiness-key';

      const config = loadGoogleApiConfig();

      expect(config).toEqual({
        placesApiKey: 'test-places-key',
        myBusinessApiKey: 'test-mybusiness-key',
        baseUrl: 'https://maps.googleapis.com',
        myBusinessBaseUrl: 'https://mybusinessbusinessinformation.googleapis.com',
        timeout: 10000,
        retryAttempts: 3,
        retryDelay: 1000,
      });
    });

    it('should use custom values when provided', () => {
      process.env.GOOGLE_PLACES_API_KEY = 'test-places-key';
      process.env.GOOGLE_MY_BUSINESS_API_KEY = 'test-mybusiness-key';
      process.env.GOOGLE_API_BASE_URL = 'https://custom.googleapis.com';
      process.env.GOOGLE_API_TIMEOUT = '5000';
      process.env.GOOGLE_API_RETRY_ATTEMPTS = '5';
      process.env.GOOGLE_API_RETRY_DELAY = '2000';

      const config = loadGoogleApiConfig();

      expect(config.baseUrl).toBe('https://custom.googleapis.com');
      expect(config.timeout).toBe(5000);
      expect(config.retryAttempts).toBe(5);
      expect(config.retryDelay).toBe(2000);
    });

    it('should throw error when Places API key is missing', () => {
      process.env.GOOGLE_MY_BUSINESS_API_KEY = 'test-mybusiness-key';
      delete process.env.GOOGLE_PLACES_API_KEY;

      expect(() => loadGoogleApiConfig()).toThrow(
        'Google API configuration validation failed. Missing or invalid fields: placesApiKey'
      );
    });

    it('should throw error when My Business API key is missing', () => {
      process.env.GOOGLE_PLACES_API_KEY = 'test-places-key';
      delete process.env.GOOGLE_MY_BUSINESS_API_KEY;

      expect(() => loadGoogleApiConfig()).toThrow(
        'Google API configuration validation failed. Missing or invalid fields: myBusinessApiKey'
      );
    });

    it('should throw error when both API keys are missing', () => {
      delete process.env.GOOGLE_PLACES_API_KEY;
      delete process.env.GOOGLE_MY_BUSINESS_API_KEY;

      expect(() => loadGoogleApiConfig()).toThrow(
        'Google API configuration validation failed'
      );
    });

    it('should handle invalid URL format', () => {
      process.env.GOOGLE_PLACES_API_KEY = 'test-places-key';
      process.env.GOOGLE_MY_BUSINESS_API_KEY = 'test-mybusiness-key';
      process.env.GOOGLE_API_BASE_URL = 'invalid-url';

      expect(() => loadGoogleApiConfig()).toThrow();
    });
  });

  describe('GOOGLE_API_ENDPOINTS', () => {
    it('should have correct Places API endpoints', () => {
      expect(GOOGLE_API_ENDPOINTS.PLACES.FIND_PLACE).toBe('/maps/api/place/findplacefromtext/json');
      expect(GOOGLE_API_ENDPOINTS.PLACES.PLACE_DETAILS).toBe('/maps/api/place/details/json');
      expect(GOOGLE_API_ENDPOINTS.PLACES.NEARBY_SEARCH).toBe('/maps/api/place/nearbysearch/json');
      expect(GOOGLE_API_ENDPOINTS.PLACES.TEXT_SEARCH).toBe('/maps/api/place/textsearch/json');
    });

    it('should have correct My Business API endpoints', () => {
      expect(GOOGLE_API_ENDPOINTS.MY_BUSINESS.LOCATIONS).toBe('/v1/locations');
      expect(GOOGLE_API_ENDPOINTS.MY_BUSINESS.REVIEWS).toBe('/v1/locations/{locationId}/reviews');
    });
  });
});