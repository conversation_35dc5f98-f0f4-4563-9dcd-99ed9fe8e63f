import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GoogleApiRateLimiter } from '../../../lib/google-api/rate-limiter.js';

// Mock rate-limiter-flexible
vi.mock('rate-limiter-flexible', () => ({
  RateLimiterRedis: vi.fn().mockImplementation((config) => ({
    consume: vi.fn(),
    get: vi.fn(),
    delete: vi.fn(),
    config,
  })),
  RateLimiterMemory: vi.fn().mockImplementation((config) => ({
    consume: vi.fn(),
    get: vi.fn(),
    delete: vi.fn(),
    config,
  })),
}));

// Mock logger
vi.mock('../../../lib/logger.js', () => ({
  logger: {
    warn: vi.fn(),
  },
}));

describe('GoogleApiRateLimiter', () => {
  let mockRedisClient: any;
  let rateLimiter: GoogleApiRateLimiter;

  beforeEach(() => {
    mockRedisClient = {
      ping: vi.fn(),
    };
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create rate limiter with Redis client', () => {
      rateLimiter = new GoogleApiRateLimiter(mockRedisClient);
      expect(rateLimiter).toBeInstanceOf(GoogleApiRateLimiter);
    });

    it('should create rate limiter without Redis client', () => {
      rateLimiter = new GoogleApiRateLimiter();
      expect(rateLimiter).toBeInstanceOf(GoogleApiRateLimiter);
    });
  });

  describe('checkPlacesApiLimit', () => {
    beforeEach(() => {
      rateLimiter = new GoogleApiRateLimiter(mockRedisClient);
    });

    it('should pass when rate limit is not exceeded', async () => {
      const mockConsume = vi.fn().mockResolvedValue(undefined);
      (rateLimiter as any).placesLimiter.consume = mockConsume;

      await expect(rateLimiter.checkPlacesApiLimit()).resolves.toBeUndefined();
      expect(mockConsume).toHaveBeenCalledWith('default');
    });

    it('should throw error when rate limit is exceeded', async () => {
      const mockConsume = vi.fn().mockRejectedValue({
        msBeforeNext: 30000,
      });
      (rateLimiter as any).placesLimiter.consume = mockConsume;

      await expect(rateLimiter.checkPlacesApiLimit()).rejects.toThrow(
        'Rate limit exceeded. Retry after 30 seconds.'
      );
    });

    it('should use custom key when provided', async () => {
      const mockConsume = vi.fn().mockResolvedValue(undefined);
      (rateLimiter as any).placesLimiter.consume = mockConsume;

      await rateLimiter.checkPlacesApiLimit('custom-key');
      expect(mockConsume).toHaveBeenCalledWith('custom-key');
    });
  });

  describe('checkMyBusinessApiLimit', () => {
    beforeEach(() => {
      rateLimiter = new GoogleApiRateLimiter(mockRedisClient);
    });

    it('should pass when rate limit is not exceeded', async () => {
      const mockConsume = vi.fn().mockResolvedValue(undefined);
      (rateLimiter as any).myBusinessLimiter.consume = mockConsume;

      await expect(rateLimiter.checkMyBusinessApiLimit()).resolves.toBeUndefined();
      expect(mockConsume).toHaveBeenCalledWith('default');
    });

    it('should throw error when rate limit is exceeded', async () => {
      const mockConsume = vi.fn().mockRejectedValue({
        msBeforeNext: 60000,
      });
      (rateLimiter as any).myBusinessLimiter.consume = mockConsume;

      await expect(rateLimiter.checkMyBusinessApiLimit()).rejects.toThrow(
        'Rate limit exceeded. Retry after 60 seconds.'
      );
    });
  });

  describe('getRemainingPoints', () => {
    beforeEach(() => {
      rateLimiter = new GoogleApiRateLimiter(mockRedisClient);
    });

    it('should return remaining points for places API', async () => {
      const mockGet = vi.fn().mockResolvedValue({ remainingPoints: 50 });
      (rateLimiter as any).placesLimiter.get = mockGet;

      const result = await rateLimiter.getRemainingPoints('places');
      expect(result).toBe(50);
      expect(mockGet).toHaveBeenCalledWith('default');
    });

    it('should return remaining points for mybusiness API', async () => {
      const mockGet = vi.fn().mockResolvedValue({ remainingPoints: 25 });
      (rateLimiter as any).myBusinessLimiter.get = mockGet;

      const result = await rateLimiter.getRemainingPoints('mybusiness');
      expect(result).toBe(25);
      expect(mockGet).toHaveBeenCalledWith('default');
    });

    it('should return 0 when no data is available', async () => {
      const mockGet = vi.fn().mockResolvedValue(null);
      (rateLimiter as any).placesLimiter.get = mockGet;

      const result = await rateLimiter.getRemainingPoints('places');
      expect(result).toBe(0);
    });
  });

  describe('resetLimits', () => {
    beforeEach(() => {
      rateLimiter = new GoogleApiRateLimiter(mockRedisClient);
    });

    it('should reset both limiters when no API specified', async () => {
      const mockPlacesDelete = vi.fn().mockResolvedValue(undefined);
      const mockMyBusinessDelete = vi.fn().mockResolvedValue(undefined);
      (rateLimiter as any).placesLimiter.delete = mockPlacesDelete;
      (rateLimiter as any).myBusinessLimiter.delete = mockMyBusinessDelete;

      await rateLimiter.resetLimits();

      expect(mockPlacesDelete).toHaveBeenCalledWith('default');
      expect(mockMyBusinessDelete).toHaveBeenCalledWith('default');
    });

    it('should reset only places limiter when specified', async () => {
      const mockPlacesDelete = vi.fn().mockResolvedValue(undefined);
      const mockMyBusinessDelete = vi.fn().mockResolvedValue(undefined);
      (rateLimiter as any).placesLimiter.delete = mockPlacesDelete;
      (rateLimiter as any).myBusinessLimiter.delete = mockMyBusinessDelete;

      await rateLimiter.resetLimits('places');

      expect(mockPlacesDelete).toHaveBeenCalledWith('default');
      expect(mockMyBusinessDelete).not.toHaveBeenCalled();
    });

    it('should reset only mybusiness limiter when specified', async () => {
      const mockPlacesDelete = vi.fn().mockResolvedValue(undefined);
      const mockMyBusinessDelete = vi.fn().mockResolvedValue(undefined);
      (rateLimiter as any).placesLimiter.delete = mockPlacesDelete;
      (rateLimiter as any).myBusinessLimiter.delete = mockMyBusinessDelete;

      await rateLimiter.resetLimits('mybusiness');

      expect(mockPlacesDelete).not.toHaveBeenCalled();
      expect(mockMyBusinessDelete).toHaveBeenCalledWith('default');
    });
  });
});