import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios from 'axios';
import { GoogleApiClient, getGoogleApiClient, resetGoogleApiClient } from '../../../lib/google-api/client.js';
import { GoogleApiConfig } from '../../../lib/google-api/config.js';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock logger
vi.mock('../../../lib/logger.js', () => ({
  logger: {
    debug: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

describe('GoogleApiClient', () => {
  let mockConfig: GoogleApiConfig;
  let mockAxiosInstance: any;

  beforeEach(() => {
    mockConfig = {
      placesApiKey: 'test-places-key',
      myBusinessApiKey: 'test-mybusiness-key',
      baseUrl: 'https://maps.googleapis.com',
      myBusinessBaseUrl: 'https://mybusinessbusinessinformation.googleapis.com',
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
    };

    mockAxiosInstance = {
      get: vi.fn(),
      post: vi.fn(),
      interceptors: {
        request: {
          use: vi.fn(),
        },
        response: {
          use: vi.fn(),
        },
      },
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);
    resetGoogleApiClient();
  });

  afterEach(() => {
    vi.clearAllMocks();
    resetGoogleApiClient();
  });

  describe('constructor', () => {
    it('should create axios instances with correct configuration', () => {
      new GoogleApiClient(mockConfig);

      expect(mockedAxios.create).toHaveBeenCalledTimes(2);
      
      // Places API client
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://maps.googleapis.com',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // My Business API client
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://mybusinessbusinessinformation.googleapis.com',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-mybusiness-key',
        },
      });
    });

    it('should setup interceptors', () => {
      new GoogleApiClient(mockConfig);

      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalledTimes(2);
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalledTimes(2);
    });
  });

  describe('findPlace', () => {
    it('should make correct API call with default parameters', async () => {
      const mockResponse = {
        data: {
          candidates: [
            {
              place_id: 'test-place-id',
              name: 'Test Business',
              formatted_address: '123 Test St',
            },
          ],
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const client = new GoogleApiClient(mockConfig);
      const result = await client.findPlace('Test Business');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/maps/api/place/findplacefromtext/json', {
        params: {
          input: 'Test Business',
          inputtype: 'textquery',
          fields: 'place_id,name,formatted_address',
          key: 'test-places-key',
        },
      });

      expect(result).toEqual(mockResponse.data);
    });

    it('should handle custom parameters', async () => {
      const mockResponse = { data: { candidates: [] } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const client = new GoogleApiClient(mockConfig);
      await client.findPlace('Test Business', 'phonenumber', ['place_id', 'name']);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/maps/api/place/findplacefromtext/json', {
        params: {
          input: 'Test Business',
          inputtype: 'phonenumber',
          fields: 'place_id,name',
          key: 'test-places-key',
        },
      });
    });
  });

  describe('getPlaceDetails', () => {
    it('should make correct API call', async () => {
      const mockResponse = {
        data: {
          result: {
            place_id: 'test-place-id',
            name: 'Test Business',
            rating: 4.5,
            reviews: [],
          },
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const client = new GoogleApiClient(mockConfig);
      const result = await client.getPlaceDetails('test-place-id');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/maps/api/place/details/json', {
        params: {
          place_id: 'test-place-id',
          fields: 'place_id,name,formatted_address,rating,user_ratings_total,reviews',
          key: 'test-places-key',
        },
      });

      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('searchPlaces', () => {
    it('should make correct API call with query only', async () => {
      const mockResponse = { data: { results: [] } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const client = new GoogleApiClient(mockConfig);
      await client.searchPlaces('restaurants');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/maps/api/place/textsearch/json', {
        params: {
          query: 'restaurants',
          key: 'test-places-key',
        },
      });
    });

    it('should include location and radius when provided', async () => {
      const mockResponse = { data: { results: [] } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const client = new GoogleApiClient(mockConfig);
      await client.searchPlaces('restaurants', '40.7128,-74.0060', 1000);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/maps/api/place/textsearch/json', {
        params: {
          query: 'restaurants',
          location: '40.7128,-74.0060',
          radius: 1000,
          key: 'test-places-key',
        },
      });
    });
  });

  describe('healthCheck', () => {
    it('should return true for both APIs when they respond successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: {} });

      const client = new GoogleApiClient(mockConfig);
      const result = await client.healthCheck();

      expect(result).toEqual({
        placesApi: true,
        myBusinessApi: true,
      });
    });

    it('should return false for APIs that fail', async () => {
      mockAxiosInstance.get
        .mockResolvedValueOnce({ data: {} }) // Places API succeeds
        .mockRejectedValueOnce(new Error('API Error')); // My Business API fails

      const client = new GoogleApiClient(mockConfig);
      const result = await client.healthCheck();

      expect(result).toEqual({
        placesApi: true,
        myBusinessApi: false,
      });
    });
  });

  describe('error handling', () => {
    it('should handle API response errors', async () => {
      const errorResponse = {
        response: {
          status: 400,
          data: {
            status: 'INVALID_REQUEST',
            error_message: 'Invalid request',
          },
        },
      };

      mockAxiosInstance.get.mockRejectedValue(errorResponse);

      const client = new GoogleApiClient(mockConfig);

      await expect(client.findPlace('test')).rejects.toThrow();
    });

    it('should handle network errors', async () => {
      const networkError = {
        request: {},
        message: 'Network Error',
      };

      mockAxiosInstance.get.mockRejectedValue(networkError);

      const client = new GoogleApiClient(mockConfig);

      await expect(client.findPlace('test')).rejects.toThrow();
    });
  });
});

describe('getGoogleApiClient singleton', () => {
  beforeEach(() => {
    resetGoogleApiClient();
  });

  afterEach(() => {
    resetGoogleApiClient();
  });

  it('should create and return singleton instance', () => {
    const config: GoogleApiConfig = {
      placesApiKey: 'test-places-key',
      myBusinessApiKey: 'test-mybusiness-key',
      baseUrl: 'https://maps.googleapis.com',
      myBusinessBaseUrl: 'https://mybusinessbusinessinformation.googleapis.com',
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
    };

    const client1 = getGoogleApiClient(config);
    const client2 = getGoogleApiClient();

    expect(client1).toBe(client2);
  });

  it('should throw error when called without config and not initialized', () => {
    expect(() => getGoogleApiClient()).toThrow(
      'Google API client not initialized. Call with config first.'
    );
  });
});