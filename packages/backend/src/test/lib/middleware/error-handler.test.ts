import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import {
  AppError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  GoogleApiError,
  DatabaseError,
  errorHandler,
  asyncHandler,
  notFoundHandler,
} from '../../../lib/middleware/error-handler.js';

// Mock logger
vi.mock('../../../lib/logger.js', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
  },
}));

describe('Error Handler Middleware', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      url: '/test',
      method: 'GET',
      ip: '127.0.0.1',
      get: vi.fn(),
      body: {},
      query: {},
      params: {},
    };

    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };

    mockNext = vi.fn();
  });

  describe('AppError class', () => {
    it('should create AppError with default values', () => {
      const error = new AppError('Test error');
      
      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(500);
      expect(error.code).toBe('INTERNAL_SERVER_ERROR');
      expect(error.isOperational).toBe(true);
    });

    it('should create AppError with custom values', () => {
      const error = new AppError('Custom error', 400, 'CUSTOM_ERROR', false, { detail: 'test' });
      
      expect(error.message).toBe('Custom error');
      expect(error.statusCode).toBe(400);
      expect(error.code).toBe('CUSTOM_ERROR');
      expect(error.isOperational).toBe(false);
      expect(error.details).toEqual({ detail: 'test' });
    });
  });

  describe('Specific error classes', () => {
    it('should create ValidationError correctly', () => {
      const error = new ValidationError('Invalid input', { field: 'email' });
      
      expect(error.statusCode).toBe(400);
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.details).toEqual({ field: 'email' });
    });

    it('should create NotFoundError correctly', () => {
      const error = new NotFoundError('Resource not found');
      
      expect(error.statusCode).toBe(404);
      expect(error.code).toBe('NOT_FOUND');
    });

    it('should create UnauthorizedError correctly', () => {
      const error = new UnauthorizedError();
      
      expect(error.statusCode).toBe(401);
      expect(error.code).toBe('UNAUTHORIZED');
    });

    it('should create GoogleApiError correctly', () => {
      const error = new GoogleApiError('API limit exceeded');
      
      expect(error.statusCode).toBe(502);
      expect(error.code).toBe('EXTERNAL_SERVICE_ERROR');
      expect(error.message).toBe('Google API service error: API limit exceeded');
    });

    it('should create DatabaseError correctly', () => {
      const error = new DatabaseError('Connection failed');
      
      expect(error.statusCode).toBe(500);
      expect(error.code).toBe('DATABASE_ERROR');
      expect(error.message).toBe('Database error: Connection failed');
    });
  });

  describe('errorHandler middleware', () => {
    it('should handle AppError correctly', () => {
      const error = new ValidationError('Invalid data', { field: 'name' });
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid data',
          details: { field: 'name' },
          timestamp: expect.any(String),
        },
      });
    });

    it('should convert generic Error to AppError', () => {
      const error = new Error('Generic error');
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: expect.any(String),
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle ValidationError from name', () => {
      const error = new Error('Validation failed');
      error.name = 'ValidationError';
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle Google API errors', () => {
      const error = new Error('Google API error: Rate limit exceeded');
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(502);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: {
          code: 'EXTERNAL_SERVICE_ERROR',
          message: expect.stringContaining('Google API'),
          timestamp: expect.any(String),
        },
      });
    });

    it('should handle network errors', () => {
      const error = new Error('ECONNREFUSED connection failed');
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(502);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: {
          code: 'EXTERNAL_SERVICE_ERROR',
          message: 'Network service error: Connection failed',
          timestamp: expect.any(String),
        },
      });
    });

    it('should include stack trace in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';
      
      const error = new AppError('Test error');
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.json).toHaveBeenCalledWith({
        error: expect.objectContaining({
          stack: expect.any(String),
        }),
      });
      
      process.env.NODE_ENV = originalEnv;
    });

    it('should not include stack trace in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      
      const error = new AppError('Test error');
      
      errorHandler(error, mockReq as Request, mockRes as Response, mockNext);
      
      const callArgs = (mockRes.json as any).mock.calls[0][0];
      expect(callArgs.error.stack).toBeUndefined();
      
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('asyncHandler wrapper', () => {
    it('should handle successful async function', async () => {
      const asyncFn = vi.fn().mockResolvedValue('success');
      const wrappedFn = asyncHandler(asyncFn);
      
      await wrappedFn(mockReq, mockRes, mockNext);
      
      expect(asyncFn).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should catch and forward async errors', async () => {
      const error = new Error('Async error');
      const asyncFn = async () => {
        throw error;
      };
      const wrappedFn = asyncHandler(asyncFn);
      
      await wrappedFn(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle sync functions that throw', async () => {
      const error = new Error('Sync error');
      const syncFn = () => {
        throw error;
      };
      const wrappedFn = asyncHandler(syncFn);
      
      // The wrapped function should catch the error and call next
      try {
        await wrappedFn(mockReq, mockRes, mockNext);
      } catch (e) {
        // If the error is not caught by asyncHandler, it will be thrown here
        // This should not happen if asyncHandler is working correctly
      }
      
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('notFoundHandler', () => {
    it('should create NotFoundError for unmatched routes', () => {
      mockReq.originalUrl = '/api/nonexistent';
      
      notFoundHandler(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 404,
          code: 'NOT_FOUND',
          message: 'Route /api/nonexistent not found',
        })
      );
    });
  });
});