import { describe, it, expect } from 'vitest';
import { 
  getPrismaClient, 
  handleDatabaseError,
  executeTransaction 
} from '../../lib/database.js';

describe('Database Utilities', () => {
  describe('getPrismaClient', () => {
    it('should return a Prisma client instance', () => {
      const client = getPrismaClient();
      expect(client).toBeDefined();
      expect(typeof client.$connect).toBe('function');
      expect(typeof client.$disconnect).toBe('function');
    });

    it('should return the same instance on multiple calls', () => {
      const client1 = getPrismaClient();
      const client2 = getPrismaClient();
      expect(client1).toBe(client2);
    });
  });

  describe('handleDatabaseError', () => {
    it('should handle Error objects correctly', () => {
      const error = new Error('Test error message');
      const result = handleDatabaseError(error, 'test operation');

      expect(result.success).toBe(false);
      expect(result.error).toBe('test operation failed: Test error message');
    });

    it('should handle unknown errors correctly', () => {
      const error = 'string error';
      const result = handleDatabaseError(error, 'test operation');

      expect(result.success).toBe(false);
      expect(result.error).toBe('test operation failed: Unknown error');
    });

    it('should handle null/undefined errors correctly', () => {
      const result = handleDatabaseError(null, 'test operation');

      expect(result.success).toBe(false);
      expect(result.error).toBe('test operation failed: Unknown error');
    });
  });

  describe('executeTransaction', () => {
    it('should handle transaction callback correctly', async () => {
      const mockCallback = async () => {
        return { success: true, data: 'test result' };
      };

      // This will fail without a real database connection, but we can test the structure
      const result = await executeTransaction(mockCallback);
      
      // The result structure should be correct even if it fails
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('error');
    });
  });
});