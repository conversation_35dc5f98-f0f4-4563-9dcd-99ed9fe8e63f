import { describe, it, expect, vi, beforeEach } from 'vitest';
import request from 'supertest';
import express from 'express';
import errorLogRoutes from '../../routes/error-log.routes.js';
import { errorHandler } from '../../lib/middleware/error-handler.js';

// Mock logger
vi.mock('../../lib/logger.js', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
  },
}));

describe('Error Log Routes', () => {
  let app: express.Application;
  
  const validErrorData = {
    message: 'Network error occurred',
    type: 'network',
    stack: 'Error: Network error\n    at fetch (/widget.js:123:45)',
    context: {
      widgetId: 'test-widget-123',
      apiUrl: 'https://api.example.com',
      retryCount: 2,
      hasStaleData: false,
    },
    timestamp: '2023-01-01T12:00:00.000Z',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    url: 'https://example.com/page-with-widget',
  };

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/api/widget', errorLogRoutes);
    app.use(errorHandler);
    vi.clearAllMocks();
  });

  describe('POST /api/widget/error-log', () => {

    it('should log valid error data successfully', async () => {
      const response = await request(app)
        .post('/api/widget/error-log')
        .send(validErrorData)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Error logged successfully',
        timestamp: expect.any(String),
      });
    });

    it('should reject invalid error data', async () => {
      const invalidData = {
        message: 'Error message',
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/widget/error-log')
        .send(invalidData)
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should validate required fields', async () => {
      const testCases = [
        { ...validErrorData, message: undefined },
        { ...validErrorData, type: undefined },
        { ...validErrorData, context: undefined },
        { ...validErrorData, timestamp: undefined },
        { ...validErrorData, userAgent: undefined },
        { ...validErrorData, url: undefined },
      ];

      for (const testCase of testCases) {
        await request(app)
          .post('/api/widget/error-log')
          .send(testCase)
          .expect(400);
      }
    });

    it('should validate context object structure', async () => {
      const invalidContextData = {
        ...validErrorData,
        context: {
          // Missing widgetId
          apiUrl: 'https://api.example.com',
        },
      };

      await request(app)
        .post('/api/widget/error-log')
        .send(invalidContextData)
        .expect(400);
    });

    it('should handle optional fields correctly', async () => {
      const dataWithOptionalFields = {
        ...validErrorData,
        stack: undefined, // Optional field
        context: {
          ...validErrorData.context,
          retryCount: undefined, // Optional field
          hasStaleData: undefined, // Optional field
        },
      };

      await request(app)
        .post('/api/widget/error-log')
        .send(dataWithOptionalFields)
        .expect(200);
    });

    it('should handle large error payloads', async () => {
      const largeErrorData = {
        ...validErrorData,
        message: 'A'.repeat(10000), // Large message
        stack: 'B'.repeat(50000), // Large stack trace
      };

      await request(app)
        .post('/api/widget/error-log')
        .send(largeErrorData)
        .expect(200);
    });

    it('should sanitize sensitive data in context', async () => {
      const dataWithSensitiveInfo = {
        ...validErrorData,
        context: {
          ...validErrorData.context,
          apiKey: 'secret-api-key', // This should be ignored by schema
          password: 'user-password', // This should be ignored by schema
        },
      };

      const response = await request(app)
        .post('/api/widget/error-log')
        .send(dataWithSensitiveInfo)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/widget/error-stats', () => {
    it('should return error statistics', async () => {
      const response = await request(app)
        .get('/api/widget/error-stats')
        .expect(200);

      expect(response.body).toEqual({
        totalErrors: expect.any(Number),
        errorsByType: expect.any(Object),
        errorsByWidget: expect.any(Object),
        recentErrors: expect.any(Array),
        timestamp: expect.any(String),
      });
    });

    it('should return consistent data structure', async () => {
      const response = await request(app)
        .get('/api/widget/error-stats')
        .expect(200);

      expect(response.body).toHaveProperty('totalErrors');
      expect(response.body).toHaveProperty('errorsByType');
      expect(response.body).toHaveProperty('errorsByWidget');
      expect(response.body).toHaveProperty('recentErrors');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('Error handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/widget/error-log')
        .send('invalid json')
        .set('Content-Type', 'application/json');
      
      // Express JSON parsing error can return either 400 or 500
      expect([400, 500]).toContain(response.status);
    });

    it('should handle empty request body', async () => {
      await request(app)
        .post('/api/widget/error-log')
        .send({})
        .expect(400);
    });

    it('should handle missing content-type header', async () => {
      await request(app)
        .post('/api/widget/error-log')
        .send(validErrorData)
        .expect(200); // Express should still parse JSON
    });
  });

  describe('Security', () => {
    it('should not expose internal error details in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const response = await request(app)
        .post('/api/widget/error-log')
        .send({}) // Invalid data to trigger error
        .expect(400);

      expect(response.body.error).not.toHaveProperty('stack');
      
      process.env.NODE_ENV = originalEnv;
    });

    it('should rate limit error logging requests', async () => {
      // This would require implementing rate limiting middleware
      // For now, we'll just test that the endpoint exists and works
      await request(app)
        .post('/api/widget/error-log')
        .send(validErrorData)
        .expect(200);
    });

    it('should validate request size limits', async () => {
      // Test with extremely large payload
      const hugePayload = {
        ...validErrorData,
        message: 'A'.repeat(1000000), // 1MB message
      };

      // This should either succeed or fail gracefully
      const response = await request(app)
        .post('/api/widget/error-log')
        .send(hugePayload);

      expect([200, 413, 400, 500]).toContain(response.status);
    });
  });
});