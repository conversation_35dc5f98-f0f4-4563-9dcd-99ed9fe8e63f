import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import businessRoutes from './routes/business.routes.js';
import reviewsRoutes from './routes/reviews.routes.js';
import reviewsDirectRoutes from './routes/reviews-direct.routes.js';
import widgetConfigRoutes from './routes/widget-config.routes.js';
import embedCodeRoutes from './routes/embed-code.routes.js';
import widgetDataRoutes from './routes/widget-data.routes.js';
import errorLogRoutes from './routes/error-log.routes.js';
import { logger } from './lib/logger.js';
import { testSupabaseConnection } from './lib/supabase.js';
import {
  errorHandler,
  notFoundHandler,
  requestTimeout,
  handleUnhandledRejection,
  handleUncaughtException
} from './lib/middleware/error-handler.js';

// Load environment variables
dotenv.config();

// Set up global error handlers
handleUnhandledRejection();
handleUncaughtException();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(requestTimeout(30000)); // 30 second timeout

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API routes
app.get('/api', (req, res) => {
  res.json({ message: 'Google Reviews Widget Generator API' });
});

// Business routes
app.use('/api/business', businessRoutes);

// Reviews routes
app.use('/api/reviews', reviewsRoutes);

// Direct reviews routes (bypasses database)
app.use('/api/reviews-direct', reviewsDirectRoutes);

// Widget configuration routes
app.use('/api/widget-configs', widgetConfigRoutes);

// Embed code routes
app.use('/api/embed-code', embedCodeRoutes);

// Widget data routes
app.use('/api/widget', widgetDataRoutes);

// Error logging routes
app.use('/api/widget', errorLogRoutes);

// 404 handler for unmatched routes
app.use(notFoundHandler);

// Global error handling middleware
app.use(errorHandler);

app.listen(PORT, async () => {
  logger.info(`Backend server running on port ${PORT}`);

  // Test database connection
  const dbTest = await testSupabaseConnection();
  if (dbTest.success) {
    logger.info('✅ Database connection successful');
  } else {
    logger.error('❌ Database connection failed:', dbTest.error);
  }
});
