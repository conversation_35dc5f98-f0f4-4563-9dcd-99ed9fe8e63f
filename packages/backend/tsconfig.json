{"extends": "../../tsconfig.json", "compilerOptions": {"composite": true, "target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "types": ["node", "vitest/globals"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}