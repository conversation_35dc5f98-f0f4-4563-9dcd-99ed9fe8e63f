{"extends": "../../tsconfig.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "types": ["vitest/globals", "@testing-library/jest-dom"]}, "include": ["src"], "exclude": ["src/test/**/*", "src/**/*.test.*", "src/**/*.spec.*"], "references": [{"path": "./tsconfig.node.json"}]}