// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Add global error handling
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents Cypress from failing the test
  // on uncaught exceptions that we expect (like network errors in development)
  if (err.message.includes('Network Error') || err.message.includes('fetch')) {
    return false;
  }
  return true;
});

// Add custom commands for accessibility testing
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to check accessibility violations
       * @example cy.checkA11y()
       */
      checkA11y(): Chainable<void>;
      
      /**
       * Custom command to wait for widget to load
       * @example cy.waitForWidget()
       */
      waitForWidget(): Chainable<void>;
      
      /**
       * Custom command to select business in search
       * @example cy.selectBusiness('Test Business')
       */
      selectBusiness(businessName: string): Chainable<void>;
      
      /**
       * Custom command to configure widget template
       * @example cy.configureTemplate('carousel')
       */
      configureTemplate(template: string): Chainable<void>;
    }
  }
}