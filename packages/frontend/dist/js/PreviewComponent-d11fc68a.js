import{j as e,u as i}from"./index-ad9d0ad0.js";import{R as t,r as o}from"./chunk-0fa44877.js";import{d as n}from"./chunk-69735360.js";const r=[{id:"1",authorName:"<PERSON>",authorPhotoUrl:"https://via.placeholder.com/40x40/4285f4/ffffff?text=SJ",rating:5,text:"Excellent service and friendly staff. The team went above and beyond to ensure our satisfaction. Highly recommend to anyone looking for quality service!",publishedDate:"2 days ago",isVerified:!0},{id:"2",authorName:"<PERSON>",authorPhotoUrl:"https://via.placeholder.com/40x40/34a853/ffffff?text=MC",rating:4,text:"Great experience overall. Professional service and reasonable pricing. Will definitely come back for future needs.",publishedDate:"1 week ago",isVerified:!0},{id:"3",authorName:"<PERSON>",authorPhotoUrl:"https://via.placeholder.com/40x40/ea4335/ffffff?text=ED",rating:5,text:"Outstanding quality and attention to detail. The results exceeded our expectations.",publishedDate:"2 weeks ago",isVerified:!1},{id:"4",authorName:"James <PERSON>",authorPhotoUrl:"https://via.placeholder.com/40x40/fbbc04/ffffff?text=JW",rating:5,text:"Fantastic service from start to finish. Very professional and efficient.",publishedDate:"3 weeks ago",isVerified:!0},{id:"5",authorName:"Lisa Rodriguez",authorPhotoUrl:"https://via.placeholder.com/40x40/9c27b0/ffffff?text=LR",rating:4,text:"Good value for money and reliable service. Would recommend to others.",publishedDate:"1 month ago",isVerified:!0}],s=n.div`
  width: ${e=>"mobile"===e.$viewport?"100%":e.$config.styling.dimensions.width};
  max-width: ${e=>"mobile"===e.$viewport?"320px":"100%"};
  height: ${e=>e.$config.styling.dimensions.height};
  background: ${e=>e.$config.styling.colors.background};
  border-radius: ${e=>e.$config.styling.dimensions.borderRadius};
  padding: ${e=>e.$config.styling.spacing.padding};
  margin: ${e=>e.$config.styling.spacing.margin};
  font-family: ${e=>e.$config.styling.fonts.family};
  font-size: ${e=>"mobile"===e.$viewport?"14px":e.$config.styling.fonts.size};
  color: ${e=>e.$config.styling.colors.text};
  border: 1px solid ${e=>e.$config.styling.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
`,l=n.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`,a=n.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${e=>e.$config.styling.spacing.gap};
  padding-bottom: 8px;
  border-bottom: 1px solid ${e=>e.$config.styling.colors.border};
`,d=n.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: ${e=>e.$config.styling.colors.text};
`,c=n.div`
  display: flex;
  gap: 6px;
  align-items: center;
`,g=n.button`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: ${e=>e.$active?e.$config.styling.colors.primary:e.$config.styling.colors.border};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${e=>e.$config.styling.colors.primary};
  }
`,x=n.div`
  flex: 1;
  padding: 12px;
  border: 1px solid ${e=>e.$config.styling.colors.border};
  border-radius: 8px;
  background: ${e=>e.$config.styling.colors.background};
`,f=n.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
`,p=n.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
`,h=n.div`
  flex: 1;
`,u=n.div`
  font-weight: 600;
  font-size: 14px;
  color: ${e=>e.$config.styling.colors.text};
`,m=n.div`
  font-size: 12px;
  color: ${e=>e.$config.styling.colors.text}80;
`,b=n.div`
  color: ${e=>e.$config.styling.colors.secondary};
  font-size: 14px;
`,$=n.p`
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: ${e=>e.$config.styling.colors.text};
`,v=n.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: ${e=>e.$config.styling.colors.background};
  border-radius: 24px;
  border: 1px solid ${e=>e.$config.styling.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: fit-content;
`,y=n.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  color: ${e=>e.$config.styling.colors.secondary};
`,w=n.span`
  color: ${e=>e.$config.styling.colors.text};
  font-size: 14px;
`,j=n.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`,k=n.div`
  text-align: center;
  margin-bottom: ${e=>e.$config.styling.spacing.gap};
  padding-bottom: 12px;
  border-bottom: 1px solid ${e=>e.$config.styling.colors.border};
`,z=n.div`
  background: ${e=>e.$config.styling.colors.border}40;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: ${e=>e.$config.styling.spacing.gap};
  font-size: 12px;
  color: ${e=>e.$config.styling.colors.text};
  text-align: center;
`,S=n.div`
  display: grid;
  grid-template-columns: ${e=>"mobile"===e.$viewport?"1fr":"1fr 1fr"};
  gap: 8px;
  flex: 1;
`,D=n.div`
  padding: 8px;
  border: 1px solid ${e=>e.$config.styling.colors.border};
  border-radius: 6px;
  background: ${e=>e.$config.styling.colors.background};
`,C=e=>"★".repeat(e)+"☆".repeat(5-e);function B({config:i,viewport:o}){const[n,B]=t.useState(0),R=[...r.filter(e=>e.rating>=i.settings.minRating).slice(0,i.settings.maxReviews)].sort((e,t)=>{switch(i.settings.sortBy){case"rating_high":return t.rating-e.rating;case"rating_low":return e.rating-t.rating;case"oldest":return new Date(e.publishedDate).getTime()-new Date(t.publishedDate).getTime();default:return new Date(t.publishedDate).getTime()-new Date(e.publishedDate).getTime()}}),V=R[n]||R[0],N=R.reduce((e,i)=>e+i.rating,0)/R.length;return e.jsx(s,{$config:i,$viewport:o,children:(()=>{var t,r;switch(i.template){case"carousel":return e.jsxs(l,{children:[e.jsxs(a,{$config:i,children:[e.jsx(d,{$config:i,children:(null==(t=i.business)?void 0:t.name)||"Customer Reviews"}),e.jsx(c,{children:R.map((t,o)=>e.jsx(g,{$active:o===n,$config:i,onClick:()=>B(o)},o))})]}),V&&e.jsxs(x,{$config:i,children:[e.jsxs(f,{children:[i.settings.showPhotos&&e.jsx(p,{src:V.authorPhotoUrl,alt:V.authorName}),e.jsxs(h,{children:[e.jsx(u,{$config:i,children:V.authorName}),i.settings.showDates&&e.jsx(m,{$config:i,children:V.publishedDate})]}),e.jsx(b,{$config:i,children:C(V.rating)})]}),e.jsx($,{$config:i,children:V.text})]})]});case"badge":return e.jsxs(v,{$config:i,children:[e.jsxs(y,{$config:i,children:[e.jsx("span",{children:"★"}),e.jsx("span",{children:N.toFixed(1)})]}),e.jsxs(w,{$config:i,children:["Based on ",R.length," reviews"]})]});case"grid":return e.jsxs(j,{children:[e.jsxs(k,{$config:i,children:[e.jsxs("div",{style:{fontSize:"16px",fontWeight:"600",marginBottom:"4px"},children:["★ ",N.toFixed(1)," (",R.length," reviews)"]}),e.jsx("div",{style:{fontSize:"14px",color:i.styling.colors.text+"80"},children:(null==(r=i.business)?void 0:r.name)||"Business Reviews"})]}),e.jsx(z,{$config:i,children:"AI Summary: Customers consistently praise the excellent service and professional staff"}),e.jsx(S,{$viewport:o,children:R.slice(0,"mobile"===o?2:4).map(t=>e.jsxs(D,{$config:i,children:[e.jsxs(f,{style:{marginBottom:"6px"},children:[i.settings.showPhotos&&e.jsx(p,{src:t.authorPhotoUrl,alt:t.authorName,style:{width:"24px",height:"24px"}}),e.jsxs(h,{children:[e.jsx(u,{$config:i,style:{fontSize:"12px"},children:t.authorName}),i.settings.showDates&&e.jsx(m,{$config:i,style:{fontSize:"10px"},children:t.publishedDate})]}),e.jsx(b,{$config:i,style:{fontSize:"12px"},children:C(t.rating)})]}),e.jsx($,{$config:i,style:{fontSize:"11px"},children:t.text.length>80?t.text.substring(0,80)+"...":t.text})]},t.id))})]});case"simple-carousel":return e.jsx("div",{style:{textAlign:"center",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:V&&e.jsxs(e.Fragment,{children:[e.jsx(u,{$config:i,style:{marginBottom:"8px",fontSize:"16px"},children:V.authorName}),e.jsx(b,{$config:i,style:{marginBottom:"12px",fontSize:"18px"},children:C(V.rating)}),e.jsxs($,{$config:i,style:{fontStyle:"italic",marginBottom:"16px"},children:['"',V.text,'"']}),e.jsx(c,{style:{justifyContent:"center"},children:R.map((t,o)=>e.jsx(g,{$active:o===n,$config:i,onClick:()=>B(o)},o))})]})});case"slider":return e.jsxs("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[e.jsx("div",{style:{textAlign:"center",marginBottom:"16px",fontSize:"16px",fontWeight:"600"},children:"Recent Reviews"}),e.jsx("div",{style:{display:"flex",gap:"12px",overflowX:"auto",paddingBottom:"8px",flex:1},children:R.map(t=>e.jsxs("div",{style:{minWidth:"mobile"===o?"200px":"150px",padding:"12px",border:`1px solid ${i.styling.colors.border}`,borderRadius:"8px",background:i.styling.colors.background},children:[e.jsx(u,{$config:i,style:{marginBottom:"4px",fontSize:"12px"},children:t.authorName}),e.jsx(b,{$config:i,style:{marginBottom:"6px",fontSize:"12px"},children:C(t.rating)}),e.jsxs($,{$config:i,style:{fontSize:"10px"},children:[t.text.substring(0,60),"..."]})]},t.id))})]});case"floating-badge":return e.jsxs("div",{style:{position:"relative",width:"100%",height:"100%",background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",borderRadius:i.styling.dimensions.borderRadius,overflow:"hidden"},children:[e.jsx("div",{style:{position:"absolute",top:"12px",left:"12px",fontSize:"12px",color:"#666",background:"rgba(255,255,255,0.9)",padding:"4px 8px",borderRadius:"4px"},children:"Your Website Content"}),e.jsxs("div",{style:{position:"absolute",bottom:"16px",right:"16px",background:i.styling.colors.background,borderRadius:"50%",width:"60px",height:"60px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",cursor:"pointer",transition:"transform 0.2s ease"},children:[e.jsxs("div",{style:{color:i.styling.colors.secondary,fontWeight:"600",fontSize:"14px"},children:["★ ",N.toFixed(1)]}),e.jsxs("div",{style:{color:i.styling.colors.text,fontSize:"10px"},children:[R.length," reviews"]})]})]});default:return e.jsx("div",{children:"Template not found"})}})()})}const R={desktop:{width:1200,height:800},tablet:{width:768,height:1024},mobile:{width:375,height:667}},V=n.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
`,N=n.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 12px;
`,P=n.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0;
`,T=n.div`
  display: flex;
  gap: 8px;
  align-items: center;
`,M=n.button`
  padding: 8px 12px;
  border: 1px solid ${e=>e.$active?"#4285f4":"#e0e0e0"};
  background: ${e=>e.$active?"#4285f4":"#ffffff"};
  color: ${e=>e.$active?"#ffffff":"#666666"};
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    border-color: #4285f4;
    background: ${e=>e.$active?"#3367d6":"#f8f9ff"};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  }
`,W=n.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  overflow: auto;
  
  /* Create a device frame effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    ${e=>"mobile"===e.$currentViewport&&"\n      background: linear-gradient(to bottom, \n        #333 0%, #333 20px, \n        transparent 20px, transparent calc(100% - 20px),\n        #333 calc(100% - 20px), #333 100%);\n      border-radius: 20px;\n    "}
  }
`,U=n.div`
  width: ${e=>Math.min(e.$width,800)}px;
  max-width: 100%;
  min-height: ${e=>Math.min(.6*e.$height,500)}px;
  background: #ffffff;
  border-radius: ${e=>"mobile"===e.$viewport?"20px":"8px"};
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  
  ${e=>"mobile"===e.$viewport&&"\n    border: 8px solid #333333;\n    &::before {\n      content: '';\n      position: absolute;\n      top: -4px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n      z-index: 1;\n    }\n  "}
  
  ${e=>"tablet"===e.$viewport&&"\n    border: 4px solid #666666;\n    border-radius: 12px;\n  "}
`,H=n.div`
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
`,F=n.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666666;
  font-size: 14px;
`,E=n.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #dc3545;
  font-size: 14px;
  text-align: center;
  
  &::before {
    content: '⚠️';
    font-size: 24px;
    margin-bottom: 8px;
  }
`,J=()=>e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7l-2 3v1h8v-1l-2-3h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H3V4h18v10z"})}),A=()=>e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19 1H5c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 18H5V5h14v14z"})}),L=()=>e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 18H7V5h10v14z"})});function q({className:t,showControls:n=!0,defaultViewport:r="desktop"}){const{config:s}=i(),[l,a]=o.useState(r),[d,c]=o.useState(!1),[g,x]=o.useState(null);o.useEffect(()=>{c(!0),x(null);const e=setTimeout(()=>{c(!1)},300);return()=>clearTimeout(e)},[s]);const f=e=>{a(e)},p=R[l];return e.jsxs(V,{className:t,children:[n&&e.jsxs(N,{children:[e.jsx(P,{children:"Live Preview"}),e.jsxs(T,{children:[e.jsxs(M,{$active:"desktop"===l,onClick:()=>f("desktop"),title:"Desktop view",children:[e.jsx(J,{}),"Desktop"]}),e.jsxs(M,{$active:"tablet"===l,onClick:()=>f("tablet"),title:"Tablet view",children:[e.jsx(A,{}),"Tablet"]}),e.jsxs(M,{$active:"mobile"===l,onClick:()=>f("mobile"),title:"Mobile view",children:[e.jsx(L,{}),"Mobile"]})]})]}),e.jsx(W,{$width:p.width,$height:p.height,$currentViewport:l,children:e.jsx(U,{$width:p.width,$height:p.height,$viewport:l,children:e.jsx(H,{children:d?e.jsx(F,{children:"Updating preview..."}):g?e.jsx(E,{children:g}):s.business?e.jsx(B,{config:s,viewport:l}):e.jsx(F,{children:"Select a business to see the widget preview"})})})})]})}export{q as PreviewComponent};
//# sourceMappingURL=PreviewComponent-d11fc68a.js.map
