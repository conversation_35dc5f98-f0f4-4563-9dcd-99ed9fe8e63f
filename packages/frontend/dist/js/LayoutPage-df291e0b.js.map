{"version": 3, "mappings": "8KAKA,MAAMA,EAA6BC,EAAMC,KAAK,IAC5CC,EAAA,IAAAC,OAAO,4CAA4D,6IAAEC,KAAgBC,IAAA,CACnFC,QAASD,EAAON,+BAIdQ,EAAmBP,EAAMC,KAAK,IAClCC,EAAA,IAAAC,OAAO,kCAAwC,mIAAEC,KAAgBC,IAAA,CAC/DC,QAASD,EAAOE,qBAKdC,EAA4B,IAC/BC,OAAA,OAAIC,MAAO,CACVC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBC,OAAQ,QACRC,SAAU,OACVC,MAAO,QAEPC,SAAA,OAAC,OAAIP,MAAO,CACVQ,MAAO,OACPJ,OAAQ,OACRK,OAAQ,oBACRC,UAAW,oBACXC,aAAc,MACdC,UAAW,0BACXC,YAAa,SACN,0BAKPC,EAAgBC,EAAOC,GAAA;;;;EAMvBC,EAAQF,EAAOG,EAAA;;;;;;EAQfC,EAAcJ,EAAOK,CAAA;;;;;;;;;EAWrBC,EAAmBN,EAAOC,GAAA;;;;;;;;;;;EAa1BM,EAAkBP,EAAOC,GAAA;;;;;EAOzBO,EAAiBR,EAAOC,GAAA;;EAIvB,SAASQ,IACd,cACGV,EACC,CAAAP,SAAA,GAAAkB,IAACR,GAAMV,SAAa,oBACpBkB,IAACN,GAAYZ,SAGb,wIACCc,EACC,CAAAd,SAAA,CAAAkB,MAACH,EACC,CAAAf,eAACmB,EAAoB,CAAAC,WAAW,qBAC9BpB,SAACkB,MAAAG,WAAA,CAASC,SAAUJ,MAAC3B,GAAgB,GACnCS,eAAClB,EAA2B,gBAIjCkC,EACC,CAAAhB,eAACmB,EAAoB,CAAAC,WAAW,UAC9BpB,SAACkB,MAAAG,WAAA,CAASC,SAAUJ,MAAC3B,GAAgB,GACnCS,eAACV,EAAiB,eAOhC", "names": ["TemplateSelectionComponent", "React", "lazy", "__vitePreload", "import", "then", "module", "default", "PreviewComponent", "ComponentLoader", "jsxs", "style", "display", "alignItems", "justifyContent", "height", "fontSize", "color", "children", "width", "border", "borderTop", "borderRadius", "animation", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "styled", "div", "Title", "h2", "Description", "p", "ContentContainer", "TemplateSection", "PreviewSection", "LayoutPage", "jsx", "WidgetErrorBoundary", "widgetStep", "Suspense", "fallback"], "sources": ["../../src/pages/LayoutPage.tsx"], "sourcesContent": ["import React, { Suspense } from 'react';\nimport styled from 'styled-components';\nimport WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';\n\n// Lazy load heavy components\nconst TemplateSelectionComponent = React.lazy(() => \n  import('../components/TemplateSelection/TemplateSelectionComponent').then(module => ({ \n    default: module.TemplateSelectionComponent \n  }))\n);\n\nconst PreviewComponent = React.lazy(() => \n  import('../components/Preview/PreviewComponent').then(module => ({ \n    default: module.PreviewComponent \n  }))\n);\n\n// Component loader for heavy components\nconst ComponentLoader: React.FC = () => (\n  <div style={{\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: '300px',\n    fontSize: '14px',\n    color: '#666'\n  }}>\n    <div style={{\n      width: '20px',\n      height: '20px',\n      border: '2px solid #f3f3f3',\n      borderTop: '2px solid #3498db',\n      borderRadius: '50%',\n      animation: 'spin 1s linear infinite',\n      marginRight: '8px'\n    }}></div>\n    Loading component...\n  </div>\n);\n\nconst PageContainer = styled.div`\n  background-color: #f8f9fa;\n  min-height: 100vh;\n  padding: 32px 16px;\n`;\n\nconst Title = styled.h2`\n  font-size: 28px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 16px;\n  text-align: center;\n`;\n\nconst Description = styled.p`\n  font-size: 16px;\n  color: #666666;\n  margin-bottom: 32px;\n  line-height: 1.6;\n  text-align: center;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst ContentContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 32px;\n  max-width: 1400px;\n  margin: 0 auto;\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: 24px;\n  }\n`;\n\nconst TemplateSection = styled.div`\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n\nconst PreviewSection = styled.div`\n  min-height: 600px;\n`;\n\nexport function LayoutPage() {\n  return (\n    <PageContainer>\n      <Title>Choose Layout</Title>\n      <Description>\n        Select a template that best fits your website's design. \n        See how each template looks with your business reviews in real-time.\n      </Description>\n      <ContentContainer>\n        <TemplateSection>\n          <WidgetErrorBoundary widgetStep=\"template-selection\">\n            <Suspense fallback={<ComponentLoader />}>\n              <TemplateSelectionComponent />\n            </Suspense>\n          </WidgetErrorBoundary>\n        </TemplateSection>\n        <PreviewSection>\n          <WidgetErrorBoundary widgetStep=\"preview\">\n            <Suspense fallback={<ComponentLoader />}>\n              <PreviewComponent />\n            </Suspense>\n          </WidgetErrorBoundary>\n        </PreviewSection>\n      </ContentContainer>\n    </PageContainer>\n  );\n}"], "file": "js/LayoutPage-df291e0b.js"}