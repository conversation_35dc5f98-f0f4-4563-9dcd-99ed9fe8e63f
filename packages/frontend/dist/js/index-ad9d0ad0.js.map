{"version": 3, "mappings": "0/BASiBA,EAAEC,EAAiBC,EAAEC,OAAOC,IAAI,iBAAiBC,EAAEF,OAAOC,IAAI,kBAAkBE,EAAEC,OAAOC,UAAUC,eAAeC,EAAEV,EAAEW,mDAAmDC,kBAAkBC,EAAE,CAACC,KAAI,EAAGC,KAAI,EAAGC,QAAO,EAAGC,UAAS,GAChP,SAASC,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAKC,EAAE,KAAiF,IAAIH,UAAvE,IAAAD,IAAIG,EAAE,GAAGH,QAAG,IAASD,EAAEN,MAAMU,EAAE,GAAGJ,EAAEN,UAAc,IAAAM,EAAEL,MAAMU,EAAEL,EAAEL,KAAcK,EAAEd,EAAEoB,KAAKN,EAAEE,KAAKT,EAAEJ,eAAea,KAAKC,EAAED,GAAGF,EAAEE,IAAI,GAAGH,GAAGA,EAAEQ,aAAiB,IAAAL,KAAKF,EAAED,EAAEQ,kBAAe,IAASJ,EAAED,KAAKC,EAAED,GAAGF,EAAEE,IAAI,MAAM,CAACM,SAAS1B,EAAE2B,KAAKV,EAAEL,IAAIU,EAAET,IAAIU,EAAEK,MAAMP,EAAEQ,OAAOrB,EAAEsB,QAAQ,YAAkB3B,EAAa4B,EAAAC,IAAChB,EAAEe,EAAAE,KAAajB,ECPxWkB,EAAAC,QAAiBpC,uBCDfK,EAAIL,EAEYqC,EAAAC,WAAGjC,EAAEiC,WACJD,EAAAE,YAAGlC,EAAEkC,m2BCDpBC,EAA8B,CAClCC,SAAU,WACVC,QAAS,CACPC,OAAQ,CACNC,QAAS,UACTC,UAAW,UACXC,WAAY,UACZC,KAAM,UACNC,OAAQ,WAEVC,MAAO,CACLC,OAAQ,oBACRC,KAAM,OACNC,OAAQ,OAEVC,WAAY,CACVC,MAAO,QACPC,OAAQ,QACRC,aAAc,OAEhBC,QAAS,CACPC,QAAS,OACTC,OAAQ,MACRC,IAAK,SAGTC,SAAU,CACRC,WAAY,EACZC,UAAW,EACXC,OAAQ,SACRC,YAAY,EACZC,WAAW,EACXC,aAAa,IAiBjB,SAASC,EAAcC,EAAoBC,GACzC,OAAQA,EAAO1C,MACb,IAAK,gBACI,UACFyC,EACHE,OAAQ,IACHF,EAAME,UACND,EAAOE,QACV9B,QAAS4B,EAAOE,QAAQ9B,QACpB,IAAK2B,EAAME,OAAO7B,WAAY4B,EAAOE,QAAQ9B,SAC7C2B,EAAME,OAAO7B,QACjBmB,SAAUS,EAAOE,QAAQX,SACrB,IAAKQ,EAAME,OAAOV,YAAaS,EAAOE,QAAQX,UAC9CQ,EAAME,OAAOV,WAGvB,IAAK,eACI,UACFQ,EACHE,OAAQ,IAAK/B,GACbiC,YAAa,GAEjB,IAAK,WACI,UACFJ,EACHI,YAAaH,EAAOE,SAExB,QACS,OAAAH,EAEb,CAGA,MAAMK,EAAgBC,qBAA6C,GAOnD,SAAAC,GAAeC,SAAEA,IAC/B,MAAOR,EAAOS,GAAYC,aAAWX,EAAe,CAClDG,OAAQ,IAAK/B,GACbiC,YAAa,IAeTO,EAA2B,CAC/BT,OAAQF,EAAME,OACdU,aAdoBC,IACpBJ,EAAS,CAAElD,KAAM,gBAAiB4C,QAASU,KAc3CC,YAXkB,KACTL,EAAA,CAAElD,KAAM,kBAWjB6C,YAAaJ,EAAMI,YACnBW,eATsBC,IACtBP,EAAS,CAAElD,KAAM,WAAY4C,QAASa,MAWxC,SACGpD,IAAAyC,EAAcY,SAAd,CAAuBN,QACrBH,YAGP,CAGO,SAASU,IACR,MAAAC,EAAUC,aAAWf,GAC3B,QAAgB,IAAZc,EACI,UAAIE,MAAM,kDAEX,OAAAF,CACT,CChIA,MAAMG,EAAQ,CACZ,CAAEC,GAAI,EAAGC,KAAM,IAAKC,MAAO,SAAUC,YAAa,sBAClD,CAAEH,GAAI,EAAGC,KAAM,UAAWC,MAAO,SAAUC,YAAa,mBACxD,CAAEH,GAAI,EAAGC,KAAM,UAAWC,MAAO,SAAUC,YAAa,oBACxD,CAAEH,GAAI,EAAGC,KAAM,WAAYC,MAAO,UAAWC,YAAa,mBAC1D,CAAEH,GAAI,EAAGC,KAAM,SAAUC,MAAO,QAASC,YAAa,wBACtD,CAAEH,GAAI,EAAGC,KAAM,YAAaC,MAAO,WAAYC,YAAa,wBAGxDC,EAAsBC,EAAOC,GAAA;;;;;;EAQ7BC,EAAYF,EAAOG,GAAA;;;;;;;;;;EAYnBC,EAAeJ,EAAOG,GAAA;;;;;;;;;;;;;;;aAefvE,GAASA,EAAMyE;;;;;;;;EAUtBC,EAAWN,EAAOG,GAAA;;;;;;;;;;;;;;;;;;wBAkBSvE,KAAM2E,SAAW,UAAY;wBAC7B3E,KAAM2E,SAAW,UAAY;;EAIxDC,EAAaR,EAAOG,GAAA;;;;;;;;;;;;sBAYKvE,GACvBA,EAAM6E,YAAoB,UAC1B7E,EAAM2E,SAAiB,UACpB;;WAGS3E,GACZA,EAAM6E,aAAe7E,EAAM2E,SAAiB,UACzC;;;;;;EASLG,EAAcV,EAAOG,GAAA;;;;;;;EASrBQ,EAAYX,EAAOG,GAAA;iBACCvE,KAAM2E,SAAW,MAAQ;;WAE/B3E,KAAM2E,SAAW,UAAY;;EAI3CK,EAAkBZ,EAAOG,GAAA;;;EAKxB,SAASU,IACd,MAAMC,EAAWC,IACXC,EAAWC,KACXzC,YAAEA,EAAAW,eAAaA,GAAmBG,IAalC4B,EANyB,MAC7B,MAAMC,EAAcH,EAASI,SACvBhC,EAAOM,EAAM2B,KAAUC,KAAE1B,OAASuB,GACjC,OAAA/B,EAAOA,EAAKO,GAAK,GAGP4B,GACblB,EAAYa,GAAcxB,EAAM8B,OAAS,GAAM,IAGnDxF,aAAC+D,EACC,CAAAnB,SAAA3C,OAACiE,EACC,CAAAtB,SAAA,OAACwB,GAAaC,aACbX,EAAM+B,IAAKrC,IACJ,MAAAmB,EAAWnB,EAAKO,KAAOuB,EACvBT,EAAcrB,EAAKO,GAAKuB,EAG5B,OAAAQ,EAAAzF,KAACqE,EAAA,CAECC,WACAE,cACAkB,QAAS,IA3BG,CAACvC,IACvBD,EAAeC,EAAKO,IACpBmB,EAAS1B,EAAKQ,OAyBWgC,CAAgBxC,GAE/BR,SAAA,CAAA5C,MAACwE,GAAWD,WAAoBE,cAC7B7B,WAAc,IAAMQ,EAAKO,GAAK,WAEhCe,EACC,CAAA9B,SAAA,CAAC5C,MAAA2E,EAAA,CAAUJ,WAAqB3B,SAAAQ,EAAKS,UACrC7D,IAAC4E,EAAiB,CAAAhC,SAAAQ,EAAKU,mBAVpBV,EAAKO,UAkBxB,CC/KA,MAAMkC,EAAkB7B,EAAO8B,MAAA;;;;;EAOzBC,EAAgB/B,EAAOG,GAAA;;;;;;;;;;;;;EAevB6B,EAAQhC,EAAOiC,EAAA;;;;;;;;;;EAYfC,EAAUlC,EAAOG,GAAA;;;;;;;;;EAWjBgC,EAAcnC,EAAOoC,MAAA;;;;;;;;;;;;;;;;;;EAoBrBC,EAAarC,EAAOoC,MAAA;;;;;;;;;;;;;;;;;;;;;;;;EA0BnB,SAASE,IACd,MAAMpD,YAAEA,EAAAZ,OAAaA,GAAWgB,IAe9BtD,aAAC6F,EACC,CAAAjD,SAAA3C,OAAC8F,EACC,CAAAnD,SAAA,GAAA5C,IAACgG,GAAMpD,SAA+B,2CACrCsD,EACC,CAAAtD,SAAA,CAAC5C,MAAAmG,EAAA,CAAYR,QAjBD,KACdY,OAAOC,QAAQ,yFAgBsB5D,SAEnC,UACC5C,MAAAqG,EAAA,CAAWV,QAdD,KAGjBc,MAAM,0DAWiC7D,SAEjC,8BAKV,CCvHA,MAAM8D,EAAkB1C,EAAOG,GAAA;;;;;EAOzBwC,EAAO3C,EAAO4C,IAAA;;;;;;;;;;;;EAcdC,EAAc7C,EAAOG,GAAA;;;;;;;;;;;EAapB,SAAS2C,IACd,cACGJ,EACC,CAAA9D,SAAA,CAAA8C,EAAA1F,IAACsG,EAAO,WACPK,EACC,CAAA/D,SAAA,CAAA8C,EAAA1F,IAAC6E,EAAe,IACf7E,MAAA6G,EAAA,CACCjE,SAAC8C,EAAA1F,IAAA+G,EAAA,CAAO,UAKlB,CCrCA,MAAMC,EAAiBhD,EAAOG,GAAA;;;;;;;;;;;;EAcxB8C,EAAYjD,EAAOG,GAAA;;;;EAMnB+C,GAAalD,EAAOmD,EAAA;;;;;EAOpBC,GAAepD,EAAOrF,CAAA;;;;;;EAQtB0I,GAAerD,EAAOG,GAAA;;;;;EAOtBmD,GAAStD,EAAOoC,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BDpC,EAAOuD,OAAA;;;;;;;;;;;;;;;;;;;;;;;EAyB5B,MAAMC,WAAsBC,YAC1B,WAAAC,CAAY9H,GACV+H,MAAM/H,GAsCoBgI,EAAAC,KAAA,qBAACC,EAAcC,KAGrC,IAESD,EAAME,QACRF,EAAMG,MACGF,EAAUG,gBACf,IAAIC,MAAOC,cACXC,UAAUC,UAChB/B,OAAOvB,SAASuD,WAOhBC,GAET,IAGMZ,EAAAC,KAAA,eAAe,KACrBtB,OAAOvB,SAASyD,WAGVb,EAAAC,KAAA,cAAc,KACpBA,KAAKa,SAAS,CACZC,UAAU,EACVb,MAAO,KACPC,UAAW,SAIPH,EAAAC,KAAA,eAAe,KACrBtB,OAAOvB,SAASuD,KAAO,MAxEvBV,KAAKzF,MAAQ,CACXuG,UAAU,EACVb,MAAO,KACPC,UAAW,KAEf,CAEA,+BAAOa,CAAyBd,GACvB,OACLa,UAAU,EACVb,QAEJ,CAEA,iBAAAe,CAAkBf,EAAcC,GAC9BF,KAAKa,SAAS,CACZZ,QACAC,cAUEF,KAAKjI,MAAMkJ,SACRjB,KAAAjI,MAAMkJ,QAAQhB,EAAOC,GAKrBF,KAAAkB,kBAAkBjB,EAAOC,EAElC,CAwCA,MAAAiB,GACM,OAAAnB,KAAKzF,MAAMuG,SAETd,KAAKjI,MAAMqJ,SACNpB,KAAKjI,MAAMqJ,gBAKjBjC,EACC,CAAApE,SAAA,GAAA5C,IAACiH,GAAUrE,SAAE,SACb5C,IAACkH,IAAWtE,SAAoB,2BAChC5C,IAACoH,IAAaxE,SAGd,iLAECyE,GACC,CAAAzE,SAAA,CAAA5C,MAACsH,IAAO4B,UAAU,UAAUvD,QAASkC,KAAKsB,aAAcvG,SAExD,uBACC0E,GAAO,CAAA4B,UAAU,YAAYvD,QAASkC,KAAKuB,YAAaxG,SAEzD,oBACC0E,GAAO,CAAA4B,UAAU,YAAYvD,QAASkC,KAAKwB,aAAczG,SAE1D,gBAGD,KAyBAiF,KAAKjI,MAAMgD,QACpB,EC7OF,MAAM0G,GAAaC,EAAMC,KAAK,UAAMC,OAAO,gIAAsBC,KAAKC,IAAA,CAAaC,QAASD,EAAOL,eAC7FO,GAAaN,EAAMC,KAAK,UAAMC,OAAO,gIAAsBC,KAAKC,IAAA,CAAaC,QAASD,EAAOE,eAC7FC,GAAaP,EAAMC,KAAK,UAAMC,OAAO,gIAAsBC,KAAKC,IAAA,CAAaC,QAASD,EAAOG,eAC7FC,GAAcR,EAAMC,KAAK,UAAMC,OAAO,kIAAuBC,KAAKC,IAAA,CAAaC,QAASD,EAAOI,gBAC/FC,GAAYT,EAAMC,KAAK,UAAMC,OAAO,uLAAqBC,KAAKC,IAAA,CAAaC,QAASD,EAAOK,cAC3FC,GAAeV,EAAMC,KAAK,UAAMC,OAAO,oIAAwBC,KAAKC,IAAA,CAAaC,QAASD,EAAOM,iBAGjGC,GAAuB,IAC1BjK,OAAA,OAAIkK,MAAO,CACVC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBhJ,OAAQ,QACRiJ,SAAU,OACVC,MAAO,QAEP5H,SAAA,OAAC,OAAIuH,MAAO,CACV9I,MAAO,OACPC,OAAQ,OACRP,OAAQ,oBACR0J,UAAW,oBACXlJ,aAAc,MACdmJ,UAAW,0BACXC,YAAa,UACN,mBAER,QAAO,CAAA/H,SAAA,sIASZ,SAASgI,KACP,SACG5K,IAAAwH,GAAA,CAAcsB,QAAS,CAAChB,EAAOC,OAQ9BnF,eAACD,EACC,CAAAC,SAAA5C,MAAC6K,GACCjI,WAAC5C,IAAA8K,EAAA,CACClI,gBAACmI,EAAA,CAAMnH,KAAK,IAAIoH,QAAShL,MAAC8G,MACxBlE,SAAA,OAACmI,EAAM,CAAAE,OAAK,EAACD,QACVhL,MAAAkL,WAAA,CAASjC,SAAUjJ,MAACkK,GAAW,IAC9BtH,SAAC5C,MAAAsJ,GAAA,CAAW,aAGfyB,EAAA,CAAMnH,KAAK,SAASoH,QAClBhL,MAAAkL,WAAA,CAASjC,SAAUjJ,MAACkK,GAAW,IAC9BtH,SAAC5C,MAAA6J,GAAA,CAAW,aAGfkB,EAAA,CAAMnH,KAAK,SAASoH,QAClBhL,MAAAkL,WAAA,CAASjC,SAAUjJ,MAACkK,GAAW,IAC9BtH,SAAC5C,MAAA8J,GAAA,CAAW,aAGfiB,EAAA,CAAMnH,KAAK,UAAUoH,QACnBhL,MAAAkL,WAAA,CAASjC,SAAUjJ,MAACkK,GAAW,IAC9BtH,SAAC5C,MAAA+J,GAAA,CAAY,aAGhBgB,EAAA,CAAMnH,KAAK,QAAQoH,QACjBhL,MAAAkL,WAAA,CAASjC,SAAUjJ,MAACkK,GAAW,IAC9BtH,SAAC5C,MAAAgK,GAAA,CAAU,aAGde,EAAA,CAAMnH,KAAK,WAAWoH,QACpBhL,MAAAkL,WAAA,CAASjC,SAAUjJ,MAACkK,GAAW,IAC9BtH,SAAC5C,MAAAiK,GAAA,CAAa,iBAShC,CCxFAkB,EAAS9K,WAAW+K,SAASC,eAAe,SAAUrC,aACnDO,EAAM+B,WAAN,CACC1I,SAAA5C,MAAC4K,IAAI", "names": ["f", "require$$0", "k", "Symbol", "for", "l", "m", "Object", "prototype", "hasOwnProperty", "n", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCurrentOwner", "p", "key", "ref", "__self", "__source", "q", "c", "a", "g", "b", "d", "e", "h", "call", "defaultProps", "$$typeof", "type", "props", "_owner", "current", "reactJsxRuntime_production_min", "jsx", "jsxs", "jsxRuntimeModule", "exports", "client", "createRoot", "hydrateRoot", "defaultConfig", "template", "styling", "colors", "primary", "secondary", "background", "text", "border", "fonts", "family", "size", "weight", "dimensions", "width", "height", "borderRadius", "spacing", "padding", "margin", "gap", "settings", "maxReviews", "minRating", "sortBy", "showPhotos", "showDates", "autoRefresh", "widgetReducer", "state", "action", "config", "payload", "currentStep", "WidgetContext", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "dispatch", "useReducer", "value", "updateConfig", "updates", "resetConfig", "setCurrentStep", "step", "Provider", "useWidget", "context", "useContext", "Error", "steps", "id", "path", "label", "description", "NavigationContainer", "styled", "nav", "StepsList", "div", "ProgressLine", "progress", "StepItem", "isActive", "StepCircle", "isCompleted", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "StepDescription", "StepNavigation", "navigate", "useNavigate", "location", "useLocation", "activeStep", "currentPath", "pathname", "find", "s", "getCurrentStepFromPath", "length", "map", "jsxRuntimeExports", "onClick", "handleStepClick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title", "h1", "Actions", "ResetButton", "button", "SaveButton", "Header", "window", "confirm", "alert", "LayoutContainer", "Main", "main", "ContentArea", "Layout", "Outlet", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h2", "ErrorMessage", "ErrorActions", "<PERSON><PERSON>", "details", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "super", "__publicField", "this", "error", "errorInfo", "message", "stack", "componentStack", "Date", "toISOString", "navigator", "userAgent", "href", "loggingError", "reload", "setState", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "onError", "logErrorToService", "render", "fallback", "className", "handleReload", "handleReset", "handleGoHome", "SourcePage", "React", "lazy", "import", "then", "module", "default", "LayoutPage", "Header<PERSON><PERSON>", "ReviewsPage", "StylePage", "SettingsPage", "<PERSON><PERSON><PERSON><PERSON>", "style", "display", "alignItems", "justifyContent", "fontSize", "color", "borderTop", "animation", "marginRight", "App", "Router", "Routes", "Route", "element", "index", "Suspense", "ReactDOM", "document", "getElementById", "StrictMode"], "sources": ["../../../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../../../node_modules/react/jsx-runtime.js", "../../../../node_modules/react-dom/client.js", "../../src/context/WidgetContext.tsx", "../../src/components/Layout/StepNavigation.tsx", "../../src/components/Layout/Header.tsx", "../../src/components/Layout/Layout.tsx", "../../src/components/ErrorBoundary/ErrorBoundary.tsx", "../../src/App.tsx", "../../src/main.tsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import React, { createContext, useContext, useReducer, ReactNode } from 'react';\nimport { WidgetConfig, WidgetContextType, TemplateType } from '../types/widget';\n\n// Default widget configuration\nconst defaultConfig: WidgetConfig = {\n  template: 'carousel',\n  styling: {\n    colors: {\n      primary: '#4285f4',\n      secondary: '#34a853',\n      background: '#ffffff',\n      text: '#333333',\n      border: '#e0e0e0',\n    },\n    fonts: {\n      family: 'Inter, sans-serif',\n      size: '14px',\n      weight: '400',\n    },\n    dimensions: {\n      width: '400px',\n      height: '300px',\n      borderRadius: '8px',\n    },\n    spacing: {\n      padding: '16px',\n      margin: '0px',\n      gap: '12px',\n    },\n  },\n  settings: {\n    maxReviews: 5,\n    minRating: 1,\n    sortBy: 'newest',\n    showPhotos: true,\n    showDates: true,\n    autoRefresh: true,\n  },\n};\n\n// Action types for reducer\ntype WidgetAction = \n  | { type: 'UPDATE_CONFIG'; payload: Partial<WidgetConfig> }\n  | { type: 'RESET_CONFIG' }\n  | { type: 'SET_STEP'; payload: number };\n\n// State interface\ninterface WidgetState {\n  config: WidgetConfig;\n  currentStep: number;\n}\n\n// Reducer function\nfunction widgetReducer(state: WidgetState, action: WidgetAction): WidgetState {\n  switch (action.type) {\n    case 'UPDATE_CONFIG':\n      return {\n        ...state,\n        config: {\n          ...state.config,\n          ...action.payload,\n          styling: action.payload.styling \n            ? { ...state.config.styling, ...action.payload.styling }\n            : state.config.styling,\n          settings: action.payload.settings\n            ? { ...state.config.settings, ...action.payload.settings }\n            : state.config.settings,\n        },\n      };\n    case 'RESET_CONFIG':\n      return {\n        ...state,\n        config: { ...defaultConfig },\n        currentStep: 0,\n      };\n    case 'SET_STEP':\n      return {\n        ...state,\n        currentStep: action.payload,\n      };\n    default:\n      return state;\n  }\n}\n\n// Create context\nconst WidgetContext = createContext<WidgetContextType | undefined>(undefined);\n\n// Provider component\ninterface WidgetProviderProps {\n  children: ReactNode;\n}\n\nexport function WidgetProvider({ children }: WidgetProviderProps) {\n  const [state, dispatch] = useReducer(widgetReducer, {\n    config: { ...defaultConfig },\n    currentStep: 0,\n  });\n\n  const updateConfig = (updates: Partial<WidgetConfig>) => {\n    dispatch({ type: 'UPDATE_CONFIG', payload: updates });\n  };\n\n  const resetConfig = () => {\n    dispatch({ type: 'RESET_CONFIG' });\n  };\n\n  const setCurrentStep = (step: number) => {\n    dispatch({ type: 'SET_STEP', payload: step });\n  };\n\n  const value: WidgetContextType = {\n    config: state.config,\n    updateConfig,\n    resetConfig,\n    currentStep: state.currentStep,\n    setCurrentStep,\n  };\n\n  return (\n    <WidgetContext.Provider value={value}>\n      {children}\n    </WidgetContext.Provider>\n  );\n}\n\n// Custom hook to use the context\nexport function useWidget() {\n  const context = useContext(WidgetContext);\n  if (context === undefined) {\n    throw new Error('useWidget must be used within a WidgetProvider');\n  }\n  return context;\n}", "import React from 'react';\nimport styled from 'styled-components';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useWidget } from '../../context/WidgetContext';\n\nconst steps = [\n  { id: 0, path: '/', label: 'Source', description: 'Find your business' },\n  { id: 1, path: '/layout', label: 'Layout', description: 'Choose template' },\n  { id: 2, path: '/header', label: 'Header', description: 'Configure header' },\n  { id: 3, path: '/reviews', label: 'Reviews', description: 'Review settings' },\n  { id: 4, path: '/style', label: 'Style', description: 'Customize appearance' },\n  { id: 5, path: '/settings', label: 'Settings', description: 'Final configuration' },\n];\n\nconst NavigationContainer = styled.nav`\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin-top: 20px;\n`;\n\nconst StepsList = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    gap: 16px;\n  }\n`;\n\nconst ProgressLine = styled.div<{ progress: number }>`\n  position: absolute;\n  top: 20px;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background-color: #e0e0e0;\n  z-index: 1;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: ${props => props.progress}%;\n    background-color: #4285f4;\n    transition: width 0.3s ease;\n  }\n\n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n\nconst StepItem = styled.div<{ isActive: boolean; isCompleted: boolean }>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  cursor: pointer;\n  z-index: 2;\n  position: relative;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n  }\n\n  @media (max-width: 768px) {\n    flex-direction: row;\n    width: 100%;\n    padding: 12px;\n    border-radius: 8px;\n    background-color: ${props => props.isActive ? '#f0f4ff' : 'transparent'};\n    border: 1px solid ${props => props.isActive ? '#4285f4' : 'transparent'};\n  }\n`;\n\nconst StepCircle = styled.div<{ isActive: boolean; isCompleted: boolean }>`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 8px;\n  transition: all 0.2s ease;\n  \n  background-color: ${props => {\n    if (props.isCompleted) return '#34a853';\n    if (props.isActive) return '#4285f4';\n    return '#e0e0e0';\n  }};\n  \n  color: ${props => {\n    if (props.isCompleted || props.isActive) return '#ffffff';\n    return '#666666';\n  }};\n\n  @media (max-width: 768px) {\n    margin-bottom: 0;\n    margin-right: 12px;\n  }\n`;\n\nconst StepContent = styled.div`\n  text-align: center;\n\n  @media (max-width: 768px) {\n    text-align: left;\n    flex: 1;\n  }\n`;\n\nconst StepLabel = styled.div<{ isActive: boolean }>`\n  font-weight: ${props => props.isActive ? '600' : '500'};\n  font-size: 14px;\n  color: ${props => props.isActive ? '#4285f4' : '#333333'};\n  margin-bottom: 4px;\n`;\n\nconst StepDescription = styled.div`\n  font-size: 12px;\n  color: #666666;\n`;\n\nexport function StepNavigation() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { currentStep, setCurrentStep } = useWidget();\n\n  const handleStepClick = (step: typeof steps[0]) => {\n    setCurrentStep(step.id);\n    navigate(step.path);\n  };\n\n  const getCurrentStepFromPath = () => {\n    const currentPath = location.pathname;\n    const step = steps.find(s => s.path === currentPath);\n    return step ? step.id : 0;\n  };\n\n  const activeStep = getCurrentStepFromPath();\n  const progress = (activeStep / (steps.length - 1)) * 100;\n\n  return (\n    <NavigationContainer>\n      <StepsList>\n        <ProgressLine progress={progress} />\n        {steps.map((step) => {\n          const isActive = step.id === activeStep;\n          const isCompleted = step.id < activeStep;\n          \n          return (\n            <StepItem\n              key={step.id}\n              isActive={isActive}\n              isCompleted={isCompleted}\n              onClick={() => handleStepClick(step)}\n            >\n              <StepCircle isActive={isActive} isCompleted={isCompleted}>\n                {isCompleted ? '✓' : step.id + 1}\n              </StepCircle>\n              <StepContent>\n                <StepLabel isActive={isActive}>{step.label}</StepLabel>\n                <StepDescription>{step.description}</StepDescription>\n              </StepContent>\n            </StepItem>\n          );\n        })}\n      </StepsList>\n    </NavigationContainer>\n  );\n}", "import React from 'react';\nimport styled from 'styled-components';\nimport { useWidget } from '../../context/WidgetContext';\n\nconst HeaderContainer = styled.header`\n  background-color: #ffffff;\n  border-bottom: 1px solid #e0e0e0;\n  padding: 16px 0;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n`;\n\nconst HeaderContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  @media (max-width: 768px) {\n    padding: 0 16px;\n    flex-direction: column;\n    gap: 12px;\n  }\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: #333333;\n  margin: 0;\n\n  @media (max-width: 768px) {\n    font-size: 20px;\n    text-align: center;\n  }\n`;\n\nconst Actions = styled.div`\n  display: flex;\n  gap: 12px;\n  align-items: center;\n\n  @media (max-width: 768px) {\n    width: 100%;\n    justify-content: center;\n  }\n`;\n\nconst ResetButton = styled.button`\n  background: none;\n  border: 1px solid #e0e0e0;\n  color: #666666;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: #f5f5f5;\n    border-color: #d0d0d0;\n  }\n\n  &:active {\n    transform: translateY(1px);\n  }\n`;\n\nconst SaveButton = styled.button`\n  background-color: #4285f4;\n  border: none;\n  color: white;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: #3367d6;\n  }\n\n  &:active {\n    transform: translateY(1px);\n  }\n\n  &:disabled {\n    background-color: #cccccc;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nexport function Header() {\n  const { resetConfig, config } = useWidget();\n\n  const handleReset = () => {\n    if (window.confirm('Are you sure you want to reset all configuration? This action cannot be undone.')) {\n      resetConfig();\n    }\n  };\n\n  const handleSave = () => {\n    // TODO: Implement save functionality in later tasks\n    console.log('Save configuration:', config);\n    alert('Save functionality will be implemented in later tasks');\n  };\n\n  return (\n    <HeaderContainer>\n      <HeaderContent>\n        <Title>Google Reviews Widget Generator</Title>\n        <Actions>\n          <ResetButton onClick={handleReset}>\n            Reset\n          </ResetButton>\n          <SaveButton onClick={handleSave}>\n            Save Configuration\n          </SaveButton>\n        </Actions>\n      </HeaderContent>\n    </HeaderContainer>\n  );\n}", "import React from 'react';\nimport styled from 'styled-components';\nimport { Outlet } from 'react-router-dom';\nimport { StepNavigation } from './StepNavigation';\nimport { Header } from './Header';\n\nconst LayoutContainer = styled.div`\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Main = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  padding: 0 20px;\n\n  @media (max-width: 768px) {\n    padding: 0 16px;\n  }\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n  padding: 24px 0;\n\n  @media (max-width: 768px) {\n    gap: 16px;\n    padding: 16px 0;\n  }\n`;\n\nexport function Layout() {\n  return (\n    <LayoutContainer>\n      <Header />\n      <Main>\n        <StepNavigation />\n        <ContentArea>\n          <Outlet />\n        </ContentArea>\n      </Main>\n    </LayoutContainer>\n  );\n}", "import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport styled from 'styled-components';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n}\n\ninterface State {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: ErrorInfo | null;\n}\n\nconst ErrorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  padding: 32px;\n  text-align: center;\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 8px;\n  margin: 16px;\n`;\n\nconst ErrorIcon = styled.div`\n  font-size: 48px;\n  color: #dc2626;\n  margin-bottom: 16px;\n`;\n\nconst ErrorTitle = styled.h2`\n  color: #dc2626;\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n`;\n\nconst ErrorMessage = styled.p`\n  color: #7f1d1d;\n  font-size: 16px;\n  margin: 0 0 24px 0;\n  max-width: 600px;\n  line-height: 1.5;\n`;\n\nconst ErrorActions = styled.div`\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n  justify-content: center;\n`;\n\nconst Button = styled.button`\n  padding: 12px 24px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &.primary {\n    background-color: #dc2626;\n    color: white;\n\n    &:hover {\n      background-color: #b91c1c;\n    }\n  }\n\n  &.secondary {\n    background-color: white;\n    color: #dc2626;\n    border: 1px solid #dc2626;\n\n    &:hover {\n      background-color: #fef2f2;\n    }\n  }\n`;\n\nconst ErrorDetails = styled.details`\n  margin-top: 24px;\n  text-align: left;\n  max-width: 800px;\n  width: 100%;\n\n  summary {\n    cursor: pointer;\n    color: #7f1d1d;\n    font-weight: 500;\n    margin-bottom: 8px;\n  }\n\n  pre {\n    background-color: #f3f4f6;\n    padding: 16px;\n    border-radius: 4px;\n    overflow-x: auto;\n    font-size: 12px;\n    color: #374151;\n    white-space: pre-wrap;\n    word-break: break-word;\n  }\n`;\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<State> {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // Log error to console in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error('ErrorBoundary caught an error:', error);\n      console.error('Error info:', errorInfo);\n    }\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // Log error to monitoring service in production\n    if (process.env.NODE_ENV === 'production') {\n      this.logErrorToService(error, errorInfo);\n    }\n  }\n\n  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {\n    // In a real application, you would send this to a monitoring service\n    // like Sentry, LogRocket, or Bugsnag\n    try {\n      const errorData = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent,\n        url: window.location.href,\n      };\n\n      // Example: Send to monitoring service\n      // monitoringService.captureException(error, { extra: errorData });\n      \n      console.error('Error logged to monitoring service:', errorData);\n    } catch (loggingError) {\n      console.error('Failed to log error to monitoring service:', loggingError);\n    }\n  };\n\n  private handleReload = () => {\n    window.location.reload();\n  };\n\n  private handleReset = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n    });\n  };\n\n  private handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Use custom fallback if provided\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default error UI\n      return (\n        <ErrorContainer>\n          <ErrorIcon>⚠️</ErrorIcon>\n          <ErrorTitle>Something went wrong</ErrorTitle>\n          <ErrorMessage>\n            We're sorry, but something unexpected happened. The error has been logged \n            and our team has been notified. Please try refreshing the page or go back to the home page.\n          </ErrorMessage>\n          \n          <ErrorActions>\n            <Button className=\"primary\" onClick={this.handleReload}>\n              Refresh Page\n            </Button>\n            <Button className=\"secondary\" onClick={this.handleReset}>\n              Try Again\n            </Button>\n            <Button className=\"secondary\" onClick={this.handleGoHome}>\n              Go Home\n            </Button>\n          </ErrorActions>\n\n          {process.env.NODE_ENV === 'development' && this.state.error && (\n            <ErrorDetails>\n              <summary>Error Details (Development Only)</summary>\n              <div>\n                <strong>Error:</strong>\n                <pre>{this.state.error.message}</pre>\n                {this.state.error.stack && (\n                  <>\n                    <strong>Stack Trace:</strong>\n                    <pre>{this.state.error.stack}</pre>\n                  </>\n                )}\n                {this.state.errorInfo?.componentStack && (\n                  <>\n                    <strong>Component Stack:</strong>\n                    <pre>{this.state.errorInfo.componentStack}</pre>\n                  </>\n                )}\n              </div>\n            </ErrorDetails>\n          )}\n        </ErrorContainer>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;", "import React, { Suspense } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { WidgetProvider } from './context/WidgetContext';\nimport { Layout } from './components/Layout/Layout';\nimport ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';\n\n// Lazy load page components for code splitting\nconst SourcePage = React.lazy(() => import('./pages/SourcePage').then(module => ({ default: module.SourcePage })));\nconst LayoutPage = React.lazy(() => import('./pages/LayoutPage').then(module => ({ default: module.LayoutPage })));\nconst HeaderPage = React.lazy(() => import('./pages/HeaderPage').then(module => ({ default: module.HeaderPage })));\nconst ReviewsPage = React.lazy(() => import('./pages/ReviewsPage').then(module => ({ default: module.ReviewsPage })));\nconst StylePage = React.lazy(() => import('./pages/StylePage').then(module => ({ default: module.StylePage })));\nconst SettingsPage = React.lazy(() => import('./pages/SettingsPage').then(module => ({ default: module.SettingsPage })));\n\n// Loading component for Suspense fallback\nconst PageLoader: React.FC = () => (\n  <div style={{\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: '200px',\n    fontSize: '16px',\n    color: '#666'\n  }}>\n    <div style={{\n      width: '24px',\n      height: '24px',\n      border: '2px solid #f3f3f3',\n      borderTop: '2px solid #3498db',\n      borderRadius: '50%',\n      animation: 'spin 1s linear infinite',\n      marginRight: '12px'\n    }}></div>\n    Loading...\n    <style>{`\n      @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n    `}</style>\n  </div>\n);\n\nfunction App() {\n  return (\n    <ErrorBoundary onError={(error, errorInfo) => {\n      // Log to monitoring service in production\n      if (process.env.NODE_ENV === 'production') {\n        console.error('Global app error:', error, errorInfo);\n        // Example: Send to monitoring service\n        // monitoringService.captureException(error, { extra: errorInfo });\n      }\n    }}>\n      <WidgetProvider>\n        <Router>\n          <Routes>\n            <Route path=\"/\" element={<Layout />}>\n              <Route index element={\n                <Suspense fallback={<PageLoader />}>\n                  <SourcePage />\n                </Suspense>\n              } />\n              <Route path=\"layout\" element={\n                <Suspense fallback={<PageLoader />}>\n                  <LayoutPage />\n                </Suspense>\n              } />\n              <Route path=\"header\" element={\n                <Suspense fallback={<PageLoader />}>\n                  <HeaderPage />\n                </Suspense>\n              } />\n              <Route path=\"reviews\" element={\n                <Suspense fallback={<PageLoader />}>\n                  <ReviewsPage />\n                </Suspense>\n              } />\n              <Route path=\"style\" element={\n                <Suspense fallback={<PageLoader />}>\n                  <StylePage />\n                </Suspense>\n              } />\n              <Route path=\"settings\" element={\n                <Suspense fallback={<PageLoader />}>\n                  <SettingsPage />\n                </Suspense>\n              } />\n            </Route>\n          </Routes>\n        </Router>\n      </WidgetProvider>\n    </ErrorBoundary>\n  );\n}\n\nexport default App;", "import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App.tsx';\nimport './index.css';\n\nReactDOM.createRoot(document.getElementById('root')!).render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);"], "file": "js/index-ad9d0ad0.js"}