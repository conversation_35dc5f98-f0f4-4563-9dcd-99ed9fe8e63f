import{u as e,j as s}from"./index-ad9d0ad0.js";import{d as r}from"./chunk-69735360.js";import{r as o}from"./chunk-0fa44877.js";import{W as t}from"./chunk-9d331b18.js";const n={}.VITE_API_URL||"http://localhost:3001/api";class i extends Error{constructor(e,s){super(e.message),this.error=e,this.status=s,this.name="ApiException"}}async function a(e){return async function(e,s={}){const r=`${n}${e}`,o={headers:{"Content-Type":"application/json",...s.headers},...s};try{const e=await fetch(r,o);if(!e.ok){let s;try{s=await e.json()}catch{s={code:"UNKNOWN_ERROR",message:`HTTP ${e.status}: ${e.statusText}`,timestamp:(new Date).toISOString()}}throw new i(s,e.status)}return await e.json()}catch(t){if(t instanceof i)throw t;if(t instanceof TypeError&&t.message.includes("fetch"))throw new i({code:"NETWORK_ERROR",message:"Unable to connect to the server. Please check your internet connection.",timestamp:(new Date).toISOString()},0);throw new i({code:"UNKNOWN_ERROR",message:t instanceof Error?t.message:"An unexpected error occurred",timestamp:(new Date).toISOString()},0)}}("/business/search",{method:"POST",body:JSON.stringify(e)})}const l=r.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`,d=r.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`,c=r.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,p=r.label`
  font-size: 14px;
  font-weight: 500;
  color: #333333;
`,u=r.input`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`,f=r.button`
  padding: 12px 24px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover:not(:disabled) {
    background-color: #3367d6;
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`,x=r.div`
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`,h=r.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`,g=r.div`
  padding: 16px;
  border: 2px solid ${e=>e.selected?"#4285f4":"#e0e0e0"};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${e=>e.selected?"#f8f9ff":"#ffffff"};

  &:hover {
    border-color: #4285f4;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
  }
`,m=r.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
`,b=r.img`
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
`,j=r.div`
  flex: 1;
`,y=r.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 4px 0;
`,w=r.p`
  font-size: 14px;
  color: #666666;
  margin: 0 0 8px 0;
`,v=r.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666666;
`,S=r.div`
  display: flex;
  align-items: center;
  gap: 2px;
`,k=r.span`
  color: ${e=>e.filled?"#ffa500":"#e0e0e0"};
  font-size: 16px;
`,E=r.div`
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
`,z=r.div`
  padding: 32px;
  text-align: center;
  color: #666666;
  font-size: 16px;
`,T=r.p`
  font-size: 14px;
  color: #666666;
  margin: 8px 0 0 0;
  line-height: 1.4;
`;function N(e,s){const[r,t]=o.useState(e);return o.useEffect(()=>{const r=setTimeout(()=>{t(e)},s);return()=>{clearTimeout(r)}},[e,s]),r}function O({onBusinessSelect:r}){const{config:t,updateConfig:n}=e(),[O,R]=o.useState(""),[C,I]=o.useState(""),[U,$]=o.useState(""),[A,F]=o.useState([]),[P,B]=o.useState(!1),[M,_]=o.useState(null),[q,D]=o.useState(t.business||null),[G,W]=o.useState(!1),K=N(O,500),L=N(C,500);o.useEffect(()=>{K.trim()&&G&&H()},[K,L]);const H=o.useCallback(async()=>{if(O.trim()){B(!0),_(null),W(!0);try{const e={query:O.trim(),...C.trim()&&{location:C.trim()},...U.trim()&&{googleMapsUrl:U.trim()}},s=await a(e);F(s.businesses),0===s.businesses.length&&_("No businesses found. Try adjusting your search terms or adding a location.")}catch(e){e instanceof i&&e.error?_(e.error.message):e instanceof Error?_(e.message):_("Failed to search for businesses. Please try again."),F([])}finally{B(!1)}}else _("Please enter a business name to search")},[O,C,U]),J=e=>{const r=[],o=Math.floor(e),t=e%1>=.5;for(let n=0;n<5;n++)n<o?r.push(s.jsx(k,{filled:!0,children:"★"},n)):n===o&&t?r.push(s.jsx(k,{filled:!0,children:"☆"},n)):r.push(s.jsx(k,{filled:!1,children:"☆"},n));return r};return s.jsxs(l,{children:[s.jsxs(d,{onSubmit:e=>{e.preventDefault(),H()},children:[s.jsxs(c,{children:[s.jsx(p,{htmlFor:"business-query",children:"Business Name *"}),s.jsx(u,{id:"business-query",type:"text",value:O,onChange:e=>R(e.target.value),placeholder:"Enter your business name...",disabled:P,required:!0}),s.jsx(T,{children:"Enter the name of your business as it appears on Google"})]}),s.jsxs(c,{children:[s.jsx(p,{htmlFor:"business-location",children:"Location (Optional)"}),s.jsx(u,{id:"business-location",type:"text",value:C,onChange:e=>I(e.target.value),placeholder:"City, State or Full Address...",disabled:P}),s.jsx(T,{children:"Add a location to help narrow down search results"})]}),s.jsxs(c,{children:[s.jsx(p,{htmlFor:"google-maps-url",children:"Google Maps URL (Alternative)"}),s.jsx(u,{id:"google-maps-url",type:"url",value:U,onChange:e=>$(e.target.value),placeholder:"https://maps.google.com/...",disabled:P}),s.jsx(T,{children:"Alternatively, paste a Google Maps link to your business"})]}),s.jsxs(f,{type:"submit",disabled:P||!O.trim(),children:[P&&s.jsx(x,{}),P?"Searching...":"Search Business"]})]}),M&&s.jsx(E,{children:M}),A.length>0&&s.jsx(h,{children:A.map(e=>{var o;return s.jsx(g,{selected:(null==q?void 0:q.placeId)===e.placeId,onClick:()=>(e=>{D(e),n({business:e}),null==r||r(e)})(e),children:s.jsxs(m,{children:[e.photoUrl&&s.jsx(b,{src:e.photoUrl,alt:e.name,onError:e=>{e.currentTarget.style.display="none"}}),s.jsxs(j,{children:[s.jsx(y,{children:e.name}),e.address&&s.jsx(w,{children:e.address}),s.jsxs(v,{children:[s.jsx(S,{children:J(e.rating||0)}),s.jsxs("span",{children:[(null==(o=e.rating)?void 0:o.toFixed(1))||"No rating",e.reviewCount&&` (${e.reviewCount} reviews)`]})]})]})]})},e.placeId)})}),G&&0===A.length&&!P&&!M&&s.jsx(z,{children:"No businesses found for your search. Try different keywords or add a location."})]})}const R=r.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`,C=r.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`,I=r.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
`;function U(){return s.jsxs(R,{children:[s.jsx(C,{children:"Find Your Business"}),s.jsx(I,{children:"Search for your business by name and address, or paste a Google Maps link to get started."}),s.jsx(t,{widgetStep:"business-search",children:s.jsx(O,{onBusinessSelect:e=>{}})})]})}r.div`
  margin-top: 24px;
  padding: 16px;
  background-color: #f0f8ff;
  border: 1px solid #4285f4;
  border-radius: 8px;
`,r.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
`,r.p`
  font-size: 14px;
  color: #666666;
  margin: 0;
`;export{U as SourcePage};
//# sourceMappingURL=SourcePage-948a5ddb.js.map
