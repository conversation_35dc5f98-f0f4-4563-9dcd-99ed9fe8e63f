var e=Object.defineProperty,t=(t,r,n)=>(((t,r,n)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n})(t,"symbol"!=typeof r?r+"":r,n),n);import{r,a as n,u as o,b as i,O as s,R as a,B as d,c as l,d as c}from"./chunk-0fa44877.js";import{d as p}from"./chunk-69735360.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var f={exports:{}},x={},h=r,u=Symbol.for("react.element"),g=Symbol.for("react.fragment"),m=Object.prototype.hasOwnProperty,y=h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,b={key:!0,ref:!0,__self:!0,__source:!0};function w(e,t,r){var n,o={},i=null,s=null;for(n in void 0!==r&&(i=""+r),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(s=t.ref),t)m.call(t,n)&&!b.hasOwnProperty(n)&&(o[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===o[n]&&(o[n]=t[n]);return{$$typeof:u,type:e,key:i,ref:s,props:o,_owner:y.current}}x.Fragment=g,x.jsx=w,x.jsxs=w,f.exports=x;var j=f.exports,v={},_=n;v.createRoot=_.createRoot,v.hydrateRoot=_.hydrateRoot;const k={},S=function(e,t,r){if(!t||0===t.length)return e();const n=document.getElementsByTagName("link");return Promise.all(t.map(e=>{if((e=function(e){return"/"+e}(e))in k)return;k[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(!!r)for(let r=n.length-1;r>=0;r--){const o=n[r];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${o}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script",i.crossOrigin=""),i.href=e,document.head.appendChild(i),t?new Promise((t,r)=>{i.addEventListener("load",t),i.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${e}`)))}):void 0})).then(()=>e()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})},E={template:"carousel",styling:{colors:{primary:"#4285f4",secondary:"#34a853",background:"#ffffff",text:"#333333",border:"#e0e0e0"},fonts:{family:"Inter, sans-serif",size:"14px",weight:"400"},dimensions:{width:"400px",height:"300px",borderRadius:"8px"},spacing:{padding:"16px",margin:"0px",gap:"12px"}},settings:{maxReviews:5,minRating:1,sortBy:"newest",showPhotos:!0,showDates:!0,autoRefresh:!0}};function P(e,t){switch(t.type){case"UPDATE_CONFIG":return{...e,config:{...e.config,...t.payload,styling:t.payload.styling?{...e.config.styling,...t.payload.styling}:e.config.styling,settings:t.payload.settings?{...e.config.settings,...t.payload.settings}:e.config.settings}};case"RESET_CONFIG":return{...e,config:{...E},currentStep:0};case"SET_STEP":return{...e,currentStep:t.payload};default:return e}}const R=r.createContext(void 0);function C({children:e}){const[t,n]=r.useReducer(P,{config:{...E},currentStep:0}),o={config:t.config,updateConfig:e=>{n({type:"UPDATE_CONFIG",payload:e})},resetConfig:()=>{n({type:"RESET_CONFIG"})},currentStep:t.currentStep,setCurrentStep:e=>{n({type:"SET_STEP",payload:e})}};return j.jsx(R.Provider,{value:o,children:e})}function O(){const e=r.useContext(R);if(void 0===e)throw new Error("useWidget must be used within a WidgetProvider");return e}const T=[{id:0,path:"/",label:"Source",description:"Find your business"},{id:1,path:"/layout",label:"Layout",description:"Choose template"},{id:2,path:"/header",label:"Header",description:"Configure header"},{id:3,path:"/reviews",label:"Reviews",description:"Review settings"},{id:4,path:"/style",label:"Style",description:"Customize appearance"},{id:5,path:"/settings",label:"Settings",description:"Final configuration"}],z=p.nav`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
`,A=p.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`,I=p.div`
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: ${e=>e.progress}%;
    background-color: #4285f4;
    transition: width 0.3s ease;
  }

  @media (max-width: 768px) {
    display: none;
  }
`,L=p.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 2;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    flex-direction: row;
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background-color: ${e=>e.isActive?"#f0f4ff":"transparent"};
    border: 1px solid ${e=>e.isActive?"#4285f4":"transparent"};
  }
`,D=p.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  
  background-color: ${e=>e.isCompleted?"#34a853":e.isActive?"#4285f4":"#e0e0e0"};
  
  color: ${e=>e.isCompleted||e.isActive?"#ffffff":"#666666"};

  @media (max-width: 768px) {
    margin-bottom: 0;
    margin-right: 12px;
  }
`,N=p.div`
  text-align: center;

  @media (max-width: 768px) {
    text-align: left;
    flex: 1;
  }
`,$=p.div`
  font-weight: ${e=>e.isActive?"600":"500"};
  font-size: 14px;
  color: ${e=>e.isActive?"#4285f4":"#333333"};
  margin-bottom: 4px;
`,F=p.div`
  font-size: 12px;
  color: #666666;
`;function G(){const e=o(),t=i(),{currentStep:r,setCurrentStep:n}=O(),s=(()=>{const e=t.pathname,r=T.find(t=>t.path===e);return r?r.id:0})(),a=s/(T.length-1)*100;return j.jsx(z,{children:j.jsxs(A,{children:[j.jsx(I,{progress:a}),T.map(t=>{const r=t.id===s,o=t.id<s;return j.jsxs(L,{isActive:r,isCompleted:o,onClick:()=>(t=>{n(t.id),e(t.path)})(t),children:[j.jsx(D,{isActive:r,isCompleted:o,children:o?"✓":t.id+1}),j.jsxs(N,{children:[j.jsx($,{isActive:r,children:t.label}),j.jsx(F,{children:t.description})]})]},t.id)})]})})}const H=p.header`
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`,V=p.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    padding: 0 16px;
    flex-direction: column;
    gap: 12px;
  }
`,W=p.h1`
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 20px;
    text-align: center;
  }
`,B=p.div`
  display: flex;
  gap: 12px;
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
`,U=p.button`
  background: none;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
  }

  &:active {
    transform: translateY(1px);
  }
`,Y=p.button`
  background-color: #4285f4;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background-color: #3367d6;
  }

  &:active {
    transform: translateY(1px);
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
  }
`;function q(){const{resetConfig:e,config:t}=O();return j.jsx(H,{children:j.jsxs(V,{children:[j.jsx(W,{children:"Google Reviews Widget Generator"}),j.jsxs(B,{children:[j.jsx(U,{onClick:()=>{window.confirm("Are you sure you want to reset all configuration? This action cannot be undone.")&&e()},children:"Reset"}),j.jsx(Y,{onClick:()=>{alert("Save functionality will be implemented in later tasks")},children:"Save Configuration"})]})]})})}const M=p.div`
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
`,K=p.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 20px;

  @media (max-width: 768px) {
    padding: 0 16px;
  }
`,J=p.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0;

  @media (max-width: 768px) {
    gap: 16px;
    padding: 16px 0;
  }
`;function Q(){return j.jsxs(M,{children:[j.jsx(q,{}),j.jsxs(K,{children:[j.jsx(G,{}),j.jsx(J,{children:j.jsx(s,{})})]})]})}const X=p.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 32px;
  text-align: center;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 16px;
`,Z=p.div`
  font-size: 48px;
  color: #dc2626;
  margin-bottom: 16px;
`,ee=p.h2`
  color: #dc2626;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
`,te=p.p`
  color: #7f1d1d;
  font-size: 16px;
  margin: 0 0 24px 0;
  max-width: 600px;
  line-height: 1.5;
`,re=p.div`
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
`,ne=p.button`
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.primary {
    background-color: #dc2626;
    color: white;

    &:hover {
      background-color: #b91c1c;
    }
  }

  &.secondary {
    background-color: white;
    color: #dc2626;
    border: 1px solid #dc2626;

    &:hover {
      background-color: #fef2f2;
    }
  }
`;p.details`
  margin-top: 24px;
  text-align: left;
  max-width: 800px;
  width: 100%;

  summary {
    cursor: pointer;
    color: #7f1d1d;
    font-weight: 500;
    margin-bottom: 8px;
  }

  pre {
    background-color: #f3f4f6;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    color: #374151;
    white-space: pre-wrap;
    word-break: break-word;
  }
`;class oe extends r.Component{constructor(e){super(e),t(this,"logErrorToService",(e,t)=>{try{e.message,e.stack,t.componentStack,(new Date).toISOString(),navigator.userAgent,window.location.href}catch(r){}}),t(this,"handleReload",()=>{window.location.reload()}),t(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})}),t(this,"handleGoHome",()=>{window.location.href="/"}),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),this.props.onError&&this.props.onError(e,t),this.logErrorToService(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:j.jsxs(X,{children:[j.jsx(Z,{children:"⚠️"}),j.jsx(ee,{children:"Something went wrong"}),j.jsx(te,{children:"We're sorry, but something unexpected happened. The error has been logged and our team has been notified. Please try refreshing the page or go back to the home page."}),j.jsxs(re,{children:[j.jsx(ne,{className:"primary",onClick:this.handleReload,children:"Refresh Page"}),j.jsx(ne,{className:"secondary",onClick:this.handleReset,children:"Try Again"}),j.jsx(ne,{className:"secondary",onClick:this.handleGoHome,children:"Go Home"})]}),!1]}):this.props.children}}const ie=a.lazy(()=>S(()=>import("./SourcePage-948a5ddb.js"),["js/SourcePage-948a5ddb.js","js/chunk-69735360.js","js/chunk-0fa44877.js","js/chunk-9d331b18.js"]).then(e=>({default:e.SourcePage}))),se=a.lazy(()=>S(()=>import("./LayoutPage-df291e0b.js"),["js/LayoutPage-df291e0b.js","js/chunk-0fa44877.js","js/chunk-69735360.js","js/chunk-9d331b18.js"]).then(e=>({default:e.LayoutPage}))),ae=a.lazy(()=>S(()=>import("./HeaderPage-10505a84.js"),["js/HeaderPage-10505a84.js","js/chunk-69735360.js","js/chunk-0fa44877.js","js/chunk-9d331b18.js"]).then(e=>({default:e.HeaderPage}))),de=a.lazy(()=>S(()=>import("./ReviewsPage-c8984000.js"),["js/ReviewsPage-c8984000.js","js/chunk-69735360.js","js/chunk-0fa44877.js","js/chunk-9d331b18.js"]).then(e=>({default:e.ReviewsPage}))),le=a.lazy(()=>S(()=>import("./StylePage-43ea26e6.js"),["js/StylePage-43ea26e6.js","js/chunk-69735360.js","js/chunk-0fa44877.js","js/chunk-9ab7d155.js","js/PreviewComponent-d11fc68a.js","js/chunk-9d331b18.js"]).then(e=>({default:e.StylePage}))),ce=a.lazy(()=>S(()=>import("./SettingsPage-6bef2bcd.js"),["js/SettingsPage-6bef2bcd.js","js/chunk-69735360.js","js/chunk-0fa44877.js","js/chunk-9d331b18.js"]).then(e=>({default:e.SettingsPage}))),pe=()=>j.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"200px",fontSize:"16px",color:"#666"},children:[j.jsx("div",{style:{width:"24px",height:"24px",border:"2px solid #f3f3f3",borderTop:"2px solid #3498db",borderRadius:"50%",animation:"spin 1s linear infinite",marginRight:"12px"}}),"Loading...",j.jsx("style",{children:"\n      @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n    "})]});function fe(){return j.jsx(oe,{onError:(e,t)=>{},children:j.jsx(C,{children:j.jsx(d,{children:j.jsx(l,{children:j.jsxs(c,{path:"/",element:j.jsx(Q,{}),children:[j.jsx(c,{index:!0,element:j.jsx(r.Suspense,{fallback:j.jsx(pe,{}),children:j.jsx(ie,{})})}),j.jsx(c,{path:"layout",element:j.jsx(r.Suspense,{fallback:j.jsx(pe,{}),children:j.jsx(se,{})})}),j.jsx(c,{path:"header",element:j.jsx(r.Suspense,{fallback:j.jsx(pe,{}),children:j.jsx(ae,{})})}),j.jsx(c,{path:"reviews",element:j.jsx(r.Suspense,{fallback:j.jsx(pe,{}),children:j.jsx(de,{})})}),j.jsx(c,{path:"style",element:j.jsx(r.Suspense,{fallback:j.jsx(pe,{}),children:j.jsx(le,{})})}),j.jsx(c,{path:"settings",element:j.jsx(r.Suspense,{fallback:j.jsx(pe,{}),children:j.jsx(ce,{})})})]})})})})})}v.createRoot(document.getElementById("root")).render(j.jsx(a.StrictMode,{children:j.jsx(fe,{})}));export{S as _,j,O as u};
//# sourceMappingURL=index-ad9d0ad0.js.map
