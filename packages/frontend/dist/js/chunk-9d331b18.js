var e=Object.defineProperty,r=(r,o,t)=>(((r,o,t)=>{o in r?e(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t})(r,"symbol"!=typeof o?o+"":o,t),t);import{j as o}from"./index-ad9d0ad0.js";import{r as t}from"./chunk-0fa44877.js";import{d as n}from"./chunk-69735360.js";const i=n.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 24px;
  text-align: center;
  background-color: #fef7f0;
  border: 1px solid #fed7aa;
  border-radius: 8px;
  margin: 16px 0;
`,s=n.div`
  font-size: 32px;
  color: #ea580c;
  margin-bottom: 12px;
`,a=n.h3`
  color: #ea580c;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
`,c=n.p`
  color: #9a3412;
  font-size: 14px;
  margin: 0 0 16px 0;
  max-width: 400px;
  line-height: 1.4;
`,d=n.button`
  padding: 8px 16px;
  background-color: #ea580c;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #dc2626;
  }

  &:disabled {
    background-color: #d1d5db;
    cursor: not-allowed;
  }
`;class l extends t.Component{constructor(e){super(e),r(this,"handleRetry",()=>{this.setState({hasError:!1,error:null}),this.props.onRetry&&this.props.onRetry()}),this.state={hasError:!1,error:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){this.setState({error:e});this.props.widgetStep,e.message,e.stack,r.componentStack,(new Date).toISOString();this.props.onError&&this.props.onError(e,r)}getErrorMessage(){const e=this.props.widgetStep,r=this.state.error;return"business-search"===e?"Unable to search for businesses. Please check your internet connection and try again.":"template-selection"===e?"Failed to load widget templates. Please refresh the page and try again.":"customization"===e?"Error loading customization options. Your settings may not be saved properly.":"preview"===e?"Unable to generate widget preview. Please check your configuration and try again.":"embed-code"===e?"Failed to generate embed code. Please verify your widget configuration.":(null==r?void 0:r.message)?`An error occurred: ${r.message}`:"An unexpected error occurred while creating your widget."}render(){return this.state.hasError?o.jsxs(i,{children:[o.jsx(s,{children:"⚠️"}),o.jsx(a,{children:"Widget Error"}),o.jsx(c,{children:this.getErrorMessage()}),o.jsx(d,{onClick:this.handleRetry,children:"Try Again"})]}):this.props.children}}export{l as W};
//# sourceMappingURL=chunk-9d331b18.js.map
