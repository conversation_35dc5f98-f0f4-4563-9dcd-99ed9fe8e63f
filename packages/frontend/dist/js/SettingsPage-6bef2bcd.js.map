{"version": 3, "file": "SettingsPage-6bef2bcd.js", "sources": ["../../src/pages/SettingsPage.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';\n\nconst PageContainer = styled.div`\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  text-align: center;\n`;\n\nconst Title = styled.h2`\n  font-size: 28px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 16px;\n`;\n\nconst Description = styled.p`\n  font-size: 16px;\n  color: #666666;\n  margin-bottom: 32px;\n  line-height: 1.6;\n`;\n\nconst PlaceholderContent = styled.div`\n  padding: 48px;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  border: 2px dashed #e0e0e0;\n`;\n\nconst PlaceholderText = styled.p`\n  font-size: 14px;\n  color: #999999;\n  margin: 0;\n`;\n\nexport function SettingsPage() {\n  return (\n    <PageContainer>\n      <Title>Final Settings</Title>\n      <Description>\n        Review your configuration and generate the embed code for your website.\n      </Description>\n      <WidgetErrorBoundary widgetStep=\"embed-code\">\n        <PlaceholderContent>\n          <PlaceholderText>\n            Final settings and embed code generation will be implemented in task 12\n          </PlaceholderText>\n        </PlaceholderContent>\n      </WidgetErrorBoundary>\n    </PageContainer>\n  );\n}"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "styled", "div", "Title", "h2", "Description", "p", "PlaceholderContent", "PlaceholderText", "SettingsPage", "children", "jsx", "WidgetErrorBoundary", "widgetStep"], "mappings": "oJAIA,MAAMA,EAAgBC,EAAOC,GAAA;;;;;;EAQvBC,EAAQF,EAAOG,EAAA;;;;;EAOfC,EAAcJ,EAAOK,CAAA;;;;;EAOrBC,EAAqBN,EAAOC,GAAA;;;;;EAO5BM,EAAkBP,EAAOK,CAAA;;;;EAMxB,SAASG,IACd,cACGT,EACC,CAAAU,SAAA,GAAAC,IAACR,GAAMO,SAAc,qBACrBC,IAACN,GAAYK,SAEb,4EACAC,EAAAA,IAACC,EAAoB,CAAAC,WAAW,aAC9BH,SAAAC,EAAAA,IAACJ,GACCG,SAACC,EAAAA,IAAAH,EAAA,CAAgBE,SAEjB,kFAKV"}