{"version": 3, "file": "TemplateSelectionComponent-e63c46c2.js", "sources": ["../../src/components/TemplateSelection/TemplatePreview.tsx", "../../src/components/TemplateSelection/TemplateSelectionComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { TemplateType } from '../../types/widget';\n\nconst PreviewWrapper = styled.div`\n  width: 100%;\n  height: 100%;\n  min-height: 180px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #666666;\n`;\n\n// Mock review data for previews\nconst mockReviews = [\n  {\n    id: '1',\n    authorName: '<PERSON>',\n    rating: 5,\n    text: 'Excellent service and friendly staff. Highly recommend!',\n    publishedDate: '2 days ago'\n  },\n  {\n    id: '2',\n    authorName: '<PERSON>',\n    rating: 4,\n    text: 'Great experience overall. Will definitely come back.',\n    publishedDate: '1 week ago'\n  },\n  {\n    id: '3',\n    authorName: '<PERSON>',\n    rating: 5,\n    text: 'Outstanding quality and attention to detail.',\n    publishedDate: '2 weeks ago'\n  }\n];\n\n// Carousel Preview\nconst CarouselPreview = styled.div`\n  width: 100%;\n  background: white;\n  border-radius: 6px;\n  padding: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n`;\n\nconst CarouselHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n`;\n\nconst CarouselNav = styled.div`\n  display: flex;\n  gap: 4px;\n`;\n\nconst NavDot = styled.div<{ $active?: boolean }>`\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background: ${props => props.$active ? '#4285f4' : '#e0e0e0'};\n`;\n\nconst ReviewCard = styled.div`\n  padding: 8px;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n`;\n\nconst ReviewHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n`;\n\nconst AuthorName = styled.span`\n  font-weight: 500;\n  font-size: 11px;\n`;\n\nconst Rating = styled.div`\n  color: #ffa500;\n  font-size: 10px;\n`;\n\nconst ReviewText = styled.p`\n  margin: 0;\n  font-size: 10px;\n  line-height: 1.3;\n  color: #666;\n`;\n\n// Badge Preview\nconst BadgePreview = styled.div`\n  background: white;\n  border-radius: 20px;\n  padding: 8px 12px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  font-size: 11px;\n`;\n\nconst BadgeRating = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 2px;\n  color: #ffa500;\n  font-weight: 600;\n`;\n\nconst BadgeText = styled.span`\n  color: #666;\n  font-size: 10px;\n`;\n\n// Grid Preview\nconst GridPreview = styled.div`\n  width: 100%;\n  background: white;\n  border-radius: 6px;\n  padding: 10px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n`;\n\nconst GridHeader = styled.div`\n  text-align: center;\n  margin-bottom: 8px;\n  padding-bottom: 6px;\n  border-bottom: 1px solid #f0f0f0;\n`;\n\nconst GridSummary = styled.div`\n  background: #f8f9fa;\n  padding: 6px;\n  border-radius: 4px;\n  margin-bottom: 8px;\n  font-size: 9px;\n  color: #666;\n  text-align: center;\n`;\n\nconst GridReviews = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 4px;\n`;\n\nconst GridReviewCard = styled.div`\n  padding: 4px;\n  border: 1px solid #f0f0f0;\n  border-radius: 3px;\n  font-size: 9px;\n`;\n\n// Simple Carousel Preview\nconst SimpleCarouselPreview = styled.div`\n  width: 100%;\n  background: white;\n  border-radius: 6px;\n  padding: 12px;\n  text-align: center;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n`;\n\nconst SimpleCarouselNav = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 4px;\n  margin-top: 8px;\n`;\n\n// Slider Preview\nconst SliderPreview = styled.div`\n  width: 100%;\n  background: white;\n  border-radius: 6px;\n  padding: 10px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n`;\n\nconst SliderTrack = styled.div`\n  display: flex;\n  gap: 6px;\n  overflow: hidden;\n`;\n\nconst SliderCard = styled.div`\n  min-width: 60px;\n  padding: 6px;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n  font-size: 9px;\n`;\n\n// Floating Badge Preview\nconst FloatingBadgePreview = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100px;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  border-radius: 6px;\n  overflow: hidden;\n`;\n\nconst FloatingBadgeElement = styled.div`\n  position: absolute;\n  bottom: 12px;\n  right: 12px;\n  background: white;\n  border-radius: 50%;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  font-size: 8px;\n`;\n\ninterface TemplatePreviewProps {\n  templateType: TemplateType;\n  isSelected: boolean;\n}\n\nexport function TemplatePreview({ templateType, isSelected }: TemplatePreviewProps) {\n  const renderPreview = () => {\n    switch (templateType) {\n      case 'carousel':\n        return (\n          <CarouselPreview>\n            <CarouselHeader>\n              <span style={{ fontSize: '11px', fontWeight: '600' }}>Customer Reviews</span>\n              <CarouselNav>\n                <NavDot $active />\n                <NavDot />\n                <NavDot />\n              </CarouselNav>\n            </CarouselHeader>\n            <ReviewCard>\n              <ReviewHeader>\n                <AuthorName>{mockReviews[0].authorName}</AuthorName>\n                <Rating>★★★★★</Rating>\n              </ReviewHeader>\n              <ReviewText>{mockReviews[0].text}</ReviewText>\n            </ReviewCard>\n          </CarouselPreview>\n        );\n\n      case 'badge':\n        return (\n          <BadgePreview>\n            <BadgeRating>\n              <span>★</span>\n              <span>4.8</span>\n            </BadgeRating>\n            <BadgeText>Based on 127 reviews</BadgeText>\n          </BadgePreview>\n        );\n\n      case 'grid':\n        return (\n          <GridPreview>\n            <GridHeader>\n              <div style={{ fontSize: '11px', fontWeight: '600' }}>★ 4.8 (127 reviews)</div>\n            </GridHeader>\n            <GridSummary>\n              AI Summary: Customers love the excellent service and friendly staff\n            </GridSummary>\n            <GridReviews>\n              {mockReviews.slice(0, 2).map((review, index) => (\n                <GridReviewCard key={index}>\n                  <div style={{ fontWeight: '500', marginBottom: '2px' }}>{review.authorName}</div>\n                  <div style={{ color: '#ffa500', fontSize: '8px' }}>★★★★★</div>\n                  <div style={{ marginTop: '2px' }}>{review.text.substring(0, 30)}...</div>\n                </GridReviewCard>\n              ))}\n            </GridReviews>\n          </GridPreview>\n        );\n\n      case 'simple-carousel':\n        return (\n          <SimpleCarouselPreview>\n            <div style={{ fontSize: '11px', fontWeight: '600', marginBottom: '6px' }}>\n              {mockReviews[0].authorName}\n            </div>\n            <div style={{ color: '#ffa500', fontSize: '10px', marginBottom: '4px' }}>★★★★★</div>\n            <div style={{ fontSize: '9px', color: '#666' }}>\n              \"{mockReviews[0].text}\"\n            </div>\n            <SimpleCarouselNav>\n              <NavDot $active />\n              <NavDot />\n              <NavDot />\n            </SimpleCarouselNav>\n          </SimpleCarouselPreview>\n        );\n\n      case 'slider':\n        return (\n          <SliderPreview>\n            <div style={{ fontSize: '11px', fontWeight: '600', marginBottom: '8px', textAlign: 'center' }}>\n              Recent Reviews\n            </div>\n            <SliderTrack>\n              {mockReviews.map((review, index) => (\n                <SliderCard key={index}>\n                  <div style={{ fontWeight: '500', marginBottom: '2px' }}>{review.authorName}</div>\n                  <div style={{ color: '#ffa500', fontSize: '8px' }}>★★★★★</div>\n                </SliderCard>\n              ))}\n            </SliderTrack>\n          </SliderPreview>\n        );\n\n      case 'floating-badge':\n        return (\n          <FloatingBadgePreview>\n            <div style={{ \n              position: 'absolute', \n              top: '8px', \n              left: '8px', \n              fontSize: '9px', \n              color: '#666',\n              background: 'rgba(255,255,255,0.8)',\n              padding: '2px 4px',\n              borderRadius: '3px'\n            }}>\n              Your Website\n            </div>\n            <FloatingBadgeElement>\n              <div style={{ color: '#ffa500', fontWeight: '600' }}>★ 4.8</div>\n              <div style={{ color: '#666' }}>127</div>\n            </FloatingBadgeElement>\n          </FloatingBadgePreview>\n        );\n\n      default:\n        return <div>Preview not available</div>;\n    }\n  };\n\n  return (\n    <PreviewWrapper>\n      {renderPreview()}\n    </PreviewWrapper>\n  );\n}", "import React from 'react';\nimport styled from 'styled-components';\nimport { useWidget } from '../../context/WidgetContext';\nimport { TemplateType } from '../../types/widget';\nimport { TemplatePreview } from './TemplatePreview';\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n\nconst Title = styled.h2`\n  font-size: 28px;\n  font-weight: 600;\n  color: #333333;\n  margin: 0 0 8px 0;\n  text-align: center;\n`;\n\nconst Description = styled.p`\n  font-size: 16px;\n  color: #666666;\n  margin: 0 0 32px 0;\n  text-align: center;\n  line-height: 1.6;\n`;\n\nconst TemplatesGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 24px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n`;\n\nconst TemplateCard = styled.div<{ $isSelected: boolean }>`\n  background: #ffffff;\n  border: 2px solid ${props => props.$isSelected ? '#4285f4' : '#e0e0e0'};\n  border-radius: 12px;\n  padding: 20px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  \n  &:hover {\n    border-color: ${props => props.$isSelected ? '#4285f4' : '#999999'};\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n  \n  ${props => props.$isSelected && `\n    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.2);\n  `}\n`;\n\nconst SelectedBadge = styled.div`\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: #4285f4;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n`;\n\nconst TemplateName = styled.h3`\n  font-size: 18px;\n  font-weight: 600;\n  color: #333333;\n  margin: 0 0 8px 0;\n`;\n\nconst TemplateDescription = styled.p`\n  font-size: 14px;\n  color: #666666;\n  margin: 0 0 16px 0;\n  line-height: 1.4;\n`;\n\nconst PreviewContainer = styled.div`\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 16px;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\n// Template definitions with descriptions\nconst templates: Array<{\n  type: TemplateType;\n  name: string;\n  description: string;\n}> = [\n  {\n    type: 'carousel',\n    name: 'Carousel Widget',\n    description: 'Interactive carousel with navigation arrows and smooth transitions between reviews.'\n  },\n  {\n    type: 'badge',\n    name: 'Badge',\n    description: 'Compact badge showing overall rating and review count, perfect for headers or sidebars.'\n  },\n  {\n    type: 'grid',\n    name: 'Grid with AI Summary',\n    description: 'Grid layout with AI-generated summary highlighting key themes from customer reviews.'\n  },\n  {\n    type: 'simple-carousel',\n    name: 'Simple Carousel',\n    description: 'Clean, minimal carousel design focusing on review content without distractions.'\n  },\n  {\n    type: 'slider',\n    name: 'Slider',\n    description: 'Horizontal slider with smooth auto-play and manual navigation controls.'\n  },\n  {\n    type: 'floating-badge',\n    name: 'Floating Badge',\n    description: 'Floating badge that can be positioned anywhere on your page with customizable placement.'\n  }\n];\n\nexport function TemplateSelectionComponent() {\n  const { config, updateConfig } = useWidget();\n\n  const handleTemplateSelect = (templateType: TemplateType) => {\n    updateConfig({ template: templateType });\n  };\n\n  return (\n    <Container>\n      <Title>Choose Your Template</Title>\n      <Description>\n        Select from our collection of professionally designed widget templates to match your website's style.\n      </Description>\n      \n      <TemplatesGrid>\n        {templates.map((template) => (\n          <TemplateCard\n            key={template.type}\n            $isSelected={config.template === template.type}\n            onClick={() => handleTemplateSelect(template.type)}\n          >\n            {config.template === template.type && (\n              <SelectedBadge>Selected</SelectedBadge>\n            )}\n            \n            <TemplateName>{template.name}</TemplateName>\n            <TemplateDescription>{template.description}</TemplateDescription>\n            \n            <PreviewContainer>\n              <TemplatePreview \n                templateType={template.type}\n                isSelected={config.template === template.type}\n              />\n            </PreviewContainer>\n          </TemplateCard>\n        ))}\n      </TemplatesGrid>\n    </Container>\n  );\n}"], "names": ["PreviewWrapper", "styled", "div", "mockReviews", "id", "<PERSON><PERSON><PERSON>", "rating", "text", "publishedDate", "CarouselPreview", "CarouselHeader", "CarouselNav", "NavDot", "props", "$active", "ReviewCard", "ReviewHeader", "<PERSON><PERSON><PERSON>", "span", "Rating", "ReviewText", "p", "BadgePreview", "BadgeRating", "BadgeText", "GridPreview", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Grid<PERSON>ummary", "GridReviews", "GridReviewCard", "SimpleCarouselPreview", "SimpleCarouselNav", "SliderPreview", "SliderTrack", "SliderCard", "FloatingBadgePreview", "FloatingBadgeElement", "TemplatePreview", "templateType", "isSelected", "jsx", "children", "style", "fontSize", "fontWeight", "slice", "map", "review", "index", "jsxs", "marginBottom", "color", "marginTop", "substring", "textAlign", "position", "top", "left", "background", "padding", "borderRadius", "renderPreview", "Container", "Title", "h2", "Description", "TemplatesGrid", "TemplateCard", "$isSelected", "SelectedBadge", "TemplateName", "h3", "TemplateDescription", "PreviewContainer", "templates", "type", "name", "description", "TemplateSelectionComponent", "config", "updateConfig", "useWidget", "template", "jsxRuntimeExports", "onClick", "handleTemplateSelect"], "mappings": "mHAIA,MAAMA,EAAiBC,EAAOC,GAAA;;;;;;;;;EAYxBC,EAAc,CAClB,CACEC,GAAI,IACJC,WAAY,gBACZC,OAAQ,EACRC,KAAM,0DACNC,cAAe,cAEjB,CACEJ,GAAI,IACJC,WAAY,YACZC,OAAQ,EACRC,KAAM,uDACNC,cAAe,cAEjB,CACEJ,GAAI,IACJC,WAAY,aACZC,OAAQ,EACRC,KAAM,+CACNC,cAAe,gBAKbC,EAAkBR,EAAOC,GAAA;;;;;;EAQzBQ,EAAiBT,EAAOC,GAAA;;;;;EAOxBS,EAAcV,EAAOC,GAAA;;;EAKrBU,EAASX,EAAOC,GAAA;;;;gBAIGW,GAAAA,EAAMC,QAAU,UAAY;EAG/CC,EAAad,EAAOC,GAAA;;;;EAMpBc,EAAef,EAAOC,GAAA;;;;;EAOtBe,EAAahB,EAAOiB,IAAA;;;EAKpBC,EAASlB,EAAOC,GAAA;;;EAKhBkB,EAAanB,EAAOoB,CAAA;;;;;EAQpBC,EAAerB,EAAOC,GAAA;;;;;;;;;EAWtBqB,EAActB,EAAOC,GAAA;;;;;;EAQrBsB,EAAYvB,EAAOiB,IAAA;;;EAMnBO,EAAcxB,EAAOC,GAAA;;;;;;EAQrBwB,EAAazB,EAAOC,GAAA;;;;;EAOpByB,EAAc1B,EAAOC,GAAA;;;;;;;;EAUrB0B,EAAc3B,EAAOC,GAAA;;;;EAMrB2B,EAAiB5B,EAAOC,GAAA;;;;;EAQxB4B,EAAwB7B,EAAOC,GAAA;;;;;;;EAS/B6B,EAAoB9B,EAAOC,GAAA;;;;;EAQ3B8B,EAAgB/B,EAAOC,GAAA;;;;;;EAQvB+B,EAAchC,EAAOC,GAAA;;;;EAMrBgC,EAAajC,EAAOC,GAAA;;;;;;EASpBiC,EAAuBlC,EAAOC,GAAA;;;;;;;EAS9BkC,EAAuBnC,EAAOC,GAAA;;;;;;;;;;;;;;EAqB7B,SAASmC,GAAgBC,aAAEA,EAAcC,WAAAA,IAuH5C,SAAAC,IAACxC,EACE,CAAAyC,SAvHiB,MACpB,OAAQH,GACN,IAAK,WACH,cACG7B,EACC,CAAAgC,SAAA,QAAC/B,EACC,CAAA+B,SAAA,CAACD,EAAAA,IAAA,OAAA,CAAKE,MAAO,CAAEC,SAAU,OAAQC,WAAY,OAASH,SAAgB,4BACrE9B,EACC,CAAA8B,SAAA,GAACD,IAAA5B,EAAA,CAAOE,SAAO,UACdF,EAAO,UACPA,EAAO,iBAGXG,EACC,CAAA0B,SAAA,QAACzB,EACC,CAAAyB,SAAA,CAAAD,EAAAA,IAACvB,EAAY,CAAAwB,SAAAtC,EAAY,GAAGE,eAC5BmC,IAACrB,GAAOsB,SAAK,aAEdD,EAAAA,IAAApB,EAAA,CAAYqB,SAAYtC,EAAA,GAAGI,aAKpC,IAAK,QACH,cACGe,EACC,CAAAmB,SAAA,QAAClB,EACC,CAAAkB,SAAA,GAAAD,IAAC,QAAKC,SAAC,QACPD,IAAC,QAAKC,SAAG,aAEXD,IAAChB,GAAUiB,SAAoB,4BAIrC,IAAK,OACH,cACGhB,EACC,CAAAgB,SAAA,CAACD,EAAAA,IAAAd,EAAA,CACCe,SAACD,EAAAA,IAAA,MAAA,CAAIE,MAAO,CAAEC,SAAU,OAAQC,WAAY,OAASH,SAAA,4BAEvDD,IAACb,GAAYc,SAEb,0EACCD,IAAAZ,EAAA,CACEa,SAAYtC,EAAA0C,MAAM,EAAG,GAAGC,IAAI,CAACC,EAAQC,MACpCC,KAACpB,EACC,CAAAY,SAAA,CAACD,EAAAA,IAAA,MAAA,CAAIE,MAAO,CAAEE,WAAY,MAAOM,aAAc,OAAUT,SAAAM,EAAO1C,aAChEmC,EAAAA,IAAC,OAAIE,MAAO,CAAES,MAAO,UAAWR,SAAU,OAASF,SAAK,iBACvD,MAAI,CAAAC,MAAO,CAAEU,UAAW,OAAUX,SAAA,CAAOM,EAAAxC,KAAK8C,UAAU,EAAG,IAAI,WAH7CL,SAU/B,IAAK,kBACH,cACGlB,EACC,CAAAW,SAAA,GAAAD,IAAC,MAAI,CAAAE,MAAO,CAAEC,SAAU,OAAQC,WAAY,MAAOM,aAAc,OAC9DT,SAAYtC,EAAA,GAAGE,aAElBmC,EAAAA,IAAC,MAAI,CAAAE,MAAO,CAAES,MAAO,UAAWR,SAAU,OAAQO,aAAc,OAAST,SAAK,UAC9EQ,OAAC,OAAIP,MAAO,CAAEC,SAAU,MAAOQ,MAAO,QAAUV,SAAA,CAAA,IAC5CtC,EAAY,GAAGI,KAAK,cAEvBwB,EACC,CAAAU,SAAA,GAACD,IAAA5B,EAAA,CAAOE,SAAO,UACdF,EAAO,UACPA,EAAO,UAKhB,IAAK,SACH,cACGoB,EACC,CAAAS,SAAA,GAAAD,IAAC,MAAI,CAAAE,MAAO,CAAEC,SAAU,OAAQC,WAAY,MAAOM,aAAc,MAAOI,UAAW,UAAYb,SAE/F,mBACAD,EAAAA,IAACP,GACEQ,SAAYtC,EAAA2C,IAAI,CAACC,EAAQC,WACvBd,EACC,CAAAO,SAAA,CAACD,EAAAA,IAAA,MAAA,CAAIE,MAAO,CAAEE,WAAY,MAAOM,aAAc,OAAUT,SAAAM,EAAO1C,aAChEmC,EAAAA,IAAC,OAAIE,MAAO,CAAES,MAAO,UAAWR,SAAU,OAASF,SAAK,YAFzCO,SAS3B,IAAK,iBACH,cACGb,EACC,CAAAM,SAAA,OAAC,OAAIC,MAAO,CACVa,SAAU,WACVC,IAAK,MACLC,KAAM,MACNd,SAAU,MACVQ,MAAO,OACPO,WAAY,wBACZC,QAAS,UACTC,aAAc,OACbnB,SAEH,wBACCL,EACC,CAAAK,SAAA,CAACD,EAAAA,IAAA,MAAA,CAAIE,MAAO,CAAES,MAAO,UAAWP,WAAY,OAASH,SAAK,gBACzD,MAAI,CAAAC,MAAO,CAAES,MAAO,QAAUV,SAAG,cAK1C,QACS,SAAAD,IAAC,OAAIC,SAAqB,4BAMlCoB,IAGP,CC9VA,MAAMC,EAAY7D,EAAOC,GAAA;;;;EAMnB6D,EAAQ9D,EAAO+D,EAAA;;;;;;EAQfC,EAAchE,EAAOoB,CAAA;;;;;;EAQrB6C,EAAgBjE,EAAOC,GAAA;;;;;;;;;EAWvBiE,EAAelE,EAAOC,GAAA;;sBAEGW,GAAAA,EAAMuD,YAAc,UAAY;;;;;;;;oBAQlCvD,GAAAA,EAAMuD,YAAc,UAAY;;;;IAIzDvD,GAASA,EAAMuD,aAAe;EAK5BC,EAAgBpE,EAAOC,GAAA;;;;;;;;;;EAYvBoE,EAAerE,EAAOsE,EAAA;;;;;EAOtBC,EAAsBvE,EAAOoB,CAAA;;;;;EAO7BoD,EAAmBxE,EAAOC,GAAA;;;;;;;;EAW1BwE,EAID,CACH,CACEC,KAAM,WACNC,KAAM,kBACNC,YAAa,uFAEf,CACEF,KAAM,QACNC,KAAM,QACNC,YAAa,2FAEf,CACEF,KAAM,OACNC,KAAM,uBACNC,YAAa,wFAEf,CACEF,KAAM,kBACNC,KAAM,kBACNC,YAAa,mFAEf,CACEF,KAAM,SACNC,KAAM,SACNC,YAAa,2EAEf,CACEF,KAAM,iBACNC,KAAM,iBACNC,YAAa,6FAIV,SAASC,IACd,MAAMC,OAAEA,EAAAC,aAAQA,GAAiBC,IAMjC,cACGnB,EACC,CAAArB,SAAA,GAAAD,IAACuB,GAAMtB,SAAoB,2BAC3BD,IAACyB,GAAYxB,SAEb,0GAECD,EAAAA,IAAA0B,EAAA,CACEzB,SAAUiC,EAAA5B,IAAKoC,GACdC,EAAAlC,KAACkB,EAAA,CAECC,YAAaW,EAAOG,WAAaA,EAASP,KAC1CS,QAAS,KAAMC,OAhBK/C,EAgBgB4C,EAASP,UAfxCK,EAAA,CAAEE,SAAU5C,IADE,IAACA,GAkBnBG,SAAA,CAAAsC,EAAOG,WAAaA,EAASP,MAC5BnC,EAAAA,IAAC6B,GAAc5B,SAAQ,eAGzBD,IAAC8B,EAAc,CAAA7B,SAAAyC,EAASN,SACxBpC,IAACgC,EAAqB,CAAA/B,SAAAyC,EAASL,oBAE9BJ,EACC,CAAAhC,SAAA0C,EAAA3C,IAACH,EAAA,CACCC,aAAc4C,EAASP,KACvBpC,WAAYwC,EAAOG,WAAaA,EAASP,WAdxCO,EAASP,WAsB1B"}