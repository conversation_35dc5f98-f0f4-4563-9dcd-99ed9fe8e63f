{"name": "@grwg/frontend", "version": "1.0.0", "description": "React frontend for Google Reviews Widget Generator", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "build:analyze": "ANALYZE=true npm run build", "preview": "vite preview --host 0.0.0.0 --port ${PORT:-3000}", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:unit": "vitest run --reporter=verbose", "test:integration": "vitest run --config vitest.integration.config.ts", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:accessibility": "playwright test --config playwright.config.ts", "test:performance": "vitest run --reporter=verbose src/test/performance.test.tsx", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e && npm run test:accessibility", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "bundle-size": "npm run build && du -sh dist/", "performance": "npm run test:performance && npm run bundle-size"}, "dependencies": {"@hookform/resolvers": "^3.1.0", "@tanstack/react-query": "^4.29.0", "axios": "^1.4.0", "lucide-react": "^0.263.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-router-dom": "^6.14.0", "styled-components": "^6.0.0", "zod": "^3.21.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/styled-components": "^5.1.26", "@vercel/node": "^5.3.6", "@vitejs/plugin-react": "^4.0.0", "jsdom": "^22.1.0", "typescript": "^5.8.3", "vite": "^4.4.0", "vitest": "^0.33.0", "cypress": "^13.6.0", "@cypress/vite-dev-server": "^5.0.0", "@axe-core/playwright": "^4.8.0", "playwright": "^1.40.0", "@playwright/test": "^1.40.0", "axe-core": "^4.8.0", "@axe-core/react": "^4.8.0", "msw": "^2.0.0"}}