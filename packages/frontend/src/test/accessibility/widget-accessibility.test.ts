import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Widget Generator Accessibility', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/business/search', async route => {
      await route.fulfill({
        json: {
          businesses: [
            {
              id: '550e8400-e29b-41d4-a716-446655440000',
              placeId: 'ChIJ123456789',
              name: 'Test Business',
              address: '123 Test St, Test City, TC 12345',
              rating: 4.5,
              reviewCount: 100,
              photoUrl: 'https://example.com/photo.jpg',
            },
          ],
          totalResults: 1,
        },
      });
    });

    await page.route('**/api/reviews/*', async route => {
      await route.fulfill({
        json: {
          reviews: [
            {
              id: 'review-1',
              authorName: '<PERSON>',
              authorPhotoUrl: 'https://example.com/john.jpg',
              rating: 5,
              text: 'Great service!',
              publishedDate: '2023-01-01T00:00:00.000Z',
              isVerified: true,
            },
          ],
          businessInfo: {
            name: 'Test Business',
            rating: 4.5,
            reviewCount: 100,
          },
          totalReviews: 100,
          averageRating: 4.5,
          lastUpdated: '2023-01-01T00:00:00.000Z',
        },
      });
    });

    await page.goto('/');
  });

  test('should not have any automatically detectable accessibility issues on home page', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    // Check that headings follow proper hierarchy (h1 -> h2 -> h3, etc.)
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    
    let previousLevel = 0;
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const currentLevel = parseInt(tagName.charAt(1));
      
      // Heading levels should not skip (e.g., h1 -> h3 is not allowed)
      expect(currentLevel).toBeLessThanOrEqual(previousLevel + 1);
      previousLevel = currentLevel;
    }
  });

  test('should have proper form labels and ARIA attributes', async ({ page }) => {
    // Check that all form inputs have associated labels
    const inputs = await page.locator('input, select, textarea').all();
    
    for (const input of inputs) {
      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledBy = await input.getAttribute('aria-labelledby');
      
      if (id) {
        // Check if there's a label with for attribute
        const label = await page.locator(`label[for="${id}"]`).count();
        expect(label > 0 || ariaLabel || ariaLabelledBy).toBeTruthy();
      } else {
        // Input should have aria-label or aria-labelledby
        expect(ariaLabel || ariaLabelledBy).toBeTruthy();
      }
    }
  });

  test('should support keyboard navigation', async ({ page }) => {
    // Test tab navigation through interactive elements
    await page.keyboard.press('Tab');
    let focusedElement = await page.locator(':focus').first();
    expect(await focusedElement.isVisible()).toBeTruthy();
    
    // Continue tabbing and ensure focus is visible
    for (let i = 0; i < 10; i++) {
      await page.keyboard.press('Tab');
      focusedElement = await page.locator(':focus').first();
      
      if (await focusedElement.count() > 0) {
        expect(await focusedElement.isVisible()).toBeTruthy();
      }
    }
  });

  test('should have sufficient color contrast', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .include('[data-testid]') // Focus on our components
      .analyze();

    const colorContrastViolations = accessibilityScanResults.violations.filter(
      violation => violation.id === 'color-contrast'
    );

    expect(colorContrastViolations).toEqual([]);
  });

  test('should have proper ARIA roles and states', async ({ page }) => {
    // Fill out the business search to trigger dynamic content
    await page.fill('[data-testid="business-search-input"]', 'Test Business');
    await page.click('[data-testid="search-button"]');
    
    // Wait for results to load
    await page.waitForSelector('[data-testid="business-result"]');
    
    // Check for proper ARIA attributes on interactive elements
    const buttons = await page.locator('button').all();
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const textContent = await button.textContent();
      
      // Button should have either text content or aria-label
      expect(ariaLabel || (textContent && textContent.trim().length > 0)).toBeTruthy();
    }
    
    // Check for proper ARIA states on toggles/checkboxes
    const checkboxes = await page.locator('[role="checkbox"], input[type="checkbox"]').all();
    for (const checkbox of checkboxes) {
      const ariaChecked = await checkbox.getAttribute('aria-checked');
      const checked = await checkbox.getAttribute('checked');
      
      // Should have aria-checked or checked attribute
      expect(ariaChecked !== null || checked !== null).toBeTruthy();
    }
  });

  test('should announce dynamic content changes to screen readers', async ({ page }) => {
    // Check for ARIA live regions
    const liveRegions = await page.locator('[aria-live]').count();
    expect(liveRegions).toBeGreaterThan(0);
    
    // Test that status messages are announced
    await page.fill('[data-testid="business-search-input"]', 'Test Business');
    await page.click('[data-testid="search-button"]');
    
    // Check for status or alert messages
    const statusMessages = await page.locator('[role="status"], [role="alert"], [aria-live]').count();
    expect(statusMessages).toBeGreaterThan(0);
  });

  test('should be accessible on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Run accessibility scan on mobile
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Check that touch targets are large enough (minimum 44x44px)
    const touchTargets = await page.locator('button, a, input, [role="button"]').all();
    
    for (const target of touchTargets) {
      if (await target.isVisible()) {
        const box = await target.boundingBox();
        if (box) {
          expect(box.width).toBeGreaterThanOrEqual(44);
          expect(box.height).toBeGreaterThanOrEqual(44);
        }
      }
    }
  });

  test('should handle focus management in modal dialogs', async ({ page }) => {
    // This test would be relevant if we have modal dialogs
    // For now, we'll check that focus is properly managed in step navigation
    
    await page.click('[data-testid="step-layout"]');
    
    // Check that focus moves to the active step content
    const activeStep = await page.locator('[data-testid="step-content"][aria-hidden="false"]');
    expect(await activeStep.count()).toBe(1);
    
    // Check that inactive steps are properly hidden from screen readers
    const hiddenSteps = await page.locator('[data-testid="step-content"][aria-hidden="true"]');
    expect(await hiddenSteps.count()).toBeGreaterThan(0);
  });

  test('should provide clear error messages', async ({ page }) => {
    // Test form validation error messages
    await page.click('[data-testid="search-button"]'); // Submit without input
    
    // Check for error message
    const errorMessage = await page.locator('[role="alert"], [aria-live="assertive"]').first();
    expect(await errorMessage.count()).toBeGreaterThan(0);
    
    // Error message should be descriptive
    const errorText = await errorMessage.textContent();
    expect(errorText).toBeTruthy();
    expect(errorText!.length).toBeGreaterThan(10); // Should be descriptive
  });

  test('should support high contrast mode', async ({ page }) => {
    // Simulate high contrast mode by checking if elements are still visible
    // and have proper contrast when forced colors are active
    
    await page.emulateMedia({ forcedColors: 'active' });
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .analyze();

    // Should not have color contrast violations in high contrast mode
    const colorContrastViolations = accessibilityScanResults.violations.filter(
      violation => violation.id === 'color-contrast'
    );

    expect(colorContrastViolations).toEqual([]);
  });
});