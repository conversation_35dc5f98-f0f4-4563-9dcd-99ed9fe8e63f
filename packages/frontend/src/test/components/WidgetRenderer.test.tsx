import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, beforeEach } from 'vitest';
import { WidgetRenderer } from '../../components/Preview/WidgetRenderer';
import { WidgetConfig } from '../../types/widget';

const mockBusiness = {
  placeId: 'test-place-id',
  name: 'Test Business',
  address: '123 Test St',
  rating: 4.5,
  reviewCount: 100,
};

const defaultConfig: WidgetConfig = {
  business: mockBusiness,
  template: 'carousel',
  styling: {
    colors: {
      primary: '#4285f4',
      secondary: '#34a853',
      background: '#ffffff',
      text: '#333333',
      border: '#e0e0e0',
    },
    fonts: {
      family: 'Inter, sans-serif',
      size: '14px',
      weight: '400',
    },
    dimensions: {
      width: '400px',
      height: '300px',
      borderRadius: '8px',
    },
    spacing: {
      padding: '16px',
      margin: '0px',
      gap: '12px',
    },
  },
  settings: {
    maxReviews: 5,
    minRating: 1,
    sortBy: 'newest',
    showPhotos: true,
    showDates: true,
    autoRefresh: true,
  },
};

describe('WidgetRenderer', () => {
  beforeEach(() => {
    // Reset any mocks or state before each test
  });

  describe('Template Rendering', () => {
    it('renders carousel template correctly', () => {
      render(<WidgetRenderer config={defaultConfig} viewport="desktop" />);
      
      expect(screen.getByText('Test Business')).toBeInTheDocument();
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText(/Excellent service and friendly staff/)).toBeInTheDocument();
    });

    it('renders badge template correctly', () => {
      const badgeConfig = { ...defaultConfig, template: 'badge' as const };
      render(<WidgetRenderer config={badgeConfig} viewport="desktop" />);
      
      expect(screen.getByText(/Based on \d+ reviews/)).toBeInTheDocument();
      expect(screen.getByText(/★/)).toBeInTheDocument();
    });

    it('renders grid template correctly', () => {
      const gridConfig = { ...defaultConfig, template: 'grid' as const };
      render(<WidgetRenderer config={gridConfig} viewport="desktop" />);
      
      expect(screen.getByText(/AI Summary:/)).toBeInTheDocument();
      expect(screen.getByText('Test Business')).toBeInTheDocument();
    });

    it('renders simple-carousel template correctly', () => {
      const simpleConfig = { ...defaultConfig, template: 'simple-carousel' as const };
      render(<WidgetRenderer config={simpleConfig} viewport="desktop" />);
      
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText(/Excellent service and friendly staff/)).toBeInTheDocument();
    });

    it('renders slider template correctly', () => {
      const sliderConfig = { ...defaultConfig, template: 'slider' as const };
      render(<WidgetRenderer config={sliderConfig} viewport="desktop" />);
      
      expect(screen.getByText('Recent Reviews')).toBeInTheDocument();
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
    });

    it('renders floating-badge template correctly', () => {
      const floatingConfig = { ...defaultConfig, template: 'floating-badge' as const };
      render(<WidgetRenderer config={floatingConfig} viewport="desktop" />);
      
      expect(screen.getByText('Your Website Content')).toBeInTheDocument();
      expect(screen.getByText(/\d+ reviews/)).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('adapts layout for mobile viewport', () => {
      render(<WidgetRenderer config={defaultConfig} viewport="mobile" />);
      
      const container = screen.getByText('Test Business').closest('[data-viewport]') || 
                       screen.getByText('Test Business').parentElement;
      
      // Should have mobile-specific styling
      expect(container).toBeInTheDocument();
    });

    it('adapts layout for tablet viewport', () => {
      render(<WidgetRenderer config={defaultConfig} viewport="tablet" />);
      
      const container = screen.getByText('Test Business').closest('[data-viewport]') || 
                       screen.getByText('Test Business').parentElement;
      
      // Should have tablet-specific styling
      expect(container).toBeInTheDocument();
    });

    it('shows different number of reviews based on viewport', () => {
      const gridConfig = { ...defaultConfig, template: 'grid' as const };
      
      // Desktop should show more reviews
      const { rerender } = render(<WidgetRenderer config={gridConfig} viewport="desktop" />);
      const desktopReviews = screen.getAllByText(/★★★★★|★★★★☆/);
      
      // Mobile should show fewer reviews
      rerender(<WidgetRenderer config={gridConfig} viewport="mobile" />);
      const mobileReviews = screen.getAllByText(/★★★★★|★★★★☆/);
      
      expect(desktopReviews.length).toBeGreaterThanOrEqual(mobileReviews.length);
    });
  });

  describe('Configuration Settings', () => {
    it('respects maxReviews setting', () => {
      const limitedConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, maxReviews: 2 }
      };
      
      render(<WidgetRenderer config={limitedConfig} viewport="desktop" />);
      
      // Should only show navigation dots for 2 reviews
      const navDots = screen.getAllByRole('button').filter(btn => 
        btn.getAttribute('style')?.includes('border-radius: 50%')
      );
      expect(navDots.length).toBeLessThanOrEqual(2);
    });

    it('respects minRating filter', () => {
      const highRatingConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, minRating: 5 }
      };
      
      render(<WidgetRenderer config={highRatingConfig} viewport="desktop" />);
      
      // Should only show 5-star reviews
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument(); // 5-star review
    });

    it('hides photos when showPhotos is false', () => {
      const noPhotosConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, showPhotos: false }
      };
      
      render(<WidgetRenderer config={noPhotosConfig} viewport="desktop" />);
      
      // Should not show author photos
      const images = screen.queryAllByRole('img');
      expect(images).toHaveLength(0);
    });

    it('hides dates when showDates is false', () => {
      const noDatesConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, showDates: false }
      };
      
      render(<WidgetRenderer config={noDatesConfig} viewport="desktop" />);
      
      // Should not show review dates
      expect(screen.queryByText('2 days ago')).not.toBeInTheDocument();
    });

    it('sorts reviews according to sortBy setting', () => {
      const ratingHighConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, sortBy: 'rating_high' as const }
      };
      
      render(<WidgetRenderer config={ratingHighConfig} viewport="desktop" />);
      
      // Should show highest rated review first (Sarah Johnson has 5 stars)
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
    });
  });

  describe('Styling Configuration', () => {
    it('applies custom colors', () => {
      const customConfig = {
        ...defaultConfig,
        styling: {
          ...defaultConfig.styling,
          colors: {
            ...defaultConfig.styling.colors,
            primary: '#ff0000',
            background: '#f0f0f0',
            text: '#000000',
          }
        }
      };
      
      render(<WidgetRenderer config={customConfig} viewport="desktop" />);
      
      const container = screen.getByText('Test Business').closest('div');
      expect(container).toHaveStyle({ color: '#000000' });
    });

    it('applies custom dimensions', () => {
      const customConfig = {
        ...defaultConfig,
        styling: {
          ...defaultConfig.styling,
          dimensions: {
            width: '500px',
            height: '400px',
            borderRadius: '12px',
          }
        }
      };
      
      render(<WidgetRenderer config={customConfig} viewport="desktop" />);
      
      // Check that the widget renders with custom config
      expect(screen.getByText('Test Business')).toBeInTheDocument();
      
      // Check that the container exists (styled-components styles may not be testable in jsdom)
      const container = screen.getByText('Test Business').closest('div');
      expect(container).toBeInTheDocument();
    });

    it('applies custom fonts', () => {
      const customConfig = {
        ...defaultConfig,
        styling: {
          ...defaultConfig.styling,
          fonts: {
            family: 'Arial, sans-serif',
            size: '16px',
            weight: '600',
          }
        }
      };
      
      render(<WidgetRenderer config={customConfig} viewport="desktop" />);
      
      // Check that the widget renders with custom config
      expect(screen.getByText('Test Business')).toBeInTheDocument();
      
      // Check that the container exists (styled-components styles may not be testable in jsdom)
      const container = screen.getByText('Test Business').closest('div');
      expect(container).toBeInTheDocument();
    });
  });

  describe('Interactive Elements', () => {
    it('allows navigation between reviews in carousel', () => {
      render(<WidgetRenderer config={defaultConfig} viewport="desktop" />);
      
      // Should show first review initially
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      
      // Click navigation dots to change reviews
      const navDots = screen.getAllByRole('button').filter(btn => 
        btn.getAttribute('style')?.includes('border-radius: 50%')
      );
      
      if (navDots.length > 1) {
        fireEvent.click(navDots[1]);
        // Should show different review content
        expect(screen.getByText(/Mike Chen|Emma Davis|James Wilson|Lisa Rodriguez/)).toBeInTheDocument();
      }
    });

    it('handles navigation in simple-carousel template', () => {
      const simpleConfig = { ...defaultConfig, template: 'simple-carousel' as const };
      render(<WidgetRenderer config={simpleConfig} viewport="desktop" />);
      
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      
      const navDots = screen.getAllByRole('button').filter(btn => 
        btn.getAttribute('style')?.includes('border-radius: 50%')
      );
      
      if (navDots.length > 1) {
        fireEvent.click(navDots[1]);
        // Should show different review
        expect(screen.getByText(/Mike Chen|Emma Davis|James Wilson|Lisa Rodriguez/)).toBeInTheDocument();
      }
    });
  });

  describe('Edge Cases', () => {
    it('handles missing business data gracefully', () => {
      const noBusiness = { ...defaultConfig, business: undefined };
      render(<WidgetRenderer config={noBusiness} viewport="desktop" />);
      
      // Should show fallback text
      expect(screen.getByText('Customer Reviews')).toBeInTheDocument();
    });

    it('handles empty reviews gracefully', () => {
      const noReviewsConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, minRating: 6 } // No reviews will match
      };
      
      render(<WidgetRenderer config={noReviewsConfig} viewport="desktop" />);
      
      // Should handle empty state gracefully
      expect(screen.getByText('Test Business')).toBeInTheDocument();
    });

    it('handles unknown template type', () => {
      const unknownConfig = { ...defaultConfig, template: 'unknown' as any };
      render(<WidgetRenderer config={unknownConfig} viewport="desktop" />);
      
      expect(screen.getByText('Template not found')).toBeInTheDocument();
    });

    it('truncates long review text appropriately', () => {
      const gridConfig = { ...defaultConfig, template: 'grid' as const };
      render(<WidgetRenderer config={gridConfig} viewport="desktop" />);
      
      // Long review text should be truncated in grid view
      const reviewTexts = screen.getAllByText(/\.\.\./);
      expect(reviewTexts.length).toBeGreaterThan(0);
    });
  });

  describe('Performance', () => {
    it('renders efficiently with many reviews', () => {
      const manyReviewsConfig = {
        ...defaultConfig,
        settings: { ...defaultConfig.settings, maxReviews: 10 }
      };
      
      const startTime = performance.now();
      render(<WidgetRenderer config={manyReviewsConfig} viewport="desktop" />);
      const endTime = performance.now();
      
      // Should render quickly (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('handles rapid viewport changes efficiently', () => {
      const { rerender } = render(<WidgetRenderer config={defaultConfig} viewport="desktop" />);
      
      const startTime = performance.now();
      rerender(<WidgetRenderer config={defaultConfig} viewport="mobile" />);
      rerender(<WidgetRenderer config={defaultConfig} viewport="tablet" />);
      rerender(<WidgetRenderer config={defaultConfig} viewport="desktop" />);
      const endTime = performance.now();
      
      // Should handle rapid changes efficiently
      expect(endTime - startTime).toBeLessThan(50);
    });
  });
});