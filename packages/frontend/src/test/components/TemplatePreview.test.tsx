import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { TemplatePreview } from '../../components/TemplateSelection/TemplatePreview';
import { TemplateType } from '../../types/widget';

describe('TemplatePreview', () => {
  const templateTypes: TemplateType[] = [
    'carousel',
    'badge', 
    'grid',
    'simple-carousel',
    'slider',
    'floating-badge'
  ];

  it.each(templateTypes)('renders preview for %s template', (templateType) => {
    render(<TemplatePreview templateType={templateType} isSelected={false} />);
    
    // Each template should render some content
    const previewWrapper = document.querySelector('[data-testid="preview-wrapper"]') || 
                          document.querySelector('div');
    expect(previewWrapper).toBeInTheDocument();
  });

  describe('Carousel Template Preview', () => {
    it('renders carousel preview with navigation dots', () => {
      render(<TemplatePreview templateType="carousel" isSelected={true} />);
      
      expect(screen.getByText('Customer Reviews')).toBeInTheDocument();
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('Excellent service and friendly staff. Highly recommend!')).toBeInTheDocument();
    });
  });

  describe('Badge Template Preview', () => {
    it('renders badge preview with rating and review count', () => {
      render(<TemplatePreview templateType="badge" isSelected={true} />);
      
      expect(screen.getByText('4.8')).toBeInTheDocument();
      expect(screen.getByText('Based on 127 reviews')).toBeInTheDocument();
      expect(screen.getByText('★')).toBeInTheDocument();
    });
  });

  describe('Grid Template Preview', () => {
    it('renders grid preview with AI summary and review cards', () => {
      render(<TemplatePreview templateType="grid" isSelected={true} />);
      
      expect(screen.getByText('★ 4.8 (127 reviews)')).toBeInTheDocument();
      expect(screen.getByText(/AI Summary: Customers love the excellent service/)).toBeInTheDocument();
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('Mike Chen')).toBeInTheDocument();
    });
  });

  describe('Simple Carousel Template Preview', () => {
    it('renders simple carousel preview with author and review', () => {
      render(<TemplatePreview templateType="simple-carousel" isSelected={true} />);
      
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('"Excellent service and friendly staff. Highly recommend!"')).toBeInTheDocument();
    });
  });

  describe('Slider Template Preview', () => {
    it('renders slider preview with multiple review cards', () => {
      render(<TemplatePreview templateType="slider" isSelected={true} />);
      
      expect(screen.getByText('Recent Reviews')).toBeInTheDocument();
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('Mike Chen')).toBeInTheDocument();
      expect(screen.getByText('Emma Davis')).toBeInTheDocument();
    });
  });

  describe('Floating Badge Template Preview', () => {
    it('renders floating badge preview with website context', () => {
      render(<TemplatePreview templateType="floating-badge" isSelected={true} />);
      
      expect(screen.getByText('Your Website')).toBeInTheDocument();
      expect(screen.getByText('★ 4.8')).toBeInTheDocument();
      expect(screen.getByText('127')).toBeInTheDocument();
    });
  });

  it('handles unknown template type gracefully', () => {
    // @ts-expect-error Testing invalid template type
    render(<TemplatePreview templateType="unknown" isSelected={false} />);
    
    expect(screen.getByText('Preview not available')).toBeInTheDocument();
  });

  it('renders consistently regardless of isSelected prop', () => {
    const { rerender } = render(<TemplatePreview templateType="badge" isSelected={false} />);
    
    expect(screen.getByText('4.8')).toBeInTheDocument();
    
    rerender(<TemplatePreview templateType="badge" isSelected={true} />);
    
    expect(screen.getByText('4.8')).toBeInTheDocument();
  });

  it('uses mock review data consistently', () => {
    render(<TemplatePreview templateType="carousel" isSelected={true} />);
    
    // Verify mock data is used
    expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
    expect(screen.getByText('Excellent service and friendly staff. Highly recommend!')).toBeInTheDocument();
  });

  it('renders star ratings in multiple templates', () => {
    const templatesWithStars: TemplateType[] = ['carousel', 'badge', 'grid', 'simple-carousel', 'slider', 'floating-badge'];
    
    templatesWithStars.forEach((templateType) => {
      const { unmount } = render(<TemplatePreview templateType={templateType} isSelected={true} />);
      
      // Each template should have some form of star rating
      const starElements = document.querySelectorAll('*');
      const hasStars = Array.from(starElements).some(el => 
        el.textContent?.includes('★') || el.textContent?.includes('4.8')
      );
      
      expect(hasStars).toBe(true);
      unmount();
    });
  });

  it('maintains proper styling structure', () => {
    render(<TemplatePreview templateType="carousel" isSelected={true} />);
    
    // Verify the wrapper structure exists
    const wrapper = document.querySelector('div');
    expect(wrapper).toBeInTheDocument();
    // The styled component should have the proper structure, but we can't easily test exact styles
    // in this test environment, so we'll just verify it renders
  });
});