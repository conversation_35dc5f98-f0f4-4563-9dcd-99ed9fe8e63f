import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { Layout } from '../../components/Layout/Layout';
import { WidgetProvider } from '../../context/WidgetContext';
import { vi } from 'vitest';

// Mock all styled-components
vi.mock('styled-components', () => {
  const styled = (tag: any) => (styles: any) => {
    return React.forwardRef((props: any, ref: any) => {
      return React.createElement(tag, { ...props, ref, 'data-styled': true });
    });
  };
  
  // Add properties for all HTML tags
  styled.div = styled('div');
  styled.nav = styled('nav');
  styled.header = styled('header');
  styled.main = styled('main');
  styled.h1 = styled('h1');
  styled.h2 = styled('h2');
  styled.p = styled('p');
  styled.button = styled('button');
  
  return { default: styled };
});

const renderWithProviders = (initialEntries = ['/']) => {
  return render(
    <WidgetProvider>
      <MemoryRouter initialEntries={initialEntries}>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<div>Source Page Content</div>} />
            <Route path="layout" element={<div>Layout Page Content</div>} />
          </Route>
        </Routes>
      </MemoryRouter>
    </WidgetProvider>
  );
};

describe('Layout', () => {
  it('renders header component', () => {
    renderWithProviders();
    expect(screen.getByText('Google Reviews Widget Generator')).toBeInTheDocument();
  });

  it('renders step navigation', () => {
    renderWithProviders();
    expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByText('Layout')).toBeInTheDocument();
    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('Reviews')).toBeInTheDocument();
    expect(screen.getByText('Style')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('renders outlet content', () => {
    renderWithProviders(['/']);
    // The outlet should render our test content
    expect(screen.getByText('Source Page Content')).toBeInTheDocument();
  });
});