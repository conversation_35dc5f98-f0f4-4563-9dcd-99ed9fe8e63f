import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PreviewComponent } from '../../components/Preview/PreviewComponent';
import { WidgetProvider } from '../../context/WidgetContext';
import { WidgetConfig } from '../../types/widget';

// Mock the WidgetRenderer component
vi.mock('../../components/Preview/WidgetRenderer', () => ({
  WidgetRenderer: ({ config, viewport }: { config: WidgetConfig; viewport: string }) => (
    <div data-testid="widget-renderer" data-viewport={viewport} data-template={config.template}>
      Mock Widget Renderer - {config.template} - {viewport}
    </div>
  ),
}));

const mockBusiness = {
  placeId: 'test-place-id',
  name: 'Test Business',
  address: '123 Test St',
  rating: 4.5,
  reviewCount: 100,
};

// Mock the useWidget hook to provide test data
const mockUseWidget = vi.fn();

vi.mock('../../context/WidgetContext', () => ({
  useWidget: () => mockUseWidget(),
  WidgetProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

const defaultMockConfig = {
  business: mockBusiness,
  template: 'carousel' as const,
  styling: {
    colors: {
      primary: '#4285f4',
      secondary: '#34a853',
      background: '#ffffff',
      text: '#333333',
      border: '#e0e0e0',
    },
    fonts: {
      family: 'Inter, sans-serif',
      size: '14px',
      weight: '400',
    },
    dimensions: {
      width: '400px',
      height: '300px',
      borderRadius: '8px',
    },
    spacing: {
      padding: '16px',
      margin: '0px',
      gap: '12px',
    },
  },
  settings: {
    maxReviews: 5,
    minRating: 1,
    sortBy: 'newest' as const,
    showPhotos: true,
    showDates: true,
    autoRefresh: true,
  },
};

const renderWithProvider = (component: React.ReactElement, config?: Partial<WidgetConfig>) => {
  mockUseWidget.mockReturnValue({
    config: { ...defaultMockConfig, ...config },
    updateConfig: vi.fn(),
    resetConfig: vi.fn(),
    currentStep: 0,
    setCurrentStep: vi.fn(),
  });

  return render(component);
};

describe('PreviewComponent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders preview component with default controls', () => {
      renderWithProvider(<PreviewComponent />);
      
      expect(screen.getByText('Live Preview')).toBeInTheDocument();
      expect(screen.getByText('Desktop')).toBeInTheDocument();
      expect(screen.getByText('Tablet')).toBeInTheDocument();
      expect(screen.getByText('Mobile')).toBeInTheDocument();
    });

    it('renders without controls when showControls is false', () => {
      renderWithProvider(<PreviewComponent showControls={false} />);
      
      expect(screen.queryByText('Live Preview')).not.toBeInTheDocument();
      expect(screen.queryByText('Desktop')).not.toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { container } = renderWithProvider(<PreviewComponent className="custom-class" />);
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Viewport Controls', () => {
    it('starts with desktop viewport by default', () => {
      renderWithProvider(<PreviewComponent />);
      
      const desktopButton = screen.getByText('Desktop');
      expect(desktopButton).toHaveStyle({ background: '#4285f4' });
    });

    it('starts with specified default viewport', () => {
      renderWithProvider(<PreviewComponent defaultViewport="mobile" />);
      
      const mobileButton = screen.getByText('Mobile');
      expect(mobileButton).toHaveStyle({ background: '#4285f4' });
    });

    it('switches viewport when buttons are clicked', async () => {
      renderWithProvider(<PreviewComponent />);
      
      const tabletButton = screen.getByText('Tablet');
      fireEvent.click(tabletButton);
      
      await waitFor(() => {
        expect(tabletButton).toHaveStyle({ background: '#4285f4' });
      });
    });

    it('updates widget renderer viewport when viewport changes', async () => {
      renderWithProvider(<PreviewComponent />);
      
      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      }, { timeout: 500 });
      
      // Initially should be desktop
      expect(screen.getByTestId('widget-renderer')).toHaveAttribute('data-viewport', 'desktop');
      
      // Switch to mobile
      const mobileButton = screen.getByText('Mobile');
      fireEvent.click(mobileButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('widget-renderer')).toHaveAttribute('data-viewport', 'mobile');
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading state initially', async () => {
      renderWithProvider(<PreviewComponent />);
      
      expect(screen.getByText('Updating preview...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      }, { timeout: 500 });
    });

    it('shows message when no business is selected', async () => {
      renderWithProvider(<PreviewComponent />, { business: undefined });
      
      await waitFor(() => {
        expect(screen.getByText('Select a business to see the widget preview')).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Behavior', () => {
    it('renders different viewport frames correctly', async () => {
      renderWithProvider(<PreviewComponent />);
      
      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      }, { timeout: 500 });
      
      // Test desktop frame
      expect(screen.getByTestId('widget-renderer')).toHaveAttribute('data-viewport', 'desktop');
      
      // Test tablet frame
      const tabletButton = screen.getByText('Tablet');
      fireEvent.click(tabletButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('widget-renderer')).toHaveAttribute('data-viewport', 'tablet');
      });
      
      // Test mobile frame
      const mobileButton = screen.getByText('Mobile');
      fireEvent.click(mobileButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('widget-renderer')).toHaveAttribute('data-viewport', 'mobile');
      });
    });

    it('maintains viewport selection across re-renders', async () => {
      const { rerender } = renderWithProvider(<PreviewComponent />);
      
      // Switch to mobile
      const mobileButton = screen.getByText('Mobile');
      fireEvent.click(mobileButton);
      
      await waitFor(() => {
        expect(mobileButton).toHaveStyle({ background: '#4285f4' });
      });
      
      // Re-render component
      rerender(<PreviewComponent />);
      
      // Mobile should still be selected
      expect(screen.getByText('Mobile')).toHaveStyle({ background: '#4285f4' });
    });
  });

  describe('Real-time Updates', () => {
    it('shows loading state when config changes', async () => {
      let configUpdateFn: any;
      
      mockUseWidget.mockImplementation(() => {
        const [config, setConfig] = React.useState(defaultMockConfig);
        configUpdateFn = setConfig;
        return {
          config,
          updateConfig: (updates: Partial<WidgetConfig>) => {
            setConfig((prev: any) => ({ ...prev, ...updates }));
          },
          resetConfig: vi.fn(),
          currentStep: 0,
          setCurrentStep: vi.fn(),
        };
      });
      
      const TestComponent = () => {
        return (
          <div>
            <button onClick={() => configUpdateFn({ template: 'badge' })}>Change Template</button>
            <PreviewComponent />
          </div>
        );
      };
      
      render(<TestComponent />);
      
      // Wait for initial loading to complete
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      }, { timeout: 500 });
      
      // Change template - this should trigger loading state
      fireEvent.click(screen.getByText('Change Template'));
      
      // Should show loading state briefly
      expect(screen.getByText('Updating preview...')).toBeInTheDocument();
      
      // Should complete loading
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      }, { timeout: 500 });
    });
  });

  describe('Accessibility', () => {
    it('has proper button titles for viewport controls', () => {
      renderWithProvider(<PreviewComponent />);
      
      expect(screen.getByTitle('Desktop view')).toBeInTheDocument();
      expect(screen.getByTitle('Tablet view')).toBeInTheDocument();
      expect(screen.getByTitle('Mobile view')).toBeInTheDocument();
    });

    it('supports keyboard navigation for viewport buttons', () => {
      renderWithProvider(<PreviewComponent />);
      
      const desktopButton = screen.getByText('Desktop');
      const tabletButton = screen.getByText('Tablet');
      
      // Focus desktop button
      desktopButton.focus();
      expect(desktopButton).toHaveFocus();
      
      // Tab to tablet button
      fireEvent.keyDown(desktopButton, { key: 'Tab' });
      tabletButton.focus();
      expect(tabletButton).toHaveFocus();
    });

    it('has proper focus styles for viewport buttons', () => {
      renderWithProvider(<PreviewComponent />);
      
      const desktopButton = screen.getByText('Desktop');
      desktopButton.focus();
      
      // Should have focus outline (tested via CSS)
      expect(desktopButton).toHaveFocus();
    });
  });

  describe('Error Handling', () => {
    it('displays error state when error occurs', async () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const ErrorComponent = () => {
        const [hasError, setHasError] = React.useState(false);
        
        if (hasError) {
          throw new Error('Test error');
        }
        
        return (
          <WidgetProvider>
            <button onClick={() => setHasError(true)}>Trigger Error</button>
            <PreviewComponent />
          </WidgetProvider>
        );
      };
      
      render(<ErrorComponent />);
      
      // Wait for initial loading
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe('Performance', () => {
    it('debounces config changes to prevent excessive re-renders', async () => {
      const renderSpy = vi.fn();
      
      const TestComponent = () => {
        const [count, setCount] = React.useState(0);
        
        React.useEffect(() => {
          renderSpy();
        });
        
        return (
          <WidgetProvider>
            <button onClick={() => setCount(c => c + 1)}>Update {count}</button>
            <PreviewComponent />
          </WidgetProvider>
        );
      };
      
      render(<TestComponent />);
      
      // Rapid clicks should be debounced
      const button = screen.getByText(/Update/);
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);
      
      await waitFor(() => {
        expect(screen.queryByText('Updating preview...')).not.toBeInTheDocument();
      });
      
      // Should have reasonable number of renders
      expect(renderSpy).toHaveBeenCalled();
    });
  });
});