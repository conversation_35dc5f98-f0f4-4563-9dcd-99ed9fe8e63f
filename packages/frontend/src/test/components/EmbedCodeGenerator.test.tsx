import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import EmbedCodeGenerator from '../../components/EmbedCode/EmbedCodeGenerator';
import { WidgetConfig } from '../../types/widget';

// Mock the clipboard API
const mockClipboard = {
  writeText: vi.fn(),
};

Object.assign(navigator, {
  clipboard: mockClipboard,
});

// Mock fetch
global.fetch = vi.fn();

const mockWidgetConfig: WidgetConfig = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  business: {
    placeId: '987fcdeb-51a2-43d7-8f9e-123456789abc',
    name: 'Test Business',
    address: '123 Test St',
    rating: 4.5,
    reviewCount: 100
  },
  template: 'carousel',
  styling: {
    colors: {
      primary: '#4285f4',
      secondary: '#34a853',
      background: '#ffffff',
      text: '#333333',
      border: '#e1e5e9',
    },
    fonts: {
      family: 'Arial, sans-serif',
      size: '14px',
      weight: 'normal',
    },
    dimensions: {
      width: '400px',
      height: '300px',
      borderRadius: '8px',
    },
    spacing: {
      padding: '16px',
      margin: '8px',
      gap: '12px',
    },
  },
  settings: {
    maxReviews: 10,
    minRating: 1,
    sortBy: 'newest',
    showPhotos: true,
    showDates: true,
    autoRefresh: true,
  },
};

const mockEmbedResult = {
  embedCode: `<!-- Google Reviews Widget by ReviewsWidget -->
<div id="reviews-widget-123e4567-e89b-12d3-a456-426614174000" 
     class="reviews-widget-container" 
     data-widget-id="123e4567-e89b-12d3-a456-426614174000"
     data-config='{"businessId":"987fcdeb-51a2-43d7-8f9e-123456789abc","templateType":"carousel"}'
     style="width: 400px; height: 300px; max-width: 100%;">
  <div class="reviews-widget-loading">Loading reviews...</div>
</div>
<script>
(function() {
  if (window.ReviewsWidget) return;
  var script = document.createElement('script');
  script.src = 'https://cdn.example.com/widget-runtime.js';
  script.async = true;
  script.onload = function() {
    if (window.ReviewsWidget) {
      window.ReviewsWidget.init('123e4567-e89b-12d3-a456-426614174000');
    }
  };
  document.head.appendChild(script);
})();
</script>`,
  widgetId: '123e4567-e89b-12d3-a456-426614174000',
  embedUrl: 'https://widgets.example.com/widget/123e4567-e89b-12d3-a456-426614174000',
  previewUrl: 'https://widgets.example.com/preview/123e4567-e89b-12d3-a456-426614174000',
};

describe('EmbedCodeGenerator', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockClipboard.writeText.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should render the component with initial state', () => {
    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    expect(screen.getByRole('heading', { name: 'Generate Embed Code' })).toBeInTheDocument();
    expect(screen.getByText('Generate the HTML code to embed your widget on any website.')).toBeInTheDocument();
    expect(screen.getByText('Standard Embed (Recommended)')).toBeInTheDocument();
    expect(screen.getByText('iFrame Embed')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Generate Embed Code' })).toBeInTheDocument();
  });

  it('should have standard embed type selected by default', () => {
    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const standardRadio = screen.getByDisplayValue('standard');
    const iframeRadio = screen.getByDisplayValue('iframe');

    expect(standardRadio).toBeChecked();
    expect(iframeRadio).not.toBeChecked();
  });

  it('should allow switching between embed types', () => {
    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const iframeRadio = screen.getByDisplayValue('iframe');
    fireEvent.click(iframeRadio);

    expect(iframeRadio).toBeChecked();
    expect(screen.getByDisplayValue('standard')).not.toBeChecked();
  });

  it('should generate standard embed code successfully', async () => {
    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    expect(generateButton).toHaveTextContent('Generating...');
    expect(generateButton).toBeDisabled();

    await waitFor(() => {
      expect(screen.getByText('Your Embed Code')).toBeInTheDocument();
    });

    expect(global.fetch).toHaveBeenCalledWith('/api/embed-code/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        configId: mockWidgetConfig.id,
      }),
    });

    expect(screen.getByText('📋 Copy')).toBeInTheDocument();
    expect(screen.getByText('💾 Download')).toBeInTheDocument();
    expect(screen.getByText(mockEmbedResult.widgetId)).toBeInTheDocument();
  });

  it('should generate iframe embed code when iframe type is selected', async () => {
    const iframeEmbedResult = {
      embedCode: '<iframe src="https://widgets.example.com/embed/123" width="400" height="300"></iframe>',
      widgetId: '123e4567-e89b-12d3-a456-426614174000',
      type: 'iframe',
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: iframeEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    // Switch to iframe type
    const iframeRadio = screen.getByDisplayValue('iframe');
    fireEvent.click(iframeRadio);

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Your Embed Code')).toBeInTheDocument();
    });

    expect(global.fetch).toHaveBeenCalledWith('/api/embed-code/generate/iframe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        configId: mockWidgetConfig.id,
      }),
    });
  });

  it('should handle API errors gracefully', async () => {
    (global.fetch as any).mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        error: {
          message: 'Widget configuration not found',
        },
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Widget configuration not found')).toBeInTheDocument();
    });

    expect(screen.getByText('⚠️')).toBeInTheDocument();
  });

  it('should handle network errors', async () => {
    (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });
  });

  it('should copy embed code to clipboard', async () => {
    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    // Generate embed code first
    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Your Embed Code')).toBeInTheDocument();
    });

    // Click copy button
    const copyButton = screen.getByText('📋 Copy');
    fireEvent.click(copyButton);

    await waitFor(() => {
      expect(mockClipboard.writeText).toHaveBeenCalledWith(mockEmbedResult.embedCode);
      expect(screen.getByText('✓ Copied!')).toBeInTheDocument();
    });

    // Success message should disappear after timeout
    await waitFor(() => {
      expect(screen.getByText('📋 Copy')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('should handle clipboard copy failure with fallback', async () => {
    mockClipboard.writeText.mockRejectedValueOnce(new Error('Clipboard not available'));

    // Mock document methods for fallback
    const mockTextArea = {
      value: '',
      style: {},
      focus: vi.fn(),
      select: vi.fn(),
    };
    const mockCreateElement = vi.fn().mockReturnValue(mockTextArea);
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();
    const mockExecCommand = vi.fn().mockReturnValue(true);

    Object.assign(document, {
      createElement: mockCreateElement,
      execCommand: mockExecCommand,
    });

    Object.assign(document.body, {
      appendChild: mockAppendChild,
      removeChild: mockRemoveChild,
    });

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    // Generate embed code first
    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Your Embed Code')).toBeInTheDocument();
    });

    // Click copy button
    const copyButton = screen.getByText('📋 Copy');
    fireEvent.click(copyButton);

    await waitFor(() => {
      expect(mockCreateElement).toHaveBeenCalledWith('textarea');
      expect(mockTextArea.value).toBe(mockEmbedResult.embedCode);
      expect(mockExecCommand).toHaveBeenCalledWith('copy');
      expect(screen.getByText('✓ Copied!')).toBeInTheDocument();
    });
  });

  it('should download embed code as HTML file', async () => {
    // Mock URL and link creation
    const mockCreateObjectURL = vi.fn().mockReturnValue('blob:mock-url');
    const mockRevokeObjectURL = vi.fn();
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    const mockCreateElement = vi.fn().mockReturnValue(mockLink);
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();

    Object.assign(URL, {
      createObjectURL: mockCreateObjectURL,
      revokeObjectURL: mockRevokeObjectURL,
    });

    Object.assign(document, {
      createElement: mockCreateElement,
    });

    Object.assign(document.body, {
      appendChild: mockAppendChild,
      removeChild: mockRemoveChild,
    });

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    // Generate embed code first
    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Your Embed Code')).toBeInTheDocument();
    });

    // Click download button
    const downloadButton = screen.getByText('💾 Download');
    fireEvent.click(downloadButton);

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockLink.href).toBe('blob:mock-url');
    expect(mockLink.download).toBe(`widget-${mockEmbedResult.widgetId}.html`);
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:mock-url');
  });

  it('should call onEmbedCodeGenerated callback when provided', async () => {
    const mockCallback = vi.fn();

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(
      <EmbedCodeGenerator 
        widgetConfig={mockWidgetConfig} 
        onEmbedCodeGenerated={mockCallback}
      />
    );

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(mockCallback).toHaveBeenCalledWith(mockEmbedResult.embedCode);
    });
  });

  it('should display preview URL as clickable link', async () => {
    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      const previewLink = screen.getByRole('link');
      expect(previewLink).toHaveAttribute('href', mockEmbedResult.previewUrl);
      expect(previewLink).toHaveAttribute('target', '_blank');
      expect(previewLink).toHaveAttribute('rel', 'noopener noreferrer');
    });
  });

  it('should display usage instructions', async () => {
    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: mockEmbedResult,
      }),
    });

    render(<EmbedCodeGenerator widgetConfig={mockWidgetConfig} />);

    const generateButton = screen.getByRole('button', { name: 'Generate Embed Code' });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('How to use this code:')).toBeInTheDocument();
      expect(screen.getByText('Copy the embed code above')).toBeInTheDocument();
      expect(screen.getByText('Paste it into your website\'s HTML where you want the widget to appear')).toBeInTheDocument();
      expect(screen.getByText('The widget will automatically load and display your Google reviews')).toBeInTheDocument();
    });
  });
});