import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import App from '../../App';

// Mock styled-components
vi.mock('styled-components', () => {
  const styled = (tag: any) => (styles: any) => {
    return React.forwardRef((props: any, ref: any) => {
      return React.createElement(tag, { ...props, ref, 'data-styled': true });
    });
  };
  
  // Add properties for all HTML tags
  styled.div = styled('div');
  styled.nav = styled('nav');
  styled.header = styled('header');
  styled.main = styled('main');
  styled.h1 = styled('h1');
  styled.h2 = styled('h2');
  styled.p = styled('p');
  styled.button = styled('button');
  styled.form = styled('form');
  styled.input = styled('input');
  styled.label = styled('label');
  styled.span = styled('span');
  styled.ul = styled('ul');
  styled.li = styled('li');
  styled.img = styled('img');
  styled.section = styled('section');
  styled.h3 = styled('h3');
  styled.select = styled('select');
  styled.textarea = styled('textarea');
  
  return { default: styled };
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Widget Creation Integration Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  it('should complete the full widget creation workflow', async () => {
    renderWithProviders(<App />);

    // Step 1: Business Search
    expect(screen.getByText('Find Your Business')).toBeInTheDocument();
    
    const searchInput = screen.getByPlaceholderText(/search for your business/i);
    const searchButton = screen.getByRole('button', { name: /search/i });

    await user.type(searchInput, 'Test Restaurant');
    await user.click(searchButton);

    // Wait for search results (mocked)
    await waitFor(() => {
      expect(screen.getByText('Test Business')).toBeInTheDocument();
    });

    // Select business
    const businessResult = screen.getByText('Test Business');
    await user.click(businessResult);

    // Step 2: Template Selection
    const layoutStep = screen.getByText('Layout');
    await user.click(layoutStep);

    await waitFor(() => {
      expect(screen.getByText('Choose a Template')).toBeInTheDocument();
    });

    // Select carousel template
    const carouselTemplate = screen.getByTestId('template-carousel');
    await user.click(carouselTemplate);

    // Step 3: Header Configuration
    const headerStep = screen.getByText('Header');
    await user.click(headerStep);

    const headerInput = screen.getByLabelText(/header text/i);
    await user.clear(headerInput);
    await user.type(headerInput, 'Customer Reviews');

    // Step 4: Review Settings
    const reviewsStep = screen.getByText('Reviews');
    await user.click(reviewsStep);

    const maxReviewsSlider = screen.getByLabelText(/maximum reviews/i);
    fireEvent.change(maxReviewsSlider, { target: { value: '10' } });

    // Step 5: Style Customization
    const styleStep = screen.getByText('Style');
    await user.click(styleStep);

    const colorPicker = screen.getByLabelText(/primary color/i);
    fireEvent.change(colorPicker, { target: { value: '#ff6b6b' } });

    // Step 6: Final Settings
    const settingsStep = screen.getByText('Settings');
    await user.click(settingsStep);

    const autoRefreshToggle = screen.getByLabelText(/auto refresh/i);
    await user.click(autoRefreshToggle);

    // Verify preview is updated throughout
    expect(screen.getByTestId('widget-preview')).toBeInTheDocument();

    // Save configuration
    const saveButton = screen.getByRole('button', { name: /save configuration/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText(/configuration saved/i)).toBeInTheDocument();
    });

    // Generate embed code
    const generateButton = screen.getByRole('button', { name: /generate embed code/i });
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByTestId('embed-code-display')).toBeInTheDocument();
    });

    // Test copy functionality
    const copyButton = screen.getByRole('button', { name: /copy/i });
    await user.click(copyButton);

    await waitFor(() => {
      expect(screen.getByText(/copied to clipboard/i)).toBeInTheDocument();
    });
  });

  it('should handle form validation errors', async () => {
    renderWithProviders(<App />);

    // Try to search without entering business name
    const searchButton = screen.getByRole('button', { name: /search/i });
    await user.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a business name/i)).toBeInTheDocument();
    });

    // Navigate to style step and test invalid color
    const searchInput = screen.getByPlaceholderText(/search for your business/i);
    await user.type(searchInput, 'Test Business');
    await user.click(searchButton);

    await waitFor(() => {
      const businessResult = screen.getByText('Test Business');
      user.click(businessResult);
    });

    const styleStep = screen.getByText('Style');
    await user.click(styleStep);

    const colorPicker = screen.getByLabelText(/primary color/i);
    await user.clear(colorPicker);
    await user.type(colorPicker, 'invalid-color');

    await waitFor(() => {
      expect(screen.getByText(/invalid color format/i)).toBeInTheDocument();
    });
  });

  it('should maintain state when navigating between steps', async () => {
    renderWithProviders(<App />);

    // Set up initial state
    const searchInput = screen.getByPlaceholderText(/search for your business/i);
    await user.type(searchInput, 'Test Business');
    await user.click(screen.getByRole('button', { name: /search/i }));

    await waitFor(() => {
      const businessResult = screen.getByText('Test Business');
      user.click(businessResult);
    });

    // Configure header
    const headerStep = screen.getByText('Header');
    await user.click(headerStep);

    const headerInput = screen.getByLabelText(/header text/i);
    await user.clear(headerInput);
    await user.type(headerInput, 'My Custom Header');

    // Navigate to style step
    const styleStep = screen.getByText('Style');
    await user.click(styleStep);

    const colorPicker = screen.getByLabelText(/primary color/i);
    fireEvent.change(colorPicker, { target: { value: '#00ff00' } });

    // Navigate back to header step
    await user.click(headerStep);

    // Verify state is maintained
    expect(screen.getByDisplayValue('My Custom Header')).toBeInTheDocument();

    // Navigate back to style step
    await user.click(styleStep);

    // Verify color is maintained
    expect(screen.getByDisplayValue('#00ff00')).toBeInTheDocument();
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

    renderWithProviders(<App />);

    const searchInput = screen.getByPlaceholderText(/search for your business/i);
    const searchButton = screen.getByRole('button', { name: /search/i });

    await user.type(searchInput, 'Test Business');
    await user.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText(/unable to search for businesses/i)).toBeInTheDocument();
    });

    // Test retry functionality
    const retryButton = screen.getByRole('button', { name: /retry/i });
    expect(retryButton).toBeInTheDocument();

    // Mock successful retry
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        businesses: [
          {
            id: '1',
            name: 'Test Business',
            address: '123 Test St',
            rating: 4.5,
            reviewCount: 100,
          },
        ],
        totalResults: 1,
      }),
    });

    await user.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText('Test Business')).toBeInTheDocument();
    });
  });

  it('should be responsive and work on mobile devices', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 667,
    });

    // Trigger resize event
    fireEvent(window, new Event('resize'));

    renderWithProviders(<App />);

    // Check for mobile-specific elements
    expect(screen.getByTestId('mobile-menu-button')).toBeInTheDocument();

    // Test mobile navigation
    const mobileMenuButton = screen.getByTestId('mobile-menu-button');
    await user.click(mobileMenuButton);

    expect(screen.getByTestId('mobile-step-menu')).toBeInTheDocument();
  });

  it('should support keyboard navigation', async () => {
    renderWithProviders(<App />);

    // Test tab navigation
    const searchInput = screen.getByPlaceholderText(/search for your business/i);
    searchInput.focus();

    // Tab to search button
    await user.tab();
    expect(screen.getByRole('button', { name: /search/i })).toHaveFocus();

    // Tab to step navigation
    await user.tab();
    expect(screen.getByText('Source')).toHaveFocus();

    // Test arrow key navigation in steps
    fireEvent.keyDown(screen.getByText('Source'), { key: 'ArrowRight' });
    expect(screen.getByText('Layout')).toHaveFocus();
  });

  it('should update preview in real-time', async () => {
    renderWithProviders(<App />);

    // Set up business
    const searchInput = screen.getByPlaceholderText(/search for your business/i);
    await user.type(searchInput, 'Test Business');
    await user.click(screen.getByRole('button', { name: /search/i }));

    await waitFor(() => {
      const businessResult = screen.getByText('Test Business');
      user.click(businessResult);
    });

    // Navigate to header step
    const headerStep = screen.getByText('Header');
    await user.click(headerStep);

    // Change header text
    const headerInput = screen.getByLabelText(/header text/i);
    await user.clear(headerInput);
    await user.type(headerInput, 'Live Preview Test');

    // Verify preview updates
    await waitFor(() => {
      const preview = screen.getByTestId('widget-preview');
      expect(preview).toHaveTextContent('Live Preview Test');
    });

    // Navigate to style step
    const styleStep = screen.getByText('Style');
    await user.click(styleStep);

    // Change color
    const colorPicker = screen.getByLabelText(/primary color/i);
    fireEvent.change(colorPicker, { target: { value: '#ff0000' } });

    // Verify preview updates with new color
    await waitFor(() => {
      const preview = screen.getByTestId('widget-preview');
      expect(preview).toHaveStyle('--primary-color: #ff0000');
    });
  });
});