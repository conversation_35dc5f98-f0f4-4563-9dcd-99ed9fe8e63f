import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { vi } from 'vitest';
import App from '../App';
import { WidgetProvider } from '../context/WidgetContext';
import { Layout } from '../components/Layout/Layout';
import { SourcePage } from '../pages/SourcePage';
import { LayoutPage } from '../pages/LayoutPage';
import { HeaderPage } from '../pages/HeaderPage';
import { ReviewsPage } from '../pages/ReviewsPage';
import { StylePage } from '../pages/StylePage';
import { SettingsPage } from '../pages/SettingsPage';

// Mock all styled-components
vi.mock('styled-components', () => {
  const styled = (tag: any) => (styles: any) => {
    return React.forwardRef((props: any, ref: any) => {
      return React.createElement(tag, { ...props, ref, 'data-styled': true });
    });
  };
  
  // Add properties for all HTML tags
  styled.div = styled('div');
  styled.nav = styled('nav');
  styled.header = styled('header');
  styled.main = styled('main');
  styled.h1 = styled('h1');
  styled.h2 = styled('h2');
  styled.p = styled('p');
  styled.button = styled('button');
  styled.form = styled('form');
  styled.input = styled('input');
  styled.label = styled('label');
  styled.span = styled('span');
  styled.ul = styled('ul');
  styled.li = styled('li');
  styled.img = styled('img');
  styled.section = styled('section');
  styled.h3 = styled('h3');
  
  return { default: styled };
});

// Create a test version of App that accepts initial entries
const TestApp = ({ initialEntries }: { initialEntries: string[] }) => {
  return (
    <WidgetProvider>
      <MemoryRouter initialEntries={initialEntries}>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<SourcePage />} />
            <Route path="layout" element={<LayoutPage />} />
            <Route path="header" element={<HeaderPage />} />
            <Route path="reviews" element={<ReviewsPage />} />
            <Route path="style" element={<StylePage />} />
            <Route path="settings" element={<SettingsPage />} />
          </Route>
        </Routes>
      </MemoryRouter>
    </WidgetProvider>
  );
};

const renderAppWithRoute = (route: string) => {
  return render(<TestApp initialEntries={[route]} />);
};

describe('App Routing', () => {
  it('renders SourcePage on root route', () => {
    renderAppWithRoute('/');
    expect(screen.getByText('Find Your Business')).toBeInTheDocument();
    expect(screen.getByText('Search for your business by name and address, or paste a Google Maps link to get started.')).toBeInTheDocument();
  });

  it('renders LayoutPage on /layout route', () => {
    renderAppWithRoute('/layout');
    expect(screen.getByText('Choose Your Template')).toBeInTheDocument();
    expect(screen.getByText('Select from our collection of professionally designed widget templates to match your website\'s style.')).toBeInTheDocument();
  });

  it('renders HeaderPage on /header route', () => {
    renderAppWithRoute('/header');
    expect(screen.getByText('Configure Header')).toBeInTheDocument();
    expect(screen.getByText('Customize the header text and appearance for your reviews widget.')).toBeInTheDocument();
  });

  it('renders ReviewsPage on /reviews route', () => {
    renderAppWithRoute('/reviews');
    expect(screen.getByText('Review Settings')).toBeInTheDocument();
    expect(screen.getByText('Configure how reviews are displayed, filtered, and sorted in your widget.')).toBeInTheDocument();
  });

  it('renders StylePage on /style route', () => {
    renderAppWithRoute('/style');
    expect(screen.getByText('Customize Style')).toBeInTheDocument();
    expect(screen.getByText('Personalize colors, fonts, spacing, and other visual elements to match your brand.')).toBeInTheDocument();
  });

  it('renders SettingsPage on /settings route', () => {
    renderAppWithRoute('/settings');
    expect(screen.getByText('Final Settings')).toBeInTheDocument();
    expect(screen.getByText('Review your configuration and generate the embed code for your website.')).toBeInTheDocument();
  });

  it('maintains layout structure across all routes', () => {
    const routes = ['/', '/layout', '/header', '/reviews', '/style', '/settings'];
    
    routes.forEach(route => {
      const { unmount } = renderAppWithRoute(route);
      expect(screen.getAllByText('Google Reviews Widget Generator')).toHaveLength(1);
      expect(screen.getAllByText('Reset')).toHaveLength(1);
      expect(screen.getAllByText('Save Configuration')).toHaveLength(1);
      unmount(); // Clean up between renders
    });
  });
});