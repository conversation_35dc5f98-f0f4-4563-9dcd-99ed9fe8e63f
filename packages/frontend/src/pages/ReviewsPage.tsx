import React from 'react';
import styled from 'styled-components';
import WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';
import { ReviewSettingsComponent } from '../components/Customization/ReviewSettingsComponent';

const PageContainer = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
`;

const PlaceholderContent = styled.div`
  padding: 48px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e0e0e0;
`;

const PlaceholderText = styled.p`
  font-size: 14px;
  color: #999999;
  margin: 0;
`;

export function ReviewsPage() {
  return (
    <PageContainer>
      <Title>Review Settings</Title>
      <Description>
        Configure how reviews are displayed, filtered, and sorted in your widget.
      </Description>
      <WidgetErrorBoundary widgetStep="customization">
        <ReviewSettingsComponent />
      </WidgetErrorBoundary>
    </PageContainer>
  );
}
