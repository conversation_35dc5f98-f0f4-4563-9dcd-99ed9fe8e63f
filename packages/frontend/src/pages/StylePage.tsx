import React from 'react';
import styled from 'styled-components';
import { CustomizationPanelComponent } from '../components/Customization/CustomizationPanelComponent';
import { PreviewComponent } from '../components/Preview/PreviewComponent';
import WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';

const PageContainer = styled.div`
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 32px 16px;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const ContentContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
`;

const PreviewSection = styled.div`
  min-height: 600px;
`;

export function StylePage() {
  return (
    <PageContainer>
      <Title>Customize Style</Title>
      <Description>
        Personalize colors, fonts, spacing, and other visual elements to match your brand.
        Changes are applied in real-time to help you see exactly how your widget will look.
      </Description>
      <ContentContainer>
        <WidgetErrorBoundary widgetStep="customization">
          <CustomizationPanelComponent />
        </WidgetErrorBoundary>
        <PreviewSection>
          <WidgetErrorBoundary widgetStep="preview">
            <PreviewComponent />
          </WidgetErrorBoundary>
        </PreviewSection>
      </ContentContainer>
    </PageContainer>
  );
}