import React, { Suspense } from 'react';
import styled from 'styled-components';
import WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';

// Lazy load heavy components
const TemplateSelectionComponent = React.lazy(() =>
  import('../components/TemplateSelection/TemplateSelectionComponent').then(module => ({
    default: module.TemplateSelectionComponent
  }))
);

const PreviewComponent = React.lazy(() =>
  import('../components/Preview/PreviewComponent').then(module => ({
    default: module.PreviewComponent
  }))
);

// Component loader for heavy components
const ComponentLoader: React.FC = () => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '300px',
    fontSize: '14px',
    color: '#666'
  }}>
    <div style={{
      width: '20px',
      height: '20px',
      border: '2px solid #f3f3f3',
      borderTop: '2px solid #3498db',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginRight: '8px'
    }}></div>
    Loading component...
  </div>
);

const PageContainer = styled.div`
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 32px 16px;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const ContentContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 600px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
`;

const TemplateSection = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const PreviewSection = styled.div`
  min-height: 600px;
`;

export function LayoutPage() {
  return (
    <PageContainer>
      <Title>Choose Layout</Title>
      <Description>
        Select a template that best fits your website's design.
        See how each template looks with your business reviews in real-time.
      </Description>
      <ContentContainer>
        <TemplateSection>
          <WidgetErrorBoundary widgetStep="template-selection">
            <Suspense fallback={<ComponentLoader />}>
              <TemplateSelectionComponent />
            </Suspense>
          </WidgetErrorBoundary>
        </TemplateSection>
        <PreviewSection>
          <WidgetErrorBoundary widgetStep="preview">
            <Suspense fallback={<ComponentLoader />}>
              <PreviewComponent />
            </Suspense>
          </WidgetErrorBoundary>
        </PreviewSection>
      </ContentContainer>
    </PageContainer>
  );
}
