import React, { useState } from 'react';
import styled from 'styled-components';
import { useWidget } from '../context/WidgetContext';
import { WidgetRenderer } from '../components/Preview/WidgetRenderer';
import EmbedCodeGenerator from '../components/EmbedCode/EmbedCodeGenerator';
import WidgetErrorBoundary from '../components/ErrorBoundary/WidgetErrorBoundary';

const PageContainer = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin-bottom: 32px;
  line-height: 1.6;
  text-align: center;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 32px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
`;

const Section = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
`;

const SectionTitle = styled.h3`
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
`;

const PreviewContainer = styled.div`
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ConfigSummary = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const ConfigItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
`;

const ConfigLabel = styled.span`
  font-weight: 500;
  color: #333333;
`;

const ConfigValue = styled.span`
  color: #666666;
  font-size: 14px;
`;

const ColorSwatch = styled.div<{ color: string }>`
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background-color: ${props => props.color};
  border: 1px solid #e0e0e0;
`;

const EmbedSection = styled.div`
  border-top: 1px solid #e0e0e0;
  padding-top: 32px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 32px;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  ${props => props.variant === 'primary' ? `
    background: #4285f4;
    color: white;
    
    &:hover {
      background: #3367d6;
    }
  ` : `
    background: #f8f9fa;
    color: #333333;
    border: 1px solid #e0e0e0;
    
    &:hover {
      background: #e9ecef;
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export function SettingsPage() {
  const { config, resetConfig } = useWidget();
  const [showEmbedCode, setShowEmbedCode] = useState(false);

  const handleSaveConfiguration = () => {
    // Save configuration to localStorage and show embed code
    try {
      localStorage.setItem('savedWidgetConfig', JSON.stringify(config));
      console.log('Configuration saved to localStorage');
      setShowEmbedCode(true);
    } catch (error) {
      console.error('Error saving configuration:', error);
    }
  };

  const formatConfigValue = (key: string, value: any): string => {
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value);
    }
    return String(value);
  };

  return (
    <PageContainer>
      <Title>Final Settings</Title>
      <Description>
        Review your configuration and generate the embed code for your website.
      </Description>

      <ContentGrid>
        <Section>
          <SectionTitle>Widget Preview</SectionTitle>
          <PreviewContainer>
            <WidgetErrorBoundary widgetStep="preview">
              <WidgetRenderer config={config} viewport="desktop" />
            </WidgetErrorBoundary>
          </PreviewContainer>
        </Section>

        <Section>
          <SectionTitle>Configuration Summary</SectionTitle>
          <ConfigSummary>
            <ConfigItem>
              <ConfigLabel>Template</ConfigLabel>
              <ConfigValue>{config.template}</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Primary Color</ConfigLabel>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ColorSwatch color={config.styling.colors.primary} />
                <ConfigValue>{config.styling.colors.primary}</ConfigValue>
              </div>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Background Color</ConfigLabel>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ColorSwatch color={config.styling.colors.background} />
                <ConfigValue>{config.styling.colors.background}</ConfigValue>
              </div>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Font Family</ConfigLabel>
              <ConfigValue>{config.styling.fonts.family}</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Dimensions</ConfigLabel>
              <ConfigValue>{config.styling.dimensions.width} × {config.styling.dimensions.height}</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Max Reviews</ConfigLabel>
              <ConfigValue>{config.settings.maxReviews}</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Min Rating</ConfigLabel>
              <ConfigValue>{config.settings.minRating} stars</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Sort By</ConfigLabel>
              <ConfigValue>{config.settings.sortBy}</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Show Photos</ConfigLabel>
              <ConfigValue>{formatConfigValue('showPhotos', config.settings.showPhotos)}</ConfigValue>
            </ConfigItem>

            <ConfigItem>
              <ConfigLabel>Show Dates</ConfigLabel>
              <ConfigValue>{formatConfigValue('showDates', config.settings.showDates)}</ConfigValue>
            </ConfigItem>

            {config.headerSettings && (
              <>
                <ConfigItem>
                  <ConfigLabel>Header Text</ConfigLabel>
                  <ConfigValue>{config.headerSettings.headerText}</ConfigValue>
                </ConfigItem>

                <ConfigItem>
                  <ConfigLabel>Subheader Text</ConfigLabel>
                  <ConfigValue>{config.headerSettings.subheaderText}</ConfigValue>
                </ConfigItem>
              </>
            )}
          </ConfigSummary>
        </Section>
      </ContentGrid>

      <ActionButtons>
        <Button variant="secondary" onClick={resetConfig}>
          Reset Configuration
        </Button>
        <Button variant="primary" onClick={handleSaveConfiguration}>
          Save Configuration
        </Button>
      </ActionButtons>

      {showEmbedCode && (
        <EmbedSection>
          <WidgetErrorBoundary widgetStep="embed-code">
            <EmbedCodeGenerator
              widgetConfig={config}
              onEmbedCodeGenerated={(embedCode) => {
                console.log('Embed code generated:', embedCode);
              }}
            />
          </WidgetErrorBoundary>
        </EmbedSection>
      )}
    </PageContainer>
  );
}
