// API service for making HTTP requests to the backend

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export class ApiException extends Error {
  constructor(
    public error: ApiError,
    public status: number
  ) {
    super(error.message);
    this.name = 'ApiException';
  }
}

// Request/Response types
export interface BusinessSearchRequest {
  query: string;
  location?: string;
  googleMapsUrl?: string;
}

export interface Business {
  id: string;
  placeId: string;
  name: string;
  address?: string;
  rating?: number;
  reviewCount?: number;
  photoUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BusinessSearchResponse {
  businesses: Business[];
  totalResults: number;
}

// HTTP client utility
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      let errorData: ApiError;

      try {
        errorData = await response.json();
      } catch {
        // If we can't parse the error response, create a generic error
        errorData = {
          code: 'UNKNOWN_ERROR',
          message: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date().toISOString(),
        };
      }

      throw new ApiException(errorData, response.status);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiException) {
      throw error;
    }

    // Handle network errors or other fetch failures
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiException({
        code: 'NETWORK_ERROR',
        message: 'Unable to connect to the server. Please check your internet connection.',
        timestamp: new Date().toISOString(),
      }, 0);
    }

    // Generic error fallback
    throw new ApiException({
      code: 'UNKNOWN_ERROR',
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
    }, 0);
  }
}

// Business search API
export async function searchBusinesses(
  request: BusinessSearchRequest
): Promise<BusinessSearchResponse> {
  const params = new URLSearchParams();
  params.append('query', request.query);
  if (request.location) {
    params.append('location', request.location);
  }
  if (request.googleMapsUrl) {
    params.append('googleMapsUrl', request.googleMapsUrl);
  }

  const response = await apiRequest<{ success: boolean; businesses: Business[] }>(`/business/search?${params.toString()}`);
  return {
    businesses: response.businesses,
    totalResults: response.businesses.length
  };
}

// Business details API (for future use)
export async function getBusinessDetails(businessId: string): Promise<Business> {
  return apiRequest<Business>(`/business/${businessId}`);
}

// Embed code types and API
export interface EmbedCodeResult {
  embedCode: string;
  widgetId: string;
  embedUrl: string;
  previewUrl: string;
}

export interface GenerateEmbedCodeRequest {
  configId: string;
  baseUrl?: string;
}

export interface ValidateEmbedCodeRequest {
  embedCode: string;
}

export interface ValidateEmbedCodeResponse {
  isValid: boolean;
  widgetId: string | null;
  hasWidgetId: boolean;
}

// Generate embed code for widget configuration
export async function generateEmbedCode(
  request: GenerateEmbedCodeRequest
): Promise<EmbedCodeResult> {
  return apiRequest<EmbedCodeResult>('/embed-code/generate', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// Generate iframe embed code
export async function generateIframeEmbedCode(
  request: GenerateEmbedCodeRequest
): Promise<{ embedCode: string; widgetId: string; type: string }> {
  return apiRequest<{ embedCode: string; widgetId: string; type: string }>('/embed-code/generate/iframe', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// Get existing embed code for widget
export async function getEmbedCode(
  widgetId: string,
  baseUrl?: string
): Promise<EmbedCodeResult> {
  const params = baseUrl ? `?baseUrl=${encodeURIComponent(baseUrl)}` : '';
  return apiRequest<EmbedCodeResult>(`/embed-code/${widgetId}${params}`);
}

// Validate embed code
export async function validateEmbedCode(
  request: ValidateEmbedCodeRequest
): Promise<ValidateEmbedCodeResponse> {
  return apiRequest<ValidateEmbedCodeResponse>('/embed-code/validate', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// Reviews types and API
export interface Review {
  id: string;
  authorName: string;
  authorPhotoUrl?: string;
  rating: number;
  text: string;
  publishedDate: string;
  isVerified: boolean;
}

export interface ReviewsResponse {
  reviews: Review[];
  businessInfo: Business;
  totalReviews: number;
  averageRating: number;
  lastUpdated: string;
}

export interface ReviewsFetchOptions {
  maxReviews?: number;
  minRating?: number;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low';
  forceRefresh?: boolean;
}

// Get reviews for a business by Google Place ID
export async function getReviewsByPlaceId(
  placeId: string,
  options: ReviewsFetchOptions = {}
): Promise<ReviewsResponse> {
  const params = new URLSearchParams();

  if (options.maxReviews) {
    params.append('maxReviews', options.maxReviews.toString());
  }
  if (options.minRating) {
    params.append('minRating', options.minRating.toString());
  }
  if (options.sortBy) {
    params.append('sortBy', options.sortBy);
  }
  if (options.forceRefresh) {
    params.append('forceRefresh', 'true');
  }

  const queryString = params.toString();
  const endpoint = `/reviews/place/${placeId}${queryString ? `?${queryString}` : ''}`;

  const response = await apiRequest<{ success: boolean; data: ReviewsResponse }>(endpoint);
  return response.data;
}

// Get reviews directly from Google Places API (bypasses database)
export async function getReviewsByPlaceIdDirect(
  placeId: string,
  options: ReviewsFetchOptions = {}
): Promise<ReviewsResponse> {
  const params = new URLSearchParams();

  if (options.maxReviews) {
    params.append('maxReviews', options.maxReviews.toString());
  }
  if (options.minRating) {
    params.append('minRating', options.minRating.toString());
  }
  if (options.sortBy) {
    params.append('sortBy', options.sortBy);
  }

  const queryString = params.toString();
  const endpoint = `/reviews-direct/place/${placeId}${queryString ? `?${queryString}` : ''}`;

  const response = await apiRequest<{ success: boolean; data: ReviewsResponse }>(endpoint);
  return response.data;
}
