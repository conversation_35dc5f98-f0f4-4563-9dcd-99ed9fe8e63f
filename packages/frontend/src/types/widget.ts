export interface Business {
  placeId: string;
  name: string;
  address?: string;
  rating?: number;
  reviewCount?: number;
  photoUrl?: string;
}

export interface Review {
  id: string;
  authorName: string;
  authorPhotoUrl?: string;
  rating: number;
  text: string;
  publishedDate: string;
  isVerified: boolean;
}

export interface ReviewsResponse {
  reviews: Review[];
  businessInfo: Business;
  totalReviews: number;
  averageRating: number;
  lastUpdated: string;
}

export interface WidgetStyling {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    border: string;
  };
  fonts: {
    family: string;
    size: string;
    weight: string;
  };
  dimensions: {
    width: string;
    height: string;
    borderRadius: string;
  };
  spacing: {
    padding: string;
    margin: string;
    gap: string;
  };
}

export interface WidgetSettings {
  maxReviews: number;
  minRating: number;
  sortBy: 'newest' | 'oldest' | 'rating_high' | 'rating_low';
  showPhotos: boolean;
  showDates: boolean;
  autoRefresh: boolean;
}

export interface HeaderSettings {
  showHeader: boolean;
  headerText: string;
  headerSize: 'small' | 'medium' | 'large';
  headerColor: string;
  headerAlign: 'left' | 'center' | 'right';
  showSubheader: boolean;
  subheaderText: string;
  subheaderColor: string;
  subheaderAlign: 'left' | 'center' | 'right';
}

export type TemplateType = 'carousel' | 'badge' | 'grid' | 'simple-carousel' | 'slider' | 'floating-badge';

export interface WidgetConfig {
  id?: string;
  business?: Business;
  template: TemplateType;
  styling: WidgetStyling;
  settings: WidgetSettings;
  headerSettings?: HeaderSettings;
}

export interface WidgetContextType {
  config: WidgetConfig;
  updateConfig: (updates: Partial<WidgetConfig>) => void;
  resetConfig: () => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
}
