import React from 'react';
import styled from 'styled-components';
import { useWidget } from '../../context/WidgetContext';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const Section = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
`;

const Select = styled.select`
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background-color: #ffffff;
  color: #333333;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background-color: #ffffff;
  color: #333333;

  &:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
`;

const CheckboxLabel = styled.label`
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  margin: 0;
`;

const HelpText = styled.p`
  font-size: 12px;
  color: #666666;
  margin-top: 4px;
  margin-bottom: 0;
`;

export function ReviewSettingsComponent() {
    const { config, updateConfig } = useWidget();
    const { settings } = config;

    const handleSettingChange = (key: keyof typeof settings, value: any) => {
        updateConfig({
            settings: {
                ...settings,
                [key]: value,
            },
        });
    };

    return (
        <Container>
            <Section>
                <SectionTitle>Review Filtering</SectionTitle>

                <FormGroup>
                    <Label htmlFor="minRating">Minimum Star Rating</Label>
                    <Select
                        id="minRating"
                        value={settings.minRating}
                        onChange={(e) => handleSettingChange('minRating', parseInt(e.target.value))}
                    >
                        <option value={1}>1 Star and above</option>
                        <option value={2}>2 Stars and above</option>
                        <option value={3}>3 Stars and above</option>
                        <option value={4}>4 Stars and above</option>
                        <option value={5}>5 Stars only</option>
                    </Select>
                    <HelpText>Only show reviews with this rating or higher</HelpText>
                </FormGroup>

                <FormGroup>
                    <Label htmlFor="maxReviews">Maximum Number of Reviews</Label>
                    <Input
                        id="maxReviews"
                        type="number"
                        min="1"
                        max="20"
                        value={settings.maxReviews}
                        onChange={(e) => handleSettingChange('maxReviews', parseInt(e.target.value))}
                    />
                    <HelpText>Limit the number of reviews displayed (1-20)</HelpText>
                </FormGroup>
            </Section>

            <Section>
                <SectionTitle>Review Sorting</SectionTitle>

                <FormGroup>
                    <Label htmlFor="sortBy">Sort Reviews By</Label>
                    <Select
                        id="sortBy"
                        value={settings.sortBy}
                        onChange={(e) => handleSettingChange('sortBy', e.target.value)}
                    >
                        <option value="newest">Most Recent First</option>
                        <option value="oldest">Oldest First</option>
                        <option value="rating_high">Highest Rating First</option>
                        <option value="rating_low">Lowest Rating First</option>
                    </Select>
                    <HelpText>Choose how reviews are ordered in your widget</HelpText>
                </FormGroup>
            </Section>

            <Section>
                <SectionTitle>Display Options</SectionTitle>

                <FormGroup>
                    <CheckboxContainer>
                        <Checkbox
                            id="showPhotos"
                            type="checkbox"
                            checked={settings.showPhotos}
                            onChange={(e) => handleSettingChange('showPhotos', e.target.checked)}
                        />
                        <CheckboxLabel htmlFor="showPhotos">Show reviewer profile photos</CheckboxLabel>
                    </CheckboxContainer>
                    <HelpText>Display profile pictures of reviewers when available</HelpText>
                </FormGroup>

                <FormGroup>
                    <CheckboxContainer>
                        <Checkbox
                            id="showDates"
                            type="checkbox"
                            checked={settings.showDates}
                            onChange={(e) => handleSettingChange('showDates', e.target.checked)}
                        />
                        <CheckboxLabel htmlFor="showDates">Show review dates</CheckboxLabel>
                    </CheckboxContainer>
                    <HelpText>Display when each review was published</HelpText>
                </FormGroup>

                <FormGroup>
                    <CheckboxContainer>
                        <Checkbox
                            id="autoRefresh"
                            type="checkbox"
                            checked={settings.autoRefresh}
                            onChange={(e) => handleSettingChange('autoRefresh', e.target.checked)}
                        />
                        <CheckboxLabel htmlFor="autoRefresh">Auto-refresh reviews</CheckboxLabel>
                    </CheckboxContainer>
                    <HelpText>Automatically update reviews periodically</HelpText>
                </FormGroup>
            </Section>
        </Container>
    );
}
