import React from 'react';

interface SyntaxHighlighterProps {
  code: string;
  language?: 'html' | 'javascript' | 'css';
  className?: string;
}

export const SyntaxHighlighter: React.FC<SyntaxHighlighterProps> = ({
  code,
  language = 'html',
  className = '',
}) => {
  const highlightHtml = (htmlCode: string): string => {
    return htmlCode
      // Comments
      .replace(/(&lt;!--.*?--&gt;)/g, '<span class="comment">$1</span>')
      // HTML tags
      .replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g, 
        '<span class="tag">$1$2</span><span class="attr">$3</span><span class="tag">$4</span>');
  };

  const highlightJavaScript = (jsCode: string): string => {
    return jsCode
      // Keywords
      .replace(/\b(var|let|const|function|if|else|for|while|return|true|false|null|undefined|new|this|typeof)\b/g, 
        '<span class="keyword">$1</span>')
      // Strings
      .replace(/(['"`])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="string">$1$2$1</span>')
      // Numbers
      .replace(/\b(\d+\.?\d*)\b/g, '<span class="number">$1</span>')
      // Comments
      .replace(/(\/\/.*$)/gm, '<span class="comment">$1</span>')
      .replace(/(\/\*.*?\*\/)/gs, '<span class="comment">$1</span>')
      // Functions
      .replace(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g, '<span class="function">$1</span>(');
  };

  const highlightCss = (cssCode: string): string => {
    return cssCode
      // Selectors
      .replace(/^([^{]+)(?=\s*{)/gm, '<span class="selector">$1</span>')
      // Properties
      .replace(/([a-zA-Z-]+)(\s*:)/g, '<span class="property">$1</span><span class="operator">$2</span>')
      // Values
      .replace(/(:\s*)([^;]+)(;?)/g, '$1<span class="value">$2</span><span class="operator">$3</span>')
      // Comments
      .replace(/(\/\*.*?\*\/)/gs, '<span class="comment">$1</span>');
  };

  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  const getHighlightedCode = (): string => {
    const escapedCode = escapeHtml(code);
    
    switch (language) {
      case 'html':
        return highlightHtml(escapedCode);
      case 'javascript':
        return highlightJavaScript(escapedCode);
      case 'css':
        return highlightCss(escapedCode);
      default:
        return escapedCode;
    }
  };

  return (
    <div className={`syntax-highlighter ${className}`}>
      <pre className="code-block">
        <code 
          className={`language-${language}`}
          dangerouslySetInnerHTML={{ __html: getHighlightedCode() }}
        />
      </pre>
      
      <style>{`
        .syntax-highlighter {
          position: relative;
        }

        .code-block {
          background: #1e1e1e;
          color: #d4d4d4;
          padding: 16px;
          border-radius: 6px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
          font-size: 13px;
          line-height: 1.5;
          overflow-x: auto;
          margin: 0;
          white-space: pre-wrap;
          word-break: break-all;
        }

        .code-block code {
          background: none;
          padding: 0;
          border-radius: 0;
          font-family: inherit;
          font-size: inherit;
          color: inherit;
        }

        /* Syntax highlighting styles */
        :global(.syntax-highlighter .tag) {
          color: #569cd6;
        }

        :global(.syntax-highlighter .attr-name) {
          color: #9cdcfe;
        }

        :global(.syntax-highlighter .attr) {
          color: #9cdcfe;
        }

        :global(.syntax-highlighter .string) {
          color: #ce9178;
        }

        :global(.syntax-highlighter .keyword) {
          color: #c586c0;
        }

        :global(.syntax-highlighter .number) {
          color: #b5cea8;
        }

        :global(.syntax-highlighter .comment) {
          color: #6a9955;
          font-style: italic;
        }

        :global(.syntax-highlighter .function) {
          color: #dcdcaa;
        }

        :global(.syntax-highlighter .operator) {
          color: #d4d4d4;
        }

        :global(.syntax-highlighter .selector) {
          color: #d7ba7d;
        }

        :global(.syntax-highlighter .property) {
          color: #9cdcfe;
        }

        :global(.syntax-highlighter .value) {
          color: #ce9178;
        }

        /* Light theme alternative */
        .syntax-highlighter.light .code-block {
          background: #f8f8f8;
          color: #333;
        }

        .syntax-highlighter.light :global(.tag) {
          color: #0000ff;
        }

        .syntax-highlighter.light :global(.attr-name) {
          color: #ff0000;
        }

        .syntax-highlighter.light :global(.string) {
          color: #008000;
        }

        .syntax-highlighter.light :global(.keyword) {
          color: #0000ff;
        }

        .syntax-highlighter.light :global(.number) {
          color: #098658;
        }

        .syntax-highlighter.light :global(.comment) {
          color: #008000;
          font-style: italic;
        }

        .syntax-highlighter.light :global(.function) {
          color: #795e26;
        }
      `}</style>
    </div>
  );
};

export default SyntaxHighlighter;