import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { Business } from '../../types/widget';
import { useWidget } from '../../context/WidgetContext';
import { searchBusinesses, ApiException } from '../../services/api';

// Styled components
const SearchContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const SearchForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #333333;
`;

const Input = styled.input`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4285f4;
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`;

const SearchButton = styled.button`
  padding: 12px 24px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover:not(:disabled) {
    background-color: #3367d6;
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`;

const LoadingSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ResultsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const BusinessCard = styled.div<{ selected?: boolean }>`
  padding: 16px;
  border: 2px solid ${props => props.selected ? '#4285f4' : '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${props => props.selected ? '#f8f9ff' : '#ffffff'};

  &:hover {
    border-color: #4285f4;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
  }
`;

const BusinessInfo = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const BusinessPhoto = styled.img`
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
`;

const BusinessDetails = styled.div`
  flex: 1;
`;

const BusinessName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 4px 0;
`;

const BusinessAddress = styled.p`
  font-size: 14px;
  color: #666666;
  margin: 0 0 8px 0;
`;

const BusinessRating = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666666;
`;

const StarRating = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
`;

const Star = styled.span<{ filled: boolean }>`
  color: ${props => props.filled ? '#ffa500' : '#e0e0e0'};
  font-size: 16px;
`;

const ErrorMessage = styled.div`
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
`;

const EmptyState = styled.div`
  padding: 32px;
  text-align: center;
  color: #666666;
  font-size: 16px;
`;

const HelpText = styled.p`
  font-size: 14px;
  color: #666666;
  margin: 8px 0 0 0;
  line-height: 1.4;
`;

// Interfaces
interface BusinessSearchComponentProps {
  onBusinessSelect?: (business: Business) => void;
}

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function BusinessSearchComponent({ onBusinessSelect }: BusinessSearchComponentProps) {
  const { config, updateConfig } = useWidget();
  const [query, setQuery] = useState('');
  const [location, setLocation] = useState('');
  const [googleMapsUrl, setGoogleMapsUrl] = useState('');
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(config.business || null);
  const [hasSearched, setHasSearched] = useState(false);

  // Debounced search for real-time functionality
  const debouncedQuery = useDebounce(query, 500);
  const debouncedLocation = useDebounce(location, 500);

  // Auto-search when debounced values change (but not on initial load)
  useEffect(() => {
    if (debouncedQuery.trim() && hasSearched) {
      handleSearch();
    }
  }, [debouncedQuery, debouncedLocation]);

  const handleSearch = useCallback(async () => {
    if (!query.trim()) {
      setError('Please enter a business name to search');
      return;
    }

    setLoading(true);
    setError(null);
    setHasSearched(true);

    try {
      const searchRequest = {
        query: query.trim(),
        ...(location.trim() && { location: location.trim() }),
        ...(googleMapsUrl.trim() && { googleMapsUrl: googleMapsUrl.trim() }),
      };

      const response = await searchBusinesses(searchRequest);
      setBusinesses(response.businesses);

      if (response.businesses.length === 0) {
        setError('No businesses found. Try adjusting your search terms or adding a location.');
      }
    } catch (err) {
      console.error('Search error:', err);
      if (err instanceof ApiException && err.error) {
        setError(err.error.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to search for businesses. Please try again.');
      }
      setBusinesses([]);
    } finally {
      setLoading(false);
    }
  }, [query, location, googleMapsUrl]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch();
  };

  const handleBusinessSelect = (business: Business) => {
    setSelectedBusiness(business);
    updateConfig({ business });
    onBusinessSelect?.(business);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Star key={i} filled={true}>★</Star>);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<Star key={i} filled={true}>☆</Star>);
      } else {
        stars.push(<Star key={i} filled={false}>☆</Star>);
      }
    }

    return stars;
  };

  return (
    <SearchContainer>
      <SearchForm onSubmit={handleSubmit}>
        <InputGroup>
          <Label htmlFor="business-query">Business Name *</Label>
          <Input
            id="business-query"
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter your business name..."
            disabled={loading}
            required
          />
          <HelpText>
            Enter the name of your business as it appears on Google
          </HelpText>
        </InputGroup>

        <InputGroup>
          <Label htmlFor="business-location">Location (Optional)</Label>
          <Input
            id="business-location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="City, State or Full Address..."
            disabled={loading}
          />
          <HelpText>
            Add a location to help narrow down search results
          </HelpText>
        </InputGroup>

        <InputGroup>
          <Label htmlFor="google-maps-url">Google Maps URL (Alternative)</Label>
          <Input
            id="google-maps-url"
            type="url"
            value={googleMapsUrl}
            onChange={(e) => setGoogleMapsUrl(e.target.value)}
            placeholder="https://maps.google.com/..."
            disabled={loading}
          />
          <HelpText>
            Alternatively, paste a Google Maps link to your business
          </HelpText>
        </InputGroup>

        <SearchButton type="submit" disabled={loading || !query.trim()}>
          {loading && <LoadingSpinner />}
          {loading ? 'Searching...' : 'Search Business'}
        </SearchButton>
      </SearchForm>

      {error && (
        <ErrorMessage>
          {error}
        </ErrorMessage>
      )}

      {businesses.length > 0 && (
        <ResultsContainer>
          {businesses.map((business) => (
            <BusinessCard
              key={business.placeId}
              selected={selectedBusiness?.placeId === business.placeId}
              onClick={() => handleBusinessSelect(business)}
            >
              <BusinessInfo>
                {business.photoUrl && (
                  <BusinessPhoto
                    src={business.photoUrl}
                    alt={business.name}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                )}
                <BusinessDetails>
                  <BusinessName>{business.name}</BusinessName>
                  {business.address && (
                    <BusinessAddress>{business.address}</BusinessAddress>
                  )}
                  <BusinessRating>
                    <StarRating>
                      {renderStars(business.rating || 0)}
                    </StarRating>
                    <span>
                      {business.rating?.toFixed(1) || 'No rating'} 
                      {business.reviewCount && ` (${business.reviewCount} reviews)`}
                    </span>
                  </BusinessRating>
                </BusinessDetails>
              </BusinessInfo>
            </BusinessCard>
          ))}
        </ResultsContainer>
      )}

      {hasSearched && businesses.length === 0 && !loading && !error && (
        <EmptyState>
          No businesses found for your search. Try different keywords or add a location.
        </EmptyState>
      )}
    </SearchContainer>
  );
}