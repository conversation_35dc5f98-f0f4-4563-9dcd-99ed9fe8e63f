import React from 'react';
import styled from 'styled-components';
import { Outlet } from 'react-router-dom';
import { StepNavigation } from './StepNavigation';
import { Header } from './Header';

const LayoutContainer = styled.div`
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
`;

const Main = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 20px;

  @media (max-width: 768px) {
    padding: 0 16px;
  }
`;

const ContentArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0;

  @media (max-width: 768px) {
    gap: 16px;
    padding: 16px 0;
  }
`;

export function Layout() {
  return (
    <LayoutContainer>
      <Header />
      <Main>
        <StepNavigation />
        <ContentArea>
          <Outlet />
        </ContentArea>
      </Main>
    </LayoutContainer>
  );
}