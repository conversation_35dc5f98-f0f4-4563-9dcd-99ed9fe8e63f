import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useWidget } from '../../context/WidgetContext';
import { WidgetRenderer } from './WidgetRenderer';

// Preview container types
type ViewportSize = 'desktop' | 'tablet' | 'mobile';

interface ViewportDimensions {
  width: number;
  height: number;
}

const viewportSizes: Record<ViewportSize, ViewportDimensions> = {
  desktop: { width: 1200, height: 800 },
  tablet: { width: 768, height: 1024 },
  mobile: { width: 375, height: 667 },
};

// Styled components
const PreviewContainer = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const PreviewHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 12px;
`;

const PreviewTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0;
`;

const PreviewControls = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const ViewportButton = styled.button<{ $active: boolean }>`
  padding: 8px 12px;
  border: 1px solid ${props => props.$active ? '#4285f4' : '#e0e0e0'};
  background: ${props => props.$active ? '#4285f4' : '#ffffff'};
  color: ${props => props.$active ? '#ffffff' : '#666666'};
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    border-color: #4285f4;
    background: ${props => props.$active ? '#3367d6' : '#f8f9ff'};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  }
`;

const PreviewViewport = styled.div<{
  $width: number;
  $height: number;
  $currentViewport: ViewportSize;
}>`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  overflow: auto;
  
  /* Create a device frame effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    ${props => props.$currentViewport === 'mobile' && `
      background: linear-gradient(to bottom, 
        #333 0%, #333 20px, 
        transparent 20px, transparent calc(100% - 20px),
        #333 calc(100% - 20px), #333 100%);
      border-radius: 20px;
    `}
  }
`;

const PreviewFrame = styled.div<{
  $width: number;
  $height: number;
  $viewport: ViewportSize;
}>`
  width: ${props => Math.min(props.$width, 800)}px;
  max-width: 100%;
  min-height: ${props => Math.min(props.$height * 0.6, 500)}px;
  background: #ffffff;
  border-radius: ${props => props.$viewport === 'mobile' ? '20px' : '8px'};
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  
  ${props => props.$viewport === 'mobile' && `
    border: 8px solid #333333;
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #666;
      border-radius: 2px;
      z-index: 1;
    }
  `}
  
  ${props => props.$viewport === 'tablet' && `
    border: 4px solid #666666;
    border-radius: 12px;
  `}
`;

const PreviewContent = styled.div`
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
`;

const LoadingState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666666;
  font-size: 14px;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #dc3545;
  font-size: 14px;
  text-align: center;
  
  &::before {
    content: '⚠️';
    font-size: 24px;
    margin-bottom: 8px;
  }
`;

// Icons for viewport buttons
const DesktopIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7l-2 3v1h8v-1l-2-3h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H3V4h18v10z" />
  </svg>
);

const TabletIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M19 1H5c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 18H5V5h14v14z" />
  </svg>
);

const MobileIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
    <path d="M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 18H7V5h10v14z" />
  </svg>
);

interface PreviewComponentProps {
  className?: string;
  showControls?: boolean;
  defaultViewport?: ViewportSize;
}

export function PreviewComponent({
  className,
  showControls = true,
  defaultViewport = 'desktop'
}: PreviewComponentProps) {
  const { config } = useWidget();
  const [currentViewport, setCurrentViewport] = useState<ViewportSize>(defaultViewport);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset error state when config changes
  useEffect(() => {
    setError(null);
    // No loading simulation needed for preview
    setIsLoading(false);
  }, [config]);

  const handleViewportChange = (viewport: ViewportSize) => {
    setCurrentViewport(viewport);
  };

  const renderPreviewContent = () => {
    if (isLoading) {
      return <LoadingState>Updating preview...</LoadingState>;
    }

    if (error) {
      return <ErrorState>{error}</ErrorState>;
    }

    // Always show the widget renderer with mock data for preview
    return <WidgetRenderer config={config} viewport={currentViewport} />;
  };

  const currentDimensions = viewportSizes[currentViewport];

  return (
    <PreviewContainer className={className}>
      {showControls && (
        <PreviewHeader>
          <PreviewTitle>Live Preview</PreviewTitle>
          <PreviewControls>
            <ViewportButton
              $active={currentViewport === 'desktop'}
              onClick={() => handleViewportChange('desktop')}
              title="Desktop view"
            >
              <DesktopIcon />
              Desktop
            </ViewportButton>
            <ViewportButton
              $active={currentViewport === 'tablet'}
              onClick={() => handleViewportChange('tablet')}
              title="Tablet view"
            >
              <TabletIcon />
              Tablet
            </ViewportButton>
            <ViewportButton
              $active={currentViewport === 'mobile'}
              onClick={() => handleViewportChange('mobile')}
              title="Mobile view"
            >
              <MobileIcon />
              Mobile
            </ViewportButton>
          </PreviewControls>
        </PreviewHeader>
      )}

      <PreviewViewport
        $width={currentDimensions.width}
        $height={currentDimensions.height}
        $currentViewport={currentViewport}
      >
        <PreviewFrame
          $width={currentDimensions.width}
          $height={currentDimensions.height}
          $viewport={currentViewport}
        >
          <PreviewContent>
            {renderPreviewContent()}
          </PreviewContent>
        </PreviewFrame>
      </PreviewViewport>
    </PreviewContainer>
  );
}
