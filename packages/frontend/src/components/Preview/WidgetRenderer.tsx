import React from 'react';
import styled from 'styled-components';
import { WidgetConfig } from '../../types/widget';
import reviewLogo from '../../assets/review-logo.jpg';

// Google-style colors
const GOOGLE_COLORS = {
  blue: '#4285f4',
  red: '#ea4335',
  yellow: '#fbbc04',
  green: '#34a853',
  purple: '#9c27b0',
  orange: '#ff5722',
  teal: '#009688',
  indigo: '#3f51b5',
  pink: '#e91e63',
  brown: '#795548',
  gray: '#9e9e9e',
  blueGray: '#607d8b'
};

// Mock review data with Google-style avatars and realistic content
const mockReviews = [
  {
    id: '1',
    authorName: '<PERSON>',
    authorPhotoUrl: null, // Will use initials
    avatarColor: GOOGLE_COLORS.purple,
    initials: 'S',
    rating: 5,
    text: 'Super fast delivery and we love our new toys. Thank you',
    publishedDate: '10 days ago',
    isVerified: true,
    location: 'Local Guide'
  },
  {
    id: '2',
    authorName: 'South Auckland - Manukau',
    authorPhotoUrl: null,
    avatarColor: GOOGLE_COLORS.indigo,
    initials: 'S',
    rating: 5,
    text: 'Easy as. Ordered online, paid and within a week parcel arrived. Thank you',
    publishedDate: '12 days ago',
    isVerified: true,
    location: ''
  },
  {
    id: '3',
    authorName: 'Hugh & Jane Masters',
    authorPhotoUrl: null,
    avatarColor: GOOGLE_COLORS.teal,
    initials: 'H',
    rating: 5,
    text: 'Easy website to browse. Great efficient service on ordering. Impressed by th...',
    publishedDate: '15 days ago',
    isVerified: true,
    location: '',
    hasReadMore: true
  },
  {
    id: '4',
    authorName: 'Sarah Wilson',
    authorPhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
    avatarColor: GOOGLE_COLORS.gray,
    initials: 'S',
    rating: 5,
    text: 'Fast delivery and packaged well. Love the addition of fruit burst too. It\'s the littl...',
    publishedDate: '17 days ago',
    isVerified: true,
    location: '',
    hasReadMore: true
  },
  {
    id: '5',
    authorName: 'Mike Chen',
    authorPhotoUrl: null,
    avatarColor: GOOGLE_COLORS.green,
    initials: 'M',
    rating: 4,
    text: 'Great experience overall. Professional service and reasonable pricing. Will definitely come back for future needs.',
    publishedDate: '1 week ago',
    isVerified: true,
    location: 'Local Guide'
  },
  {
    id: '6',
    authorName: 'Emma Davis',
    authorPhotoUrl: null,
    avatarColor: GOOGLE_COLORS.red,
    initials: 'E',
    rating: 5,
    text: 'Outstanding quality and attention to detail. The results exceeded our expectations.',
    publishedDate: '2 weeks ago',
    isVerified: false,
    location: ''
  }
];

// Header components
const WidgetHeader = styled.div<{
  $show: boolean;
  $color: string;
  $size: string;
  $align: string;
}>`
  display: ${props => props.$show ? 'block' : 'none'};
  color: ${props => props.$color};
  font-size: ${props => props.$size === 'small' ? '16px' : props.$size === 'medium' ? '20px' : '24px'};
  font-weight: 600;
  text-align: ${props => props.$align};
  margin-bottom: 8px;
  line-height: 1.2;
`;

const WidgetSubheader = styled.div<{
  $show: boolean;
  $color: string;
  $align: string;
}>`
  display: ${props => props.$show ? 'block' : 'none'};
  color: ${props => props.$color};
  font-size: 14px;
  text-align: ${props => props.$align};
  margin-bottom: 16px;
  opacity: 0.8;
  line-height: 1.3;
`;

// Base widget container
const WidgetContainer = styled.div<{
  $config: WidgetConfig;
  $viewport: string;
}>`
  width: ${props => props.$viewport === 'mobile' ? '100%' : props.$config.styling.dimensions.width};
  max-width: ${props => props.$viewport === 'mobile' ? '320px' : '100%'};
  height: auto;
  min-height: ${props => props.$config.styling.dimensions.height};
  background: ${props => props.$config.styling.colors.background};
  border-radius: ${props => props.$config.styling.dimensions.borderRadius};
  padding: ${props => props.$config.styling.spacing.padding};
  margin: ${props => props.$config.styling.spacing.margin};
  font-family: ${props => props.$config.styling.fonts.family};
  font-size: ${props => props.$viewport === 'mobile' ? '14px' : props.$config.styling.fonts.size};
  color: ${props => props.$config.styling.colors.text};
  border: 1px solid ${props => props.$config.styling.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
`;

const WidgetContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

// Google Reviews Style Components
const GoogleWidgetContainer = styled.div`
  width: 100%;
  background: white;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
`;

const GoogleHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8eaed;
`;

const GoogleHeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const GoogleLogo = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 400;
  color: #3c4043;
`;

const GoogleRating = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
`;

const GoogleRatingNumber = styled.span`
  font-size: 20px;
  font-weight: 400;
  color: #3c4043;
`;

const GoogleStars = styled.div`
  color: #fbbc04;
  font-size: 16px;
  letter-spacing: 1px;
`;

const GoogleReviewCount = styled.span`
  color: #5f6368;
  font-size: 14px;
  margin-left: 4px;
`;

const GoogleReviewButton = styled.button`
  background: #1a73e8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #1557b0;
  }
`;

const GoogleCarouselContainer = styled.div`
  position: relative;
  padding: 0 20px 16px;
`;

const GoogleCarouselWrapper = styled.div`
  display: flex;
  gap: 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 16px 0;
  
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
`;

const GoogleReviewCard = styled.div`
  min-width: 280px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8eaed;
`;

const GoogleReviewHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
`;

const GoogleAvatar = styled.div<{ $color: string; $hasPhoto?: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.$hasPhoto ? 'transparent' : props.$color};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 16px;
  overflow: hidden;
`;

const GoogleAvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
`;

const GoogleReviewAuthorInfo = styled.div`
  flex: 1;
`;

const GoogleAuthorName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: #3c4043;
  line-height: 1.2;
`;

const GoogleAuthorLocation = styled.div`
  font-size: 12px;
  color: #5f6368;
  margin-top: 2px;
`;

const GoogleReviewDate = styled.div`
  font-size: 12px;
  color: #5f6368;
`;

const GoogleReviewRating = styled.div`
  color: #fbbc04;
  font-size: 14px;
  margin-bottom: 8px;
  letter-spacing: 1px;
`;

const GoogleReviewText = styled.p`
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #3c4043;
`;

const GoogleReadMore = styled.span`
  color: #1a73e8;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    text-decoration: underline;
  }
`;

const GoogleReviewFooter = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
`;

const GoogleLogoSmall = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  color: #5f6368;
`;

const GoogleCarouselNav = styled.div`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border: 1px solid #dadce0;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
  
  &:hover {
    background: #f8f9fa;
  }
`;

const GoogleCarouselNavLeft = styled(GoogleCarouselNav)`
  left: -16px;
`;

const GoogleCarouselNavRight = styled(GoogleCarouselNav)`
  right: -16px;
`;

// Legacy components for other templates
const CarouselContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const CarouselHeader = styled.div<{ $config: WidgetConfig }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.$config.styling.spacing.gap};
  padding-bottom: 8px;
  border-bottom: 1px solid ${props => props.$config.styling.colors.border};
`;

const CarouselTitle = styled.h3<{ $config: WidgetConfig }>`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: ${props => props.$config.styling.colors.text};
`;

const CarouselNavigation = styled.div`
  display: flex;
  gap: 6px;
  align-items: center;
`;

const NavDot = styled.button<{ $active: boolean; $config: WidgetConfig }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: ${props => props.$active ? props.$config.styling.colors.primary : props.$config.styling.colors.border};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$config.styling.colors.primary};
  }
`;

const ReviewCard = styled.div<{ $config: WidgetConfig }>`
  flex: 1;
  padding: 12px;
  border: 1px solid ${props => props.$config.styling.colors.border};
  border-radius: 8px;
  background: ${props => props.$config.styling.colors.background};
`;

const ReviewHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
`;

const AuthorPhoto = styled.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
`;

const AuthorInfo = styled.div`
  flex: 1;
`;

const AuthorName = styled.div<{ $config: WidgetConfig }>`
  font-weight: 600;
  font-size: 14px;
  color: ${props => props.$config.styling.colors.text};
`;

const ReviewDate = styled.div<{ $config: WidgetConfig }>`
  font-size: 12px;
  color: ${props => props.$config.styling.colors.text}80;
`;

const Rating = styled.div<{ $config: WidgetConfig }>`
  color: ${props => props.$config.styling.colors.secondary};
  font-size: 14px;
`;

const ReviewText = styled.p<{ $config: WidgetConfig }>`
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: ${props => props.$config.styling.colors.text};
`;

// Badge Widget
const BadgeContainer = styled.div<{ $config: WidgetConfig }>`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: ${props => props.$config.styling.colors.background};
  border-radius: 24px;
  border: 1px solid ${props => props.$config.styling.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: fit-content;
`;

const BadgeRating = styled.div<{ $config: WidgetConfig }>`
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  color: ${props => props.$config.styling.colors.secondary};
`;

const BadgeText = styled.span<{ $config: WidgetConfig }>`
  color: ${props => props.$config.styling.colors.text};
  font-size: 14px;
`;

// Grid Widget
const GridContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const GridHeader = styled.div<{ $config: WidgetConfig }>`
  text-align: center;
  margin-bottom: ${props => props.$config.styling.spacing.gap};
  padding-bottom: 12px;
  border-bottom: 1px solid ${props => props.$config.styling.colors.border};
`;

const GridSummary = styled.div<{ $config: WidgetConfig }>`
  background: ${props => props.$config.styling.colors.border}40;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: ${props => props.$config.styling.spacing.gap};
  font-size: 12px;
  color: ${props => props.$config.styling.colors.text};
  text-align: center;
`;

const GridReviews = styled.div<{ $viewport: string }>`
  display: grid;
  grid-template-columns: ${props => props.$viewport === 'mobile' ? '1fr' : '1fr 1fr'};
  gap: 8px;
  flex: 1;
`;

const GridReviewCard = styled.div<{ $config: WidgetConfig }>`
  padding: 8px;
  border: 1px solid ${props => props.$config.styling.colors.border};
  border-radius: 6px;
  background: ${props => props.$config.styling.colors.background};
`;

// Utility function to render stars
const renderStars = (rating: number) => {
  return '★'.repeat(rating) + '☆'.repeat(5 - rating);
};

interface WidgetRendererProps {
  config: WidgetConfig;
  viewport: string;
}

export function WidgetRenderer({ config, viewport }: WidgetRendererProps) {
  const [currentReviewIndex, setCurrentReviewIndex] = React.useState(0);

  // Filter reviews based on settings
  const filteredReviews = mockReviews
    .filter(review => review.rating >= config.settings.minRating)
    .slice(0, config.settings.maxReviews);

  // Sort reviews based on settings
  const sortedReviews = [...filteredReviews].sort((a, b) => {
    switch (config.settings.sortBy) {
      case 'rating_high':
        return b.rating - a.rating;
      case 'rating_low':
        return a.rating - b.rating;
      case 'oldest':
        return new Date(a.publishedDate).getTime() - new Date(b.publishedDate).getTime();
      case 'newest':
      default:
        return new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime();
    }
  });

  const currentReview = sortedReviews[currentReviewIndex] || sortedReviews[0];
  const averageRating = sortedReviews.reduce((sum, review) => sum + review.rating, 0) / sortedReviews.length;

  const renderWidget = () => {
    switch (config.template) {
      case 'carousel':
        return (
          <GoogleWidgetContainer>
            <GoogleHeader>
              <GoogleHeaderLeft>
                <GoogleLogo>
                  <img src={reviewLogo} alt="Reviews" style={{ height: '24px', width: 'auto' }} />
                  <span style={{ marginLeft: '8px' }}>Reviews</span>
                </GoogleLogo>
                <GoogleRating>
                  <GoogleRatingNumber>{averageRating.toFixed(1)}</GoogleRatingNumber>
                  <GoogleStars>★★★★★</GoogleStars>
                  <GoogleReviewCount>({sortedReviews.length})</GoogleReviewCount>
                </GoogleRating>
              </GoogleHeaderLeft>
              <GoogleReviewButton>Review us on Google</GoogleReviewButton>
            </GoogleHeader>

            <GoogleCarouselContainer>
              <GoogleCarouselNavLeft>‹</GoogleCarouselNavLeft>
              <GoogleCarouselWrapper>
                {sortedReviews.map((review) => (
                  <GoogleReviewCard key={review.id}>
                    <GoogleReviewHeader>
                      <GoogleAvatar $color={review.avatarColor} $hasPhoto={!!review.authorPhotoUrl}>
                        {review.authorPhotoUrl ? (
                          <GoogleAvatarImage src={review.authorPhotoUrl} alt={review.authorName} />
                        ) : (
                          review.initials
                        )}
                      </GoogleAvatar>
                      <GoogleReviewAuthorInfo>
                        <GoogleAuthorName>{review.authorName}</GoogleAuthorName>
                        {review.location && (
                          <GoogleAuthorLocation>{review.location}</GoogleAuthorLocation>
                        )}
                      </GoogleReviewAuthorInfo>
                      {config.settings.showDates && (
                        <GoogleReviewDate>{review.publishedDate}</GoogleReviewDate>
                      )}
                    </GoogleReviewHeader>

                    <GoogleReviewRating>
                      {'★'.repeat(review.rating)}
                    </GoogleReviewRating>

                    <GoogleReviewText>
                      {review.text}
                      {review.hasReadMore && (
                        <>
                          {' '}
                          <GoogleReadMore>Read more</GoogleReadMore>
                        </>
                      )}
                    </GoogleReviewText>

                    <GoogleReviewFooter>
                      <GoogleLogoSmall>
                        <img src={reviewLogo} alt="Reviews" style={{ height: '16px', width: 'auto' }} />
                      </GoogleLogoSmall>
                    </GoogleReviewFooter>
                  </GoogleReviewCard>
                ))}
              </GoogleCarouselWrapper>
              <GoogleCarouselNavRight>›</GoogleCarouselNavRight>
            </GoogleCarouselContainer>
          </GoogleWidgetContainer>
        );

      case 'badge':
        return (
          <BadgeContainer $config={config}>
            <BadgeRating $config={config}>
              <span>★</span>
              <span>{averageRating.toFixed(1)}</span>
            </BadgeRating>
            <BadgeText $config={config}>
              Based on {sortedReviews.length} reviews
            </BadgeText>
          </BadgeContainer>
        );

      case 'grid':
        return (
          <GridContainer>
            <GridHeader $config={config}>
              <div style={{ fontSize: '16px', fontWeight: '600', marginBottom: '4px' }}>
                ★ {averageRating.toFixed(1)} ({sortedReviews.length} reviews)
              </div>
              <div style={{ fontSize: '14px', color: config.styling.colors.text + '80' }}>
                {config.business?.name || 'Business Reviews'}
              </div>
            </GridHeader>
            <GridSummary $config={config}>
              AI Summary: Customers consistently praise the excellent service and professional staff
            </GridSummary>
            <GridReviews $viewport={viewport}>
              {sortedReviews.slice(0, viewport === 'mobile' ? 2 : 4).map((review) => (
                <GridReviewCard key={review.id} $config={config}>
                  <ReviewHeader style={{ marginBottom: '6px' }}>
                    {config.settings.showPhotos && review.authorPhotoUrl && (
                      <AuthorPhoto
                        src={review.authorPhotoUrl}
                        alt={review.authorName}
                        style={{ width: '24px', height: '24px' }}
                      />
                    )}
                    <AuthorInfo>
                      <AuthorName $config={config} style={{ fontSize: '12px' }}>
                        {review.authorName}
                      </AuthorName>
                      {config.settings.showDates && (
                        <ReviewDate $config={config} style={{ fontSize: '10px' }}>
                          {review.publishedDate}
                        </ReviewDate>
                      )}
                    </AuthorInfo>
                    <Rating $config={config} style={{ fontSize: '12px' }}>
                      {renderStars(review.rating)}
                    </Rating>
                  </ReviewHeader>
                  <ReviewText $config={config} style={{ fontSize: '11px' }}>
                    {review.text.length > 80 ? review.text.substring(0, 80) + '...' : review.text}
                  </ReviewText>
                </GridReviewCard>
              ))}
            </GridReviews>
          </GridContainer>
        );

      case 'simple-carousel':
        return (
          <div style={{ textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            {currentReview && (
              <>
                <AuthorName $config={config} style={{ marginBottom: '8px', fontSize: '16px' }}>
                  {currentReview.authorName}
                </AuthorName>
                <Rating $config={config} style={{ marginBottom: '12px', fontSize: '18px' }}>
                  {renderStars(currentReview.rating)}
                </Rating>
                <ReviewText $config={config} style={{ fontStyle: 'italic', marginBottom: '16px' }}>
                  "{currentReview.text}"
                </ReviewText>
                <CarouselNavigation style={{ justifyContent: 'center' }}>
                  {sortedReviews.map((_, index) => (
                    <NavDot
                      key={index}
                      $active={index === currentReviewIndex}
                      $config={config}
                      onClick={() => setCurrentReviewIndex(index)}
                    />
                  ))}
                </CarouselNavigation>
              </>
            )}
          </div>
        );

      case 'slider':
        return (
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <div style={{ textAlign: 'center', marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>
              Recent Reviews
            </div>
            <div style={{
              display: 'flex',
              gap: '12px',
              overflowX: 'auto',
              paddingBottom: '8px',
              flex: 1
            }}>
              {sortedReviews.map((review) => (
                <div
                  key={review.id}
                  style={{
                    minWidth: viewport === 'mobile' ? '200px' : '150px',
                    padding: '12px',
                    border: `1px solid ${config.styling.colors.border}`,
                    borderRadius: '8px',
                    background: config.styling.colors.background,
                  }}
                >
                  <AuthorName $config={config} style={{ marginBottom: '4px', fontSize: '12px' }}>
                    {review.authorName}
                  </AuthorName>
                  <Rating $config={config} style={{ marginBottom: '6px', fontSize: '12px' }}>
                    {renderStars(review.rating)}
                  </Rating>
                  <ReviewText $config={config} style={{ fontSize: '10px' }}>
                    {review.text.substring(0, 60)}...
                  </ReviewText>
                </div>
              ))}
            </div>
          </div>
        );

      case 'floating-badge':
        return (
          <div style={{
            position: 'relative',
            width: '100%',
            height: '100%',
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            borderRadius: config.styling.dimensions.borderRadius,
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: '12px',
              left: '12px',
              fontSize: '12px',
              color: '#666',
              background: 'rgba(255,255,255,0.9)',
              padding: '4px 8px',
              borderRadius: '4px'
            }}>
              Your Website Content
            </div>
            <div style={{
              position: 'absolute',
              bottom: '16px',
              right: '16px',
              background: config.styling.colors.background,
              borderRadius: '50%',
              width: '60px',
              height: '60px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              cursor: 'pointer',
              transition: 'transform 0.2s ease',
            }}>
              <div style={{
                color: config.styling.colors.secondary,
                fontWeight: '600',
                fontSize: '14px'
              }}>
                ★ {averageRating.toFixed(1)}
              </div>
              <div style={{
                color: config.styling.colors.text,
                fontSize: '10px'
              }}>
                {sortedReviews.length} reviews
              </div>
            </div>
          </div>
        );

      default:
        return <div>Template not found</div>;
    }
  };

  // Get header settings with defaults
  const headerSettings = config.headerSettings || {
    showHeader: true,
    headerText: config.business?.name ? `${config.business.name} Reviews` : 'Customer Reviews',
    headerSize: 'medium',
    headerColor: '#333333',
    headerAlign: 'center',
    showSubheader: true,
    subheaderText: 'What our customers are saying',
    subheaderColor: '#666666',
    subheaderAlign: 'center',
  };

  // For carousel template, render directly without wrapper
  if (config.template === 'carousel') {
    return renderWidget();
  }

  // For other templates, keep the wrapper but make it 100% width
  return (
    <div style={{ width: '100%' }}>
      <WidgetHeader
        $show={headerSettings.showHeader}
        $color={headerSettings.headerColor}
        $size={headerSettings.headerSize}
        $align={headerSettings.headerAlign}
      >
        {headerSettings.headerText}
      </WidgetHeader>
      <WidgetSubheader
        $show={headerSettings.showSubheader}
        $color={headerSettings.subheaderColor}
        $align={headerSettings.subheaderAlign}
      >
        {headerSettings.subheaderText}
      </WidgetSubheader>
      {renderWidget()}
    </div>
  );
}
