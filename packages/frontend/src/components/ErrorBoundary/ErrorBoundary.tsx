import React, { Component, ErrorInfo, ReactNode } from 'react';
import styled from 'styled-components';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 32px;
  text-align: center;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 16px;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  color: #dc2626;
  margin-bottom: 16px;
`;

const ErrorTitle = styled.h2`
  color: #dc2626;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
`;

const ErrorMessage = styled.p`
  color: #7f1d1d;
  font-size: 16px;
  margin: 0 0 24px 0;
  max-width: 600px;
  line-height: 1.5;
`;

const ErrorActions = styled.div`
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.primary {
    background-color: #dc2626;
    color: white;

    &:hover {
      background-color: #b91c1c;
    }
  }

  &.secondary {
    background-color: white;
    color: #dc2626;
    border: 1px solid #dc2626;

    &:hover {
      background-color: #fef2f2;
    }
  }
`;

const ErrorDetails = styled.details`
  margin-top: 24px;
  text-align: left;
  max-width: 800px;
  width: 100%;

  summary {
    cursor: pointer;
    color: #7f1d1d;
    font-weight: 500;
    margin-bottom: 8px;
  }

  pre {
    background-color: #f3f4f6;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    color: #374151;
    white-space: pre-wrap;
    word-break: break-word;
  }
`;

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error);
      console.error('Error info:', errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorInfo);
    }
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to a monitoring service
    // like Sentry, LogRocket, or Bugsnag
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      // Example: Send to monitoring service
      // monitoringService.captureException(error, { extra: errorData });
      
      console.error('Error logged to monitoring service:', errorData);
    } catch (loggingError) {
      console.error('Failed to log error to monitoring service:', loggingError);
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <ErrorContainer>
          <ErrorIcon>⚠️</ErrorIcon>
          <ErrorTitle>Something went wrong</ErrorTitle>
          <ErrorMessage>
            We're sorry, but something unexpected happened. The error has been logged 
            and our team has been notified. Please try refreshing the page or go back to the home page.
          </ErrorMessage>
          
          <ErrorActions>
            <Button className="primary" onClick={this.handleReload}>
              Refresh Page
            </Button>
            <Button className="secondary" onClick={this.handleReset}>
              Try Again
            </Button>
            <Button className="secondary" onClick={this.handleGoHome}>
              Go Home
            </Button>
          </ErrorActions>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <ErrorDetails>
              <summary>Error Details (Development Only)</summary>
              <div>
                <strong>Error:</strong>
                <pre>{this.state.error.message}</pre>
                {this.state.error.stack && (
                  <>
                    <strong>Stack Trace:</strong>
                    <pre>{this.state.error.stack}</pre>
                  </>
                )}
                {this.state.errorInfo?.componentStack && (
                  <>
                    <strong>Component Stack:</strong>
                    <pre>{this.state.errorInfo.componentStack}</pre>
                  </>
                )}
              </div>
            </ErrorDetails>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;