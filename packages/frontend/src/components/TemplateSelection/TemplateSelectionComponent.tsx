import React from 'react';
import styled from 'styled-components';
import { useWidget } from '../../context/WidgetContext';
import { TemplateType } from '../../types/widget';
import { TemplatePreview } from './TemplatePreview';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
  text-align: center;
`;

const Description = styled.p`
  font-size: 16px;
  color: #666666;
  margin: 0 0 32px 0;
  text-align: center;
  line-height: 1.6;
`;

const TemplatesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const TemplateCard = styled.div<{ $isSelected: boolean }>`
  background: #ffffff;
  border: 2px solid ${props => props.$isSelected ? '#4285f4' : '#e0e0e0'};
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    border-color: ${props => props.$isSelected ? '#4285f4' : '#999999'};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  ${props => props.$isSelected && `
    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.2);
  `}
`;

const SelectedBadge = styled.div`
  position: absolute;
  top: 12px;
  right: 12px;
  background: #4285f4;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
`;

const TemplateName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 8px 0;
`;

const TemplateDescription = styled.p`
  font-size: 14px;
  color: #666666;
  margin: 0 0 16px 0;
  line-height: 1.4;
`;

const PreviewContainer = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

// Template definitions with descriptions
const templates: Array<{
  type: TemplateType;
  name: string;
  description: string;
}> = [
  {
    type: 'carousel',
    name: 'Carousel Widget',
    description: 'Interactive carousel with navigation arrows and smooth transitions between reviews.'
  },
  {
    type: 'badge',
    name: 'Badge',
    description: 'Compact badge showing overall rating and review count, perfect for headers or sidebars.'
  },
  {
    type: 'grid',
    name: 'Grid with AI Summary',
    description: 'Grid layout with AI-generated summary highlighting key themes from customer reviews.'
  },
  {
    type: 'simple-carousel',
    name: 'Simple Carousel',
    description: 'Clean, minimal carousel design focusing on review content without distractions.'
  },
  {
    type: 'slider',
    name: 'Slider',
    description: 'Horizontal slider with smooth auto-play and manual navigation controls.'
  },
  {
    type: 'floating-badge',
    name: 'Floating Badge',
    description: 'Floating badge that can be positioned anywhere on your page with customizable placement.'
  }
];

export function TemplateSelectionComponent() {
  const { config, updateConfig } = useWidget();

  const handleTemplateSelect = (templateType: TemplateType) => {
    updateConfig({ template: templateType });
  };

  return (
    <Container>
      <Title>Choose Your Template</Title>
      <Description>
        Select from our collection of professionally designed widget templates to match your website's style.
      </Description>
      
      <TemplatesGrid>
        {templates.map((template) => (
          <TemplateCard
            key={template.type}
            $isSelected={config.template === template.type}
            onClick={() => handleTemplateSelect(template.type)}
          >
            {config.template === template.type && (
              <SelectedBadge>Selected</SelectedBadge>
            )}
            
            <TemplateName>{template.name}</TemplateName>
            <TemplateDescription>{template.description}</TemplateDescription>
            
            <PreviewContainer>
              <TemplatePreview 
                templateType={template.type}
                isSelected={config.template === template.type}
              />
            </PreviewContainer>
          </TemplateCard>
        ))}
      </TemplatesGrid>
    </Container>
  );
}