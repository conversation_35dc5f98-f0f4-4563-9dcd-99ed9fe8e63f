import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { WidgetProvider } from './context/WidgetContext';
import { Layout } from './components/Layout/Layout';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';

// Lazy load page components for code splitting
const SourcePage = React.lazy(() => import('./pages/SourcePage').then(module => ({ default: module.SourcePage })));
const LayoutPage = React.lazy(() => import('./pages/LayoutPage').then(module => ({ default: module.LayoutPage })));
const HeaderPage = React.lazy(() => import('./pages/HeaderPage').then(module => ({ default: module.HeaderPage })));
const ReviewsPage = React.lazy(() => import('./pages/ReviewsPage').then(module => ({ default: module.ReviewsPage })));
const StylePage = React.lazy(() => import('./pages/StylePage').then(module => ({ default: module.StylePage })));
const SettingsPage = React.lazy(() => import('./pages/SettingsPage').then(module => ({ default: module.SettingsPage })));

// Loading component for Suspense fallback
const PageLoader: React.FC = () => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '200px',
    fontSize: '16px',
    color: '#666'
  }}>
    <div style={{
      width: '24px',
      height: '24px',
      border: '2px solid #f3f3f3',
      borderTop: '2px solid #3498db',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginRight: '12px'
    }}></div>
    Loading...
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

function App() {
  return (
    <ErrorBoundary onError={(error, errorInfo) => {
      // Log to monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        console.error('Global app error:', error, errorInfo);
        // Example: Send to monitoring service
        // monitoringService.captureException(error, { extra: errorInfo });
      }
    }}>
      <WidgetProvider>
        <Router>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={
                <Suspense fallback={<PageLoader />}>
                  <SourcePage />
                </Suspense>
              } />
              <Route path="layout" element={
                <Suspense fallback={<PageLoader />}>
                  <LayoutPage />
                </Suspense>
              } />
              <Route path="header" element={
                <Suspense fallback={<PageLoader />}>
                  <HeaderPage />
                </Suspense>
              } />
              <Route path="reviews" element={
                <Suspense fallback={<PageLoader />}>
                  <ReviewsPage />
                </Suspense>
              } />
              <Route path="style" element={
                <Suspense fallback={<PageLoader />}>
                  <StylePage />
                </Suspense>
              } />
              <Route path="settings" element={
                <Suspense fallback={<PageLoader />}>
                  <SettingsPage />
                </Suspense>
              } />
            </Route>
          </Routes>
        </Router>
      </WidgetProvider>
    </ErrorBoundary>
  );
}

export default App;