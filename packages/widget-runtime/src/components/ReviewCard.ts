// ReviewCard component for individual review display
import { imageLoader } from '../utils/image-loader.js';

export interface Review {
  id: string;
  authorName: string;
  authorPhotoUrl?: string;
  rating: number;
  text: string;
  publishedDate: Date;
  isVerified: boolean;
}

export interface ReviewCardConfig {
  showPhotos: boolean;
  showDates: boolean;
  maxTextLength: number;
  theme: 'light' | 'dark';
}

export class ReviewCard {
  private review: Review;
  private config: ReviewCardConfig;
  private element: HTMLElement;
  private isExpanded: boolean = false;

  constructor(review: Review, config: ReviewCardConfig) {
    this.review = review;
    this.config = config;
    this.element = this.createElement();
  }

  private createElement(): HTMLElement {
    const card = document.createElement('div');
    card.className = 'grw-review-card';
    card.setAttribute('data-review-id', this.review.id);
    
    card.innerHTML = this.getCardHTML();
    this.attachEventListeners(card);
    this.loadOptimizedImages(card);
    
    return card;
  }

  private getCardHTML(): string {
    const truncatedText = this.getTruncatedText();
    const needsExpansion = this.review.text.length > this.config.maxTextLength;
    const formattedDate = this.formatDate(this.review.publishedDate);

    // Create the card structure first
    const cardHTML = `
      <div class="grw-review-header">
        ${this.config.showPhotos && this.review.authorPhotoUrl ? `
          <div class="grw-author-photo" data-photo-container="true"></div>
        ` : ''}
        <div class="grw-author-info">
          <div class="grw-author-name">
            ${this.escapeHtml(this.review.authorName)}
            ${this.review.isVerified ? '<span class="grw-verified-badge">✓</span>' : ''}
          </div>
          ${this.config.showDates ? `
            <div class="grw-review-date">${formattedDate}</div>
          ` : ''}
        </div>
        <div class="grw-rating">
          ${this.generateStarRating(this.review.rating)}
        </div>
      </div>
      <div class="grw-review-content">
        <div class="grw-review-text ${needsExpansion ? 'grw-expandable' : ''}">
          <span class="grw-text-content">${this.escapeHtml(truncatedText)}</span>
          ${needsExpansion ? `
            <button class="grw-expand-btn" type="button" aria-label="Expand review">
              <span class="grw-expand-text">Show more</span>
              <span class="grw-collapse-text" style="display: none;">Show less</span>
            </button>
          ` : ''}
        </div>
      </div>
    `;

    return cardHTML;
  }

  private getTruncatedText(): string {
    if (this.review.text.length <= this.config.maxTextLength) {
      return this.review.text;
    }
    
    // Find the last space before the limit to avoid cutting words
    const truncated = this.review.text.substring(0, this.config.maxTextLength);
    const lastSpace = truncated.lastIndexOf(' ');
    
    return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated + '...';
  }

  private generateStarRating(rating: number): string {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let starsHTML = '';
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
      starsHTML += '<span class="grw-star grw-star-full">★</span>';
    }
    
    // Half star
    if (hasHalfStar) {
      starsHTML += '<span class="grw-star grw-star-half">★</span>';
    }
    
    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
      starsHTML += '<span class="grw-star grw-star-empty">☆</span>';
    }
    
    return `<div class="grw-stars" aria-label="${rating} out of 5 stars">${starsHTML}</div>`;
  }

  private formatDate(date: Date): string {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  private loadOptimizedImages(card: HTMLElement): void {
    if (!this.config.showPhotos || !this.review.authorPhotoUrl) return;

    const photoContainer = card.querySelector('[data-photo-container="true"]') as HTMLElement;
    if (!photoContainer) return;

    const optimizedImg = imageLoader.createOptimizedImage({
      src: this.review.authorPhotoUrl,
      alt: this.review.authorName,
      width: 40,
      height: 40,
      loading: 'lazy',
      onLoad: () => {
        // Add fade-in animation when image loads
        optimizedImg.style.opacity = '0';
        optimizedImg.style.transition = 'opacity 0.3s ease';
        setTimeout(() => {
          optimizedImg.style.opacity = '1';
        }, 10);
      },
      onError: () => {
        // Use initials as fallback
        photoContainer.innerHTML = `
          <div style="
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            color: #666;
          ">
            ${this.getInitials(this.review.authorName)}
          </div>
        `;
      }
    });

    photoContainer.appendChild(optimizedImg);
  }

  private getInitials(name: string): string {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .substring(0, 2)
      .toUpperCase();
  }

  private attachEventListeners(card: HTMLElement): void {
    const expandBtn = card.querySelector('.grw-expand-btn') as HTMLButtonElement;
    if (expandBtn) {
      expandBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleExpansion();
      });
    }
  }

  private toggleExpansion(): void {
    const textContent = this.element.querySelector('.grw-text-content') as HTMLElement;
    const expandText = this.element.querySelector('.grw-expand-text') as HTMLElement;
    const collapseText = this.element.querySelector('.grw-collapse-text') as HTMLElement;
    const expandBtn = this.element.querySelector('.grw-expand-btn') as HTMLButtonElement;

    if (!textContent || !expandText || !collapseText || !expandBtn) return;

    this.isExpanded = !this.isExpanded;

    // Add transition class for smooth animation
    textContent.classList.add('grw-transitioning');

    if (this.isExpanded) {
      textContent.textContent = this.review.text;
      expandText.style.display = 'none';
      collapseText.style.display = 'inline';
      expandBtn.setAttribute('aria-label', 'Collapse review');
      this.element.classList.add('grw-expanded');
    } else {
      textContent.textContent = this.getTruncatedText();
      expandText.style.display = 'inline';
      collapseText.style.display = 'none';
      expandBtn.setAttribute('aria-label', 'Expand review');
      this.element.classList.remove('grw-expanded');
    }

    // Remove transition class after animation
    setTimeout(() => {
      textContent.classList.remove('grw-transitioning');
    }, 300);
  }

  public getElement(): HTMLElement {
    return this.element;
  }

  public getReview(): Review {
    return this.review;
  }

  public updateConfig(newConfig: Partial<ReviewCardConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.element.innerHTML = this.getCardHTML();
    this.attachEventListeners(this.element);
  }

  public destroy(): void {
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}