// ReviewsDisplay component for handling different layout types
import { ReviewCard, Review, ReviewCardConfig } from './ReviewCard.js';

export type LayoutType = 'carousel' | 'grid' | 'list';

export interface ReviewsDisplayConfig {
  layout: LayoutType;
  maxReviews: number;
  reviewCardConfig: ReviewCardConfig;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showNavigation?: boolean;
  showPagination?: boolean;
  itemsPerPage?: number;
  gridColumns?: number;
}

export class ReviewsDisplay {
  private reviews: Review[];
  private config: ReviewsDisplayConfig;
  private element: HTMLElement;
  private reviewCards: ReviewCard[] = [];
  private currentPage: number = 0;
  private autoPlayTimer?: number;
  private isTransitioning: boolean = false;

  constructor(reviews: Review[], config: ReviewsDisplayConfig) {
    this.reviews = reviews.slice(0, config.maxReviews);
    this.config = config;
    this.element = this.createElement();
    this.init();
  }

  private createElement(): HTMLElement {
    const container = document.createElement('div');
    container.className = `grw-reviews-display grw-layout-${this.config.layout}`;
    
    container.innerHTML = this.getContainerHTML();
    this.attachEventListeners(container);
    
    return container;
  }

  private getContainerHTML(): string {
    const showNavButtons = this.config.showNavigation && (
      this.config.layout === 'carousel' || 
      (this.config.showPagination && this.hasMultiplePages())
    );

    return `
      <div class="grw-reviews-container">
        ${showNavButtons ? `
          <button class="grw-nav-btn grw-nav-prev" type="button" aria-label="Previous reviews">
            <span class="grw-nav-icon">‹</span>
          </button>
        ` : ''}
        
        <div class="grw-reviews-viewport">
          <div class="grw-reviews-track"></div>
        </div>
        
        ${showNavButtons ? `
          <button class="grw-nav-btn grw-nav-next" type="button" aria-label="Next reviews">
            <span class="grw-nav-icon">›</span>
          </button>
        ` : ''}
      </div>
      
      ${this.config.showPagination ? `
        <div class="grw-pagination">
          <div class="grw-pagination-dots"></div>
        </div>
      ` : ''}
    `;
  }

  private hasMultiplePages(): boolean {
    let itemsPerPage: number;

    switch (this.config.layout) {
      case 'carousel':
        itemsPerPage = this.config.itemsPerPage || 1;
        break;
      case 'grid':
        const columns = this.config.gridColumns || 2;
        itemsPerPage = this.config.itemsPerPage || (columns * 2);
        break;
      case 'list':
        itemsPerPage = this.config.itemsPerPage || 5;
        break;
    }

    return this.reviews.length > itemsPerPage;
  }

  private init(): void {
    this.createReviewCards();
    this.renderReviews();
    this.setupPagination();
    this.setupAutoPlay();
    this.setupTouchGestures();
  }

  private createReviewCards(): void {
    this.reviewCards = this.reviews.map(review => 
      new ReviewCard(review, this.config.reviewCardConfig)
    );
  }

  private renderReviews(): void {
    const track = this.element.querySelector('.grw-reviews-track') as HTMLElement;
    if (!track) return;

    // Clear existing content
    track.innerHTML = '';

    switch (this.config.layout) {
      case 'carousel':
        this.renderCarousel(track);
        break;
      case 'grid':
        this.renderGrid(track);
        break;
      case 'list':
        this.renderList(track);
        break;
    }
  }

  private renderCarousel(track: HTMLElement): void {
    const itemsPerPage = this.config.itemsPerPage || 1;
    const totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);

    // Create pages
    for (let page = 0; page < totalPages; page++) {
      const pageElement = document.createElement('div');
      pageElement.className = 'grw-carousel-page';
      
      const startIndex = page * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, this.reviewCards.length);
      
      for (let i = startIndex; i < endIndex; i++) {
        const cardWrapper = document.createElement('div');
        cardWrapper.className = 'grw-carousel-item';
        cardWrapper.appendChild(this.reviewCards[i].getElement());
        pageElement.appendChild(cardWrapper);
      }
      
      track.appendChild(pageElement);
    }

    // Set initial position (ensure transform is set)
    track.style.transform = 'translateX(0%)';
  }

  private renderGrid(track: HTMLElement): void {
    const columns = this.config.gridColumns || 2;
    const itemsPerPage = this.config.itemsPerPage || (columns * 2); // Default to 2 rows
    
    if (this.config.showPagination && this.reviewCards.length > itemsPerPage) {
      this.renderPaginatedGrid(track, columns, itemsPerPage);
    } else {
      track.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
      this.reviewCards.forEach(card => {
        const cardWrapper = document.createElement('div');
        cardWrapper.className = 'grw-grid-item';
        cardWrapper.appendChild(card.getElement());
        track.appendChild(cardWrapper);
      });
    }
  }

  private renderList(track: HTMLElement): void {
    const itemsPerPage = this.config.itemsPerPage || 5; // Default to 5 items per page for list
    
    if (this.config.showPagination && this.reviewCards.length > itemsPerPage) {
      this.renderPaginatedList(track, itemsPerPage);
    } else {
      this.reviewCards.forEach(card => {
        const cardWrapper = document.createElement('div');
        cardWrapper.className = 'grw-list-item';
        cardWrapper.appendChild(card.getElement());
        track.appendChild(cardWrapper);
      });
    }
  }

  private renderPaginatedGrid(track: HTMLElement, columns: number, itemsPerPage: number): void {
    const totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
    track.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    
    // Create pages for grid layout
    for (let page = 0; page < totalPages; page++) {
      const pageElement = document.createElement('div');
      pageElement.className = 'grw-grid-page';
      pageElement.style.display = page === 0 ? 'contents' : 'none';
      
      const startIndex = page * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, this.reviewCards.length);
      
      for (let i = startIndex; i < endIndex; i++) {
        const cardWrapper = document.createElement('div');
        cardWrapper.className = 'grw-grid-item';
        cardWrapper.appendChild(this.reviewCards[i].getElement());
        pageElement.appendChild(cardWrapper);
      }
      
      track.appendChild(pageElement);
    }
  }

  private renderPaginatedList(track: HTMLElement, itemsPerPage: number): void {
    const totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
    
    // Create pages for list layout
    for (let page = 0; page < totalPages; page++) {
      const pageElement = document.createElement('div');
      pageElement.className = 'grw-list-page';
      pageElement.style.display = page === 0 ? 'block' : 'none';
      
      const startIndex = page * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, this.reviewCards.length);
      
      for (let i = startIndex; i < endIndex; i++) {
        const cardWrapper = document.createElement('div');
        cardWrapper.className = 'grw-list-item';
        cardWrapper.appendChild(this.reviewCards[i].getElement());
        pageElement.appendChild(cardWrapper);
      }
      
      track.appendChild(pageElement);
    }
  }

  private setupPagination(): void {
    if (!this.config.showPagination) return;

    const dotsContainer = this.element.querySelector('.grw-pagination-dots') as HTMLElement;
    if (!dotsContainer) return;

    let itemsPerPage: number;
    let totalPages: number;

    switch (this.config.layout) {
      case 'carousel':
        itemsPerPage = this.config.itemsPerPage || 1;
        break;
      case 'grid':
        const columns = this.config.gridColumns || 2;
        itemsPerPage = this.config.itemsPerPage || (columns * 2);
        break;
      case 'list':
        itemsPerPage = this.config.itemsPerPage || 5;
        break;
    }

    totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);

    // Only show pagination if there are multiple pages
    if (totalPages <= 1) {
      dotsContainer.style.display = 'none';
      return;
    }

    dotsContainer.style.display = 'flex';
    dotsContainer.innerHTML = '';

    for (let i = 0; i < totalPages; i++) {
      const dot = document.createElement('button');
      dot.className = `grw-pagination-dot ${i === 0 ? 'grw-active' : ''}`;
      dot.setAttribute('type', 'button');
      dot.setAttribute('aria-label', `Go to page ${i + 1}`);
      dot.addEventListener('click', () => this.goToPage(i));
      dotsContainer.appendChild(dot);
    }
  }

  private setupAutoPlay(): void {
    if (!this.config.autoPlay || this.config.layout !== 'carousel') return;

    const interval = this.config.autoPlayInterval || 5000;
    
    // Add a small delay before starting auto-play to allow for proper initialization
    setTimeout(() => {
      if (this.config.autoPlay && this.config.layout === 'carousel' && !this.autoPlayTimer) {
        this.autoPlayTimer = window.setInterval(() => {
          if (!this.isTransitioning) {
            this.nextPage();
          }
        }, interval);
      }
    }, 100);

    // Pause on hover
    this.element.addEventListener('mouseenter', () => this.pauseAutoPlay());
    this.element.addEventListener('mouseleave', () => this.resumeAutoPlay());
  }

  private setupTouchGestures(): void {
    // Only enable touch gestures if there are multiple pages
    if (!this.hasMultiplePages()) return;

    let startX = 0;
    let startY = 0;
    let isDragging = false;

    const track = this.element.querySelector('.grw-reviews-track') as HTMLElement;
    if (!track) return;

    track.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      isDragging = true;
      
      // Only pause autoplay for carousel
      if (this.config.layout === 'carousel') {
        this.pauseAutoPlay();
      }
    }, { passive: true });

    track.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const diffX = Math.abs(currentX - startX);
      const diffY = Math.abs(currentY - startY);

      // For carousel, prevent vertical scrolling if horizontal swipe is detected
      // For grid/list, allow more lenient swipe detection
      const threshold = this.config.layout === 'carousel' ? 10 : 20;
      
      if (diffX > diffY && diffX > threshold) {
        e.preventDefault();
      }
    }, { passive: false });

    track.addEventListener('touchend', (e) => {
      if (!isDragging) return;
      
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const diffX = startX - endX;
      const diffY = startY - endY;
      
      // Different thresholds for different layouts
      const horizontalThreshold = this.config.layout === 'carousel' ? 50 : 80;
      const verticalThreshold = 60;

      // Handle horizontal swipes (all layouts)
      if (Math.abs(diffX) > horizontalThreshold && Math.abs(diffX) > Math.abs(diffY)) {
        if (diffX > 0) {
          this.nextPage();
        } else {
          this.previousPage();
        }
      }
      // Handle vertical swipes (grid and list layouts only)
      else if (this.config.layout !== 'carousel' && Math.abs(diffY) > verticalThreshold) {
        if (diffY > 0) {
          this.nextPage(); // Swipe up = next page
        } else {
          this.previousPage(); // Swipe down = previous page
        }
      }

      isDragging = false;
      
      // Only resume autoplay for carousel
      if (this.config.layout === 'carousel') {
        this.resumeAutoPlay();
      }
    }, { passive: true });
  }

  private attachEventListeners(container: HTMLElement): void {
    const prevBtn = container.querySelector('.grw-nav-prev') as HTMLButtonElement;
    const nextBtn = container.querySelector('.grw-nav-next') as HTMLButtonElement;

    if (prevBtn) {
      prevBtn.addEventListener('click', () => this.previousPage());
    }

    if (nextBtn) {
      nextBtn.addEventListener('click', () => this.nextPage());
    }

    // Enhanced keyboard navigation for accessibility (all layouts)
    container.addEventListener('keydown', (e) => {
      // Only handle navigation if there are multiple pages
      if (!this.hasMultiplePages()) return;

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          this.previousPage();
          this.announcePageChange();
          break;
        case 'ArrowRight':
          e.preventDefault();
          this.nextPage();
          this.announcePageChange();
          break;
        case 'Home':
          e.preventDefault();
          this.goToPage(0);
          this.announcePageChange();
          break;
        case 'End':
          e.preventDefault();
          let itemsPerPage: number;
          switch (this.config.layout) {
            case 'carousel':
              itemsPerPage = this.config.itemsPerPage || 1;
              break;
            case 'grid':
              const columns = this.config.gridColumns || 2;
              itemsPerPage = this.config.itemsPerPage || (columns * 2);
              break;
            case 'list':
              itemsPerPage = this.config.itemsPerPage || 5;
              break;
          }
          const totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
          this.goToPage(totalPages - 1);
          this.announcePageChange();
          break;
        case 'PageUp':
          e.preventDefault();
          this.previousPage();
          this.announcePageChange();
          break;
        case 'PageDown':
          e.preventDefault();
          this.nextPage();
          this.announcePageChange();
          break;
      }
    });

    // Make container focusable for keyboard navigation (all layouts with pagination)
    if (this.hasMultiplePages()) {
      container.setAttribute('tabindex', '0');
      container.setAttribute('role', 'region');
      
      const layoutLabel = this.config.layout === 'carousel' ? 'carousel' : 
                         this.config.layout === 'grid' ? 'grid' : 'list';
      container.setAttribute('aria-label', `Reviews ${layoutLabel}`);
      container.setAttribute('aria-live', 'polite');
      
      // Add keyboard navigation instructions
      const instructions = this.config.layout === 'carousel' 
        ? 'Use arrow keys to navigate between pages, Home/End to go to first/last page'
        : 'Use arrow keys or Page Up/Down to navigate between pages, Home/End to go to first/last page';
      container.setAttribute('aria-describedby', 'grw-nav-instructions');
      
      // Add hidden instructions for screen readers
      const instructionsElement = document.createElement('div');
      instructionsElement.id = 'grw-nav-instructions';
      instructionsElement.className = 'grw-sr-only';
      instructionsElement.textContent = instructions;
      instructionsElement.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
      container.appendChild(instructionsElement);
    }
  }

  private goToPage(pageIndex: number): void {
    if (this.isTransitioning) return;

    let itemsPerPage: number;
    let totalPages: number;

    switch (this.config.layout) {
      case 'carousel':
        itemsPerPage = this.config.itemsPerPage || 1;
        break;
      case 'grid':
        const columns = this.config.gridColumns || 2;
        itemsPerPage = this.config.itemsPerPage || (columns * 2);
        break;
      case 'list':
        itemsPerPage = this.config.itemsPerPage || 5;
        break;
    }

    totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
    this.currentPage = Math.max(0, Math.min(pageIndex, totalPages - 1));

    switch (this.config.layout) {
      case 'carousel':
        this.updateCarouselPosition();
        break;
      case 'grid':
        this.updateGridPosition();
        break;
      case 'list':
        this.updateListPosition();
        break;
    }

    this.updatePaginationDots();
  }

  private nextPage(): void {
    let itemsPerPage: number;
    let totalPages: number;

    switch (this.config.layout) {
      case 'carousel':
        itemsPerPage = this.config.itemsPerPage || 1;
        break;
      case 'grid':
        const columns = this.config.gridColumns || 2;
        itemsPerPage = this.config.itemsPerPage || (columns * 2);
        break;
      case 'list':
        itemsPerPage = this.config.itemsPerPage || 5;
        break;
    }

    totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
    
    if (totalPages <= 1) return;

    const nextPage = this.config.layout === 'carousel' 
      ? (this.currentPage + 1) % totalPages  // Carousel loops
      : Math.min(this.currentPage + 1, totalPages - 1);  // Grid/List stops at end
    
    this.goToPage(nextPage);
  }

  private previousPage(): void {
    let itemsPerPage: number;
    let totalPages: number;

    switch (this.config.layout) {
      case 'carousel':
        itemsPerPage = this.config.itemsPerPage || 1;
        break;
      case 'grid':
        const columns = this.config.gridColumns || 2;
        itemsPerPage = this.config.itemsPerPage || (columns * 2);
        break;
      case 'list':
        itemsPerPage = this.config.itemsPerPage || 5;
        break;
    }

    totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
    
    if (totalPages <= 1) return;

    const prevPage = this.config.layout === 'carousel'
      ? (this.currentPage === 0 ? totalPages - 1 : this.currentPage - 1)  // Carousel loops
      : Math.max(this.currentPage - 1, 0);  // Grid/List stops at beginning
    
    this.goToPage(prevPage);
  }

  private updateCarouselPosition(): void {
    const track = this.element.querySelector('.grw-reviews-track') as HTMLElement;
    if (!track) return;

    this.isTransitioning = true;
    
    const translateX = -this.currentPage * 100;
    track.style.transform = `translateX(${translateX}%)`;

    // Reset transition flag after animation
    setTimeout(() => {
      this.isTransitioning = false;
    }, 300);
  }

  private updateGridPosition(): void {
    const track = this.element.querySelector('.grw-reviews-track') as HTMLElement;
    if (!track) return;

    this.isTransitioning = true;

    const pages = track.querySelectorAll('.grw-grid-page') as NodeListOf<HTMLElement>;
    pages.forEach((page, index) => {
      if (index === this.currentPage) {
        page.style.display = 'contents';
        page.style.opacity = '1';
      } else {
        page.style.display = 'none';
        page.style.opacity = '0';
      }
    });

    // Reset transition flag immediately for grid (no animation delay)
    this.isTransitioning = false;
  }

  private updateListPosition(): void {
    const track = this.element.querySelector('.grw-reviews-track') as HTMLElement;
    if (!track) return;

    this.isTransitioning = true;

    const pages = track.querySelectorAll('.grw-list-page') as NodeListOf<HTMLElement>;
    pages.forEach((page, index) => {
      if (index === this.currentPage) {
        page.style.display = 'block';
        page.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        page.style.opacity = '1';
        page.style.transform = 'translateY(0)';
      } else {
        page.style.display = 'none';
        page.style.opacity = '0';
        page.style.transform = 'translateY(-20px)';
      }
    });

    // Reset transition flag immediately for list (tests expect synchronous behavior)
    this.isTransitioning = false;
  }

  private updatePaginationDots(): void {
    const dots = this.element.querySelectorAll('.grw-pagination-dot');
    dots.forEach((dot, index) => {
      dot.classList.toggle('grw-active', index === this.currentPage);
    });
  }

  private pauseAutoPlay(): void {
    if (this.autoPlayTimer) {
      clearInterval(this.autoPlayTimer);
      this.autoPlayTimer = undefined;
    }
  }

  private resumeAutoPlay(): void {
    if (this.config.autoPlay && this.config.layout === 'carousel' && !this.autoPlayTimer) {
      const interval = this.config.autoPlayInterval || 5000;
      this.autoPlayTimer = window.setInterval(() => {
        if (!this.isTransitioning) {
          this.nextPage();
        }
      }, interval);
    }
  }

  private announcePageChange(): void {
    const itemsPerPage = this.config.itemsPerPage || 1;
    const totalPages = Math.ceil(this.reviewCards.length / itemsPerPage);
    const currentPageNumber = this.currentPage + 1;
    
    // Create or update screen reader announcement
    let announcement = this.element.querySelector('.grw-sr-announcement') as HTMLElement;
    if (!announcement) {
      announcement = document.createElement('div');
      announcement.className = 'grw-sr-announcement';
      announcement.setAttribute('aria-live', 'polite');
      announcement.setAttribute('aria-atomic', 'true');
      announcement.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
      this.element.appendChild(announcement);
    }
    
    announcement.textContent = `Page ${currentPageNumber} of ${totalPages}`;
  }

  public getElement(): HTMLElement {
    return this.element;
  }

  public updateReviews(reviews: Review[]): void {
    this.reviews = reviews.slice(0, this.config.maxReviews);
    
    // Destroy existing cards
    this.reviewCards.forEach(card => card.destroy());
    
    // Create new cards
    this.createReviewCards();
    this.renderReviews();
    this.setupPagination();
    
    // Reset to first page
    this.currentPage = 0;
    if (this.config.layout === 'carousel') {
      this.updateCarouselPosition();
      this.updatePaginationDots();
    }
  }

  public updateConfig(newConfig: Partial<ReviewsDisplayConfig>): void {
    const oldLayout = this.config.layout;
    this.config = { ...this.config, ...newConfig };

    // Update review cards config if needed
    if (newConfig.reviewCardConfig) {
      this.reviewCards.forEach(card => 
        card.updateConfig(newConfig.reviewCardConfig!)
      );
    }

    // Re-render if layout changed
    if (newConfig.layout && newConfig.layout !== oldLayout) {
      this.element.className = `grw-reviews-display grw-layout-${this.config.layout}`;
      this.element.innerHTML = this.getContainerHTML();
      this.attachEventListeners(this.element);
      this.renderReviews();
      this.setupPagination();
      this.setupAutoPlay();
      this.setupTouchGestures();
    }
  }

  public destroy(): void {
    this.pauseAutoPlay();
    this.reviewCards.forEach(card => card.destroy());
    
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}