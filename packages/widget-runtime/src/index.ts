// Google Reviews Widget Runtime
// Lightweight embeddable widget that can be loaded on any website

import { ReviewsDisplay, ReviewsDisplayConfig, LayoutType } from './components/ReviewsDisplay.js';
import { Review, ReviewCardConfig } from './components/ReviewCard.js';

interface WidgetConfig {
  widgetId: string;
  apiUrl?: string;
  width?: string;
  height?: string;
  theme?: 'light' | 'dark';
  responsive?: boolean;
  layout?: LayoutType;
  maxReviews?: number;
  showPhotos?: boolean;
  showDates?: boolean;
  maxTextLength?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showNavigation?: boolean;
  showPagination?: boolean;
  itemsPerPage?: number;
  gridColumns?: number;
}

interface EmbedParams {
  [key: string]: string;
}

interface WidgetDataResponse {
  reviews: Review[];
  businessInfo: {
    name: string;
    rating: number;
    reviewCount: number;
  };
  totalReviews: number;
  averageRating: number;
  lastUpdated: Date;
}

interface CachedWidgetData {
  data: WidgetDataResponse;
  timestamp: number;
  etag?: string;
}

class WidgetCache {
  private cache: Map<string, CachedWidgetData> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly STALE_TTL = 10 * 60 * 1000; // 10 minutes for stale data

  set(widgetId: string, data: WidgetDataResponse, etag?: string): void {
    this.cache.set(widgetId, {
      data,
      timestamp: Date.now(),
      etag
    });
  }

  get(widgetId: string): WidgetDataResponse | null {
    const cached = this.cache.get(widgetId);
    if (!cached) return null;

    const age = Date.now() - cached.timestamp;
    if (age < this.CACHE_TTL) {
      return cached.data;
    }

    // Remove expired cache
    if (age > this.STALE_TTL) {
      this.cache.delete(widgetId);
    }

    return null;
  }

  getStale(widgetId: string): WidgetDataResponse | null {
    const cached = this.cache.get(widgetId);
    if (!cached) return null;

    const age = Date.now() - cached.timestamp;
    if (age < this.STALE_TTL) {
      return cached.data;
    }

    // Remove very old cache
    this.cache.delete(widgetId);
    return null;
  }

  getETag(widgetId: string): string | null {
    const cached = this.cache.get(widgetId);
    return cached?.etag || null;
  }

  clear(widgetId?: string): void {
    if (widgetId) {
      this.cache.delete(widgetId);
    } else {
      this.cache.clear();
    }
  }

  size(): number {
    return this.cache.size;
  }
}

class GoogleReviewsWidget {
  private config: WidgetConfig;
  private container: HTMLElement;
  private shadowRoot: ShadowRoot;
  private isInitialized: boolean = false;
  private resizeObserver?: ResizeObserver;
  private reviewsDisplay?: ReviewsDisplay;
  private reviews: Review[] = [];
  private isLoading: boolean = false;
  private loadError: string | null = null;
  private retryCount: number = 0;
  private maxRetries: number = 3;
  private retryDelay: number = 1000; // Start with 1 second
  private cache: WidgetCache;
  private serviceWorkerRegistration?: ServiceWorkerRegistration;

  constructor(config: WidgetConfig) {
    this.config = this.mergeDefaultConfig(config);
    this.container = this.createContainer();
    this.shadowRoot = this.createShadowDOM();
    this.cache = new WidgetCache();
    this.registerServiceWorker();
    this.init();
  }

  private mergeDefaultConfig(config: WidgetConfig): WidgetConfig {
    return {
      apiUrl: 'http://localhost:3001',
      width: '100%',
      height: 'auto',
      theme: 'light',
      responsive: true,
      layout: 'carousel',
      maxReviews: 10,
      showPhotos: true,
      showDates: true,
      maxTextLength: 150,
      autoPlay: false,
      autoPlayInterval: 5000,
      showNavigation: true,
      showPagination: true,
      itemsPerPage: 1,
      gridColumns: 2,
      ...config
    };
  }

  private createContainer(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'grw-widget-root';
    container.setAttribute('data-widget-id', this.config.widgetId);
    
    // Set container styles for responsive behavior
    container.style.cssText = `
      display: block;
      width: ${this.config.width};
      height: ${this.config.height};
      max-width: 100%;
      box-sizing: border-box;
      overflow: hidden;
    `;

    return container;
  }

  private createShadowDOM(): ShadowRoot {
    // Create shadow DOM for CSS isolation
    const shadowRoot = this.container.attachShadow({ mode: 'closed' });
    
    // Inject isolated CSS
    const style = document.createElement('style');
    style.textContent = this.getIsolatedCSS();
    shadowRoot.appendChild(style);

    return shadowRoot;
  }

  private getIsolatedCSS(): string {
    return `
      :host {
        all: initial;
        display: block;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
        color: #333;
        box-sizing: border-box;
        ${this.config.theme === 'dark' ? '--grw-card-bg: #2d2d2d; --grw-border-color: #404040; --grw-text-primary: #ffffff; --grw-text-secondary: #b0b0b0; --grw-placeholder-bg: #404040; --grw-nav-bg: #2d2d2d; --grw-nav-hover-bg: #404040; --grw-nav-hover-border: #555555; --grw-dot-inactive: #555555; --grw-dot-hover: #777777;' : '--grw-card-bg: #ffffff; --grw-border-color: #e1e5e9; --grw-text-primary: #1a1a1a; --grw-text-secondary: #666666; --grw-placeholder-bg: #f0f0f0; --grw-nav-bg: #ffffff; --grw-nav-hover-bg: #f5f5f5; --grw-nav-hover-border: #d0d0d0; --grw-dot-inactive: #d0d0d0; --grw-dot-hover: #999999;'}
        --grw-verified-bg: #4285f4;
        --grw-star-filled: #ffc107;
        --grw-star-empty: #e0e0e0;
        --grw-link-color: #1a73e8;
        --grw-link-hover: #1557b0;
        --grw-focus-color: #1a73e8;
        --grw-dot-active: #1a73e8;
      }

      *, *::before, *::after {
        box-sizing: border-box;
      }

      .grw-widget {
        width: 100%;
        height: 100%;
        background: ${this.config.theme === 'dark' ? '#1a1a1a' : '#ffffff'};
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
        padding: 16px;
      }

      .grw-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: ${this.config.theme === 'dark' ? '#ffffff' : '#666666'};
      }

      .grw-error {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #e74c3c;
        text-align: center;
        padding: 20px;
      }

      .grw-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: grw-spin 1s linear infinite;
        margin-right: 10px;
      }

      @keyframes grw-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Review Card Styles */
      .grw-review-card {
        background: var(--grw-card-bg);
        border: 1px solid var(--grw-border-color);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .grw-review-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
      }

      .grw-review-header {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 12px;
      }

      .grw-author-photo {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        background: var(--grw-placeholder-bg);
      }

      .grw-author-photo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .grw-author-info {
        flex: 1;
        min-width: 0;
      }

      .grw-author-name {
        font-weight: 600;
        font-size: 14px;
        color: var(--grw-text-primary);
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 2px;
      }

      .grw-verified-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        background: var(--grw-verified-bg);
        color: white;
        border-radius: 50%;
        font-size: 10px;
        font-weight: bold;
      }

      .grw-review-date {
        font-size: 12px;
        color: var(--grw-text-secondary);
      }

      .grw-rating {
        flex-shrink: 0;
      }

      .grw-stars {
        display: flex;
        gap: 1px;
      }

      .grw-star {
        font-size: 16px;
        line-height: 1;
      }

      .grw-star-full {
        color: var(--grw-star-filled);
      }

      .grw-star-half {
        color: var(--grw-star-filled);
        position: relative;
      }

      .grw-star-half::after {
        content: '☆';
        position: absolute;
        left: 50%;
        top: 0;
        color: var(--grw-star-empty);
        overflow: hidden;
      }

      .grw-star-empty {
        color: var(--grw-star-empty);
      }

      .grw-review-content {
        margin-top: 12px;
      }

      .grw-review-text {
        font-size: 14px;
        line-height: 1.5;
        color: var(--grw-text-primary);
      }

      .grw-text-content {
        display: block;
        transition: all 0.3s ease;
      }

      .grw-text-content.grw-transitioning {
        opacity: 0.7;
      }

      .grw-expand-btn {
        background: none;
        border: none;
        color: var(--grw-link-color);
        cursor: pointer;
        font-size: 14px;
        padding: 4px 0;
        margin-top: 8px;
        text-decoration: none;
        transition: color 0.2s ease;
      }

      .grw-expand-btn:hover {
        color: var(--grw-link-hover);
        text-decoration: underline;
      }

      .grw-expand-btn:focus {
        outline: 2px solid var(--grw-focus-color);
        outline-offset: 2px;
        border-radius: 2px;
      }

      /* Reviews Display Container */
      .grw-reviews-display {
        width: 100%;
        position: relative;
      }

      .grw-reviews-container {
        position: relative;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .grw-reviews-viewport {
        flex: 1;
        overflow: hidden;
        position: relative;
      }

      .grw-reviews-track {
        display: flex;
        transition: transform 0.3s ease;
      }

      /* Carousel Layout */
      .grw-layout-carousel .grw-reviews-track {
        width: 100%;
      }

      .grw-carousel-page {
        flex: 0 0 100%;
        display: flex;
        gap: 16px;
      }

      .grw-carousel-item {
        flex: 1;
        min-width: 0;
      }

      /* Grid Layout */
      .grw-layout-grid .grw-reviews-track {
        display: grid;
        gap: 16px;
        grid-template-columns: repeat(2, 1fr);
      }

      .grw-grid-item {
        display: flex;
        flex-direction: column;
      }

      /* List Layout */
      .grw-layout-list .grw-reviews-track {
        flex-direction: column;
        gap: 12px;
      }

      .grw-list-item {
        width: 100%;
      }

      /* Navigation Buttons */
      .grw-nav-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: 1px solid var(--grw-border-color);
        border-radius: 50%;
        background: var(--grw-nav-bg);
        color: var(--grw-text-primary);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 18px;
        line-height: 1;
      }

      .grw-nav-btn:hover {
        background: var(--grw-nav-hover-bg);
        border-color: var(--grw-nav-hover-border);
        transform: scale(1.05);
      }

      .grw-nav-btn:focus {
        outline: 2px solid var(--grw-focus-color);
        outline-offset: 2px;
      }

      .grw-nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }

      .grw-nav-icon {
        display: block;
        font-weight: bold;
      }

      /* Pagination */
      .grw-pagination {
        display: flex;
        justify-content: center;
        margin-top: 16px;
      }

      .grw-pagination-dots {
        display: flex;
        gap: 8px;
      }

      .grw-pagination-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: none;
        background: var(--grw-dot-inactive);
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .grw-pagination-dot:hover {
        background: var(--grw-dot-hover);
        transform: scale(1.2);
      }

      .grw-pagination-dot.grw-active {
        background: var(--grw-dot-active);
      }

      .grw-pagination-dot:focus {
        outline: 2px solid var(--grw-focus-color);
        outline-offset: 2px;
      }

      /* Responsive styles */
      @media (max-width: 768px) {
        .grw-widget {
          border-radius: 4px;
          padding: 12px;
        }
        
        .grw-review-card {
          padding: 12px;
        }
        
        .grw-author-photo {
          width: 32px;
          height: 32px;
        }
        
        .grw-author-name {
          font-size: 13px;
        }
        
        .grw-review-text {
          font-size: 13px;
        }
        
        .grw-layout-grid .grw-reviews-track {
          grid-template-columns: 1fr;
        }
        
        .grw-nav-btn {
          width: 36px;
          height: 36px;
          font-size: 16px;
        }
        
        .grw-carousel-page {
          gap: 12px;
        }
      }

      @media (max-width: 480px) {
        :host {
          font-size: 12px;
        }
        
        .grw-widget {
          padding: 10px;
        }
        
        .grw-review-card {
          padding: 10px;
          margin-bottom: 10px;
        }
        
        .grw-review-header {
          gap: 8px;
          margin-bottom: 10px;
        }
        
        .grw-author-photo {
          width: 28px;
          height: 28px;
        }
        
        .grw-author-name {
          font-size: 12px;
        }
        
        .grw-review-date {
          font-size: 11px;
        }
        
        .grw-review-text {
          font-size: 12px;
        }
        
        .grw-stars {
          gap: 0;
        }
        
        .grw-star {
          font-size: 14px;
        }
        
        .grw-nav-btn {
          width: 32px;
          height: 32px;
          font-size: 14px;
        }
        
        .grw-carousel-page {
          gap: 8px;
        }
        
        .grw-pagination {
          margin-top: 12px;
        }
        
        .grw-pagination-dots {
          gap: 6px;
        }
      }

      /* Animation for smooth transitions */
      @keyframes grw-fade-in {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .grw-review-card {
        animation: grw-fade-in 0.3s ease forwards;
      }
    `;
  }

  private async registerServiceWorker(): Promise<void> {
    // Only register service worker in production and if supported
    if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {
      return;
    }

    // Skip service worker in development
    if (this.config.apiUrl?.includes('localhost') || this.config.apiUrl?.includes('127.0.0.1')) {
      return;
    }

    try {
      const registration = await navigator.serviceWorker.register('/grw-service-worker.js', {
        scope: '/'
      });

      this.serviceWorkerRegistration = registration;

      // Listen for service worker updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New service worker is available
              console.log('[Widget] New service worker available');
            }
          });
        }
      });

      console.log('[Widget] Service worker registered successfully');
    } catch (error) {
      console.warn('[Widget] Service worker registration failed:', error);
    }
  }

  private async init(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      this.render();
      this.setupResponsiveContainer();
      await this.loadReviews();
      this.isInitialized = true;
    } catch (error) {
      console.error('Widget initialization failed:', error);
      this.renderError('Failed to initialize widget');
    }
  }

  private render(): void {
    const widgetElement = document.createElement('div');
    widgetElement.className = 'grw-widget';
    
    // Show loading state initially
    widgetElement.innerHTML = `
      <div class="grw-loading">
        <div class="grw-spinner"></div>
        <span>Loading reviews...</span>
      </div>
    `;

    this.shadowRoot.appendChild(widgetElement);
  }

  private renderError(message: string): void {
    const widgetElement = this.shadowRoot.querySelector('.grw-widget');
    if (widgetElement) {
      widgetElement.innerHTML = `
        <div class="grw-error">
          <span>${message}</span>
        </div>
      `;
    }
  }

  private setupResponsiveContainer(): void {
    if (!this.config.responsive) {
      return;
    }

    // Use ResizeObserver for responsive behavior
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          this.handleResize(entry.contentRect);
        }
      });
      this.resizeObserver.observe(this.container);
    }

    // Fallback for older browsers
    window.addEventListener('resize', () => {
      this.handleResize(this.container.getBoundingClientRect());
    });
  }

  private handleResize(rect: DOMRectReadOnly): void {
    const widget = this.shadowRoot.querySelector('.grw-widget') as HTMLElement;
    if (!widget) return;

    // Adjust layout based on container width
    if (rect.width < 480) {
      widget.classList.add('grw-mobile');
    } else {
      widget.classList.remove('grw-mobile');
    }

    if (rect.width < 768) {
      widget.classList.add('grw-tablet');
    } else {
      widget.classList.remove('grw-tablet');
    }
  }

  private async loadReviews(): Promise<void> {
    if (this.isLoading) {
      return;
    }

    this.isLoading = true;
    this.loadError = null;

    try {
      // Check cache first
      const cachedData = this.cache.get(this.config.widgetId);
      if (cachedData) {
        this.processWidgetData(cachedData);
        this.isLoading = false;
        return;
      }

      // Show skeleton loading state
      this.renderSkeletonLoader();

      // Prepare request headers for conditional requests
      const headers: HeadersInit = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      const etag = this.cache.getETag(this.config.widgetId);
      if (etag) {
        headers['If-None-Match'] = etag;
      }

      const response = await fetch(`${this.config.apiUrl}/api/widget/${this.config.widgetId}/data`, {
        method: 'GET',
        headers,
        // Add timeout for better error handling
        signal: AbortSignal.timeout ? AbortSignal.timeout(10000) : undefined
      });

      if (response.status === 304) {
        // Not modified, use cached data
        const staleData = this.cache.getStale(this.config.widgetId);
        if (staleData) {
          this.processWidgetData(staleData);
          this.isLoading = false;
          return;
        }
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: WidgetDataResponse = await response.json();
      
      // Parse date strings back to Date objects
      data.lastUpdated = new Date(data.lastUpdated);
      data.reviews = data.reviews.map(review => ({
        ...review,
        publishedDate: new Date(review.publishedDate)
      }));

      // Cache the data with ETag
      const responseETag = response.headers.get('ETag');
      this.cache.set(this.config.widgetId, data, responseETag || undefined);

      this.processWidgetData(data);
      this.retryCount = 0; // Reset retry count on success
    } catch (error) {
      console.error('Failed to load reviews:', error);
      this.handleLoadError(error);
    } finally {
      this.isLoading = false;
    }
  }

  private processWidgetData(data: WidgetDataResponse): void {
    this.reviews = data.reviews || [];
    this.renderReviews();
  }

  private handleLoadError(error: any): void {
    this.loadError = error instanceof Error ? error.message : 'Unknown error';
    
    // Categorize error type for better handling
    const errorType = this.categorizeError(error);
    
    // Log error with context
    this.logError(error, errorType, {
      widgetId: this.config.widgetId,
      apiUrl: this.config.apiUrl,
      retryCount: this.retryCount,
      hasStaleData: !!this.cache.getStale(this.config.widgetId),
    });

    // Try to serve stale cached data as fallback
    const staleData = this.cache.getStale(this.config.widgetId);
    if (staleData) {
      console.warn('Serving stale data due to load error:', this.loadError);
      this.processWidgetData(staleData);
      this.showStaleDataNotice();
      return;
    }

    // If no cached data and we haven't exceeded retry limit, try again
    if (this.retryCount < this.maxRetries && this.shouldRetry(errorType)) {
      this.retryCount++;
      const delay = this.retryDelay * Math.pow(2, this.retryCount - 1); // Exponential backoff
      
      console.warn(`Retrying in ${delay}ms (attempt ${this.retryCount}/${this.maxRetries})`);
      
      setTimeout(() => {
        this.loadReviews();
      }, delay);
      
      return;
    }

    // All retries exhausted, show error or fallback to mock data
    if (this.shouldUseMockData()) {
      console.warn('Using mock data as final fallback');
      this.loadMockReviews();
    } else {
      this.renderError(this.getErrorMessage(errorType));
    }
  }

  private shouldUseMockData(): boolean {
    // Use mock data in development or when explicitly configured
    return this.config.apiUrl?.includes('localhost') || 
           this.config.apiUrl?.includes('127.0.0.1') ||
           (window as any).GRW_USE_MOCK_DATA === true;
  }

  private categorizeError(error: any): string {
    if (!error) return 'unknown';
    
    const message = error.message || error.toString();
    
    if (error.name === 'AbortError' || message.includes('timeout')) {
      return 'timeout';
    }
    
    if (error.name === 'TypeError' && message.includes('fetch')) {
      return 'network';
    }
    
    if (message.includes('HTTP 4')) {
      return 'client-error';
    }
    
    if (message.includes('HTTP 5')) {
      return 'server-error';
    }
    
    if (message.includes('JSON') || message.includes('parse')) {
      return 'parse-error';
    }
    
    if (message.includes('CORS')) {
      return 'cors-error';
    }
    
    return 'unknown';
  }

  private shouldRetry(errorType: string): boolean {
    // Don't retry client errors (4xx) or parse errors
    return !['client-error', 'parse-error', 'cors-error'].includes(errorType);
  }

  private getErrorMessage(errorType: string): string {
    switch (errorType) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection.';
      case 'timeout':
        return 'Request timed out. Please try again later.';
      case 'server-error':
        return 'Server error occurred. Please try again later.';
      case 'client-error':
        return 'Widget configuration error. Please check your setup.';
      case 'parse-error':
        return 'Invalid response from server. Please try again later.';
      case 'cors-error':
        return 'Cross-origin request blocked. Please check widget configuration.';
      default:
        return `Failed to load reviews: ${this.loadError}`;
    }
  }

  private logError(error: any, errorType: string, context: any): void {
    const errorData = {
      message: error?.message || 'Unknown error',
      type: errorType,
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // Log to console in development
    if (this.config.apiUrl?.includes('localhost') || this.config.apiUrl?.includes('127.0.0.1')) {
      console.error('Widget error:', errorData);
    }

    // In production, you could send this to a monitoring service
    // Example: Send to monitoring endpoint
    try {
      if (typeof fetch !== 'undefined' && this.config.apiUrl && !this.config.apiUrl.includes('localhost')) {
        fetch(`${this.config.apiUrl}/api/widget/error-log`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(errorData)
        }).catch(() => {
          // Silently fail error logging to avoid infinite loops
        });
      }
    } catch {
      // Silently fail error logging
    }
  }

  private showStaleDataNotice(): void {
    const widgetElement = this.shadowRoot.querySelector('.grw-widget');
    if (!widgetElement) return;

    // Add a subtle notice that data might be outdated
    const notice = document.createElement('div');
    notice.className = 'grw-stale-notice';
    notice.innerHTML = `
      <style>
        .grw-stale-notice {
          position: absolute;
          top: 8px;
          right: 8px;
          background: rgba(255, 193, 7, 0.9);
          color: #856404;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 500;
          z-index: 10;
          opacity: 0;
          animation: grw-fade-in-notice 0.3s ease forwards;
        }
        
        @keyframes grw-fade-in-notice {
          to { opacity: 1; }
        }
      </style>
      <span title="Reviews data may be outdated">⚠️ Offline</span>
    `;

    widgetElement.appendChild(notice);

    // Remove notice after 5 seconds
    setTimeout(() => {
      if (notice.parentNode) {
        notice.remove();
      }
    }, 5000);
  }

  private renderSkeletonLoader(): void {
    const widgetElement = this.shadowRoot.querySelector('.grw-widget');
    if (!widgetElement) return;

    const skeletonHTML = this.generateSkeletonHTML();
    widgetElement.innerHTML = skeletonHTML;
  }

  private generateSkeletonHTML(): string {
    const itemCount = this.config.layout === 'carousel' ? (this.config.itemsPerPage || 1) : 3;
    const skeletonItems = Array(itemCount).fill(0).map((_, index) => `
      <div class="grw-skeleton-card" style="animation-delay: ${index * 0.1}s">
        <div class="grw-skeleton-header">
          <div class="grw-skeleton-avatar"></div>
          <div class="grw-skeleton-info">
            <div class="grw-skeleton-name"></div>
            <div class="grw-skeleton-date"></div>
          </div>
          <div class="grw-skeleton-rating">
            <div class="grw-skeleton-stars"></div>
          </div>
        </div>
        <div class="grw-skeleton-content">
          <div class="grw-skeleton-text"></div>
          <div class="grw-skeleton-text short"></div>
        </div>
      </div>
    `).join('');

    return `
      <style>
        .grw-skeleton-card {
          background: var(--grw-card-bg);
          border: 1px solid var(--grw-border-color);
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          animation: grw-skeleton-pulse 1.5s ease-in-out infinite;
        }
        
        .grw-skeleton-header {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 12px;
        }
        
        .grw-skeleton-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: var(--grw-placeholder-bg);
        }
        
        .grw-skeleton-info {
          flex: 1;
        }
        
        .grw-skeleton-name {
          width: 120px;
          height: 16px;
          background: var(--grw-placeholder-bg);
          border-radius: 4px;
          margin-bottom: 4px;
        }
        
        .grw-skeleton-date {
          width: 80px;
          height: 12px;
          background: var(--grw-placeholder-bg);
          border-radius: 4px;
        }
        
        .grw-skeleton-rating {
          width: 100px;
          height: 16px;
          background: var(--grw-placeholder-bg);
          border-radius: 4px;
        }
        
        .grw-skeleton-stars {
          width: 100%;
          height: 100%;
          background: var(--grw-placeholder-bg);
          border-radius: 4px;
        }
        
        .grw-skeleton-content {
          margin-top: 12px;
        }
        
        .grw-skeleton-text {
          height: 14px;
          background: var(--grw-placeholder-bg);
          border-radius: 4px;
          margin-bottom: 8px;
        }
        
        .grw-skeleton-text:not(.short) {
          width: 100%;
        }
        
        .grw-skeleton-text.short {
          width: 70%;
        }
        
        @keyframes grw-skeleton-pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.6;
          }
        }
      </style>
      <div class="grw-skeleton-container">
        ${skeletonItems}
      </div>
    `;
  }

  private loadMockReviews(): void {
    // Mock reviews for demonstration
    this.reviews = [
      {
        id: '1',
        authorName: 'John Smith',
        authorPhotoUrl: 'https://via.placeholder.com/40x40',
        rating: 5,
        text: 'Excellent service! The team was professional and delivered exactly what we needed. Highly recommend their services to anyone looking for quality work.',
        publishedDate: new Date(Date.now() - 86400000 * 2), // 2 days ago
        isVerified: true
      },
      {
        id: '2',
        authorName: 'Sarah Johnson',
        rating: 4,
        text: 'Great experience overall. The staff was friendly and helpful throughout the entire process.',
        publishedDate: new Date(Date.now() - 86400000 * 7), // 1 week ago
        isVerified: false
      },
      {
        id: '3',
        authorName: 'Mike Davis',
        authorPhotoUrl: 'https://via.placeholder.com/40x40',
        rating: 5,
        text: 'Outstanding quality and attention to detail. They went above and beyond our expectations. The final result exceeded what we had hoped for and the communication was excellent throughout.',
        publishedDate: new Date(Date.now() - 86400000 * 14), // 2 weeks ago
        isVerified: true
      },
      {
        id: '4',
        authorName: 'Emily Wilson',
        rating: 4,
        text: 'Very satisfied with the service. Professional team and timely delivery.',
        publishedDate: new Date(Date.now() - 86400000 * 21), // 3 weeks ago
        isVerified: false
      }
    ];

    this.renderReviews();
  }

  private renderReviews(): void {
    const widgetElement = this.shadowRoot.querySelector('.grw-widget');
    if (!widgetElement || this.reviews.length === 0) {
      this.renderError('No reviews available');
      return;
    }

    // Create reviews display configuration
    const reviewCardConfig: ReviewCardConfig = {
      showPhotos: this.config.showPhotos || true,
      showDates: this.config.showDates || true,
      maxTextLength: this.config.maxTextLength || 150,
      theme: this.config.theme || 'light'
    };

    const reviewsDisplayConfig: ReviewsDisplayConfig = {
      layout: this.config.layout || 'carousel',
      maxReviews: this.config.maxReviews || 10,
      reviewCardConfig,
      autoPlay: this.config.autoPlay || false,
      autoPlayInterval: this.config.autoPlayInterval || 5000,
      showNavigation: this.config.showNavigation !== false,
      showPagination: this.config.showPagination !== false,
      itemsPerPage: this.config.itemsPerPage || 1,
      gridColumns: this.config.gridColumns || 2
    };

    // Create and render reviews display
    this.reviewsDisplay = new ReviewsDisplay(this.reviews, reviewsDisplayConfig);
    
    // Clear loading state and add reviews
    widgetElement.innerHTML = '';
    widgetElement.appendChild(this.reviewsDisplay.getElement());
  }

  public mount(selector: string): boolean {
    try {
      const target = document.querySelector(selector);
      if (!target) {
        console.error(`Widget mount target not found: ${selector}`);
        return false;
      }

      target.appendChild(this.container);
      return true;
    } catch (error) {
      console.error('Failed to mount widget:', error);
      return false;
    }
  }

  public unmount(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    if (this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }

    this.isInitialized = false;
  }

  public updateConfig(newConfig: Partial<WidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update container styles if dimensions changed
    if (newConfig.width || newConfig.height) {
      this.container.style.width = this.config.width || '100%';
      this.container.style.height = this.config.height || 'auto';
    }

    // Re-render if theme changed
    if (newConfig.theme) {
      const style = this.shadowRoot.querySelector('style');
      if (style) {
        style.textContent = this.getIsolatedCSS();
      }
    }
  }

  public getConfig(): WidgetConfig {
    return { ...this.config };
  }

  public isReady(): boolean {
    return this.isInitialized;
  }

  public async refresh(): Promise<void> {
    // Clear cache and reload data
    this.cache.clear(this.config.widgetId);
    this.retryCount = 0;
    await this.loadReviews();
  }

  public clearCache(): void {
    this.cache.clear(this.config.widgetId);
  }

  public getCacheStats(): { size: number; hasData: boolean } {
    return {
      size: this.cache.size(),
      hasData: this.cache.get(this.config.widgetId) !== null
    };
  }
}

// Utility function to parse embed parameters from script tag
function parseEmbedParams(): EmbedParams {
  const scripts = document.querySelectorAll('script[data-grw-widget-id]');
  const params: EmbedParams = {};

  scripts.forEach(script => {
    Array.from(script.attributes).forEach(attr => {
      if (attr.name.startsWith('data-grw-')) {
        const key = attr.name.replace('data-grw-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
        params[key] = attr.value;
      }
    });
  });

  return params;
}

// Auto-initialization function
function autoInit(): void {
  const params = parseEmbedParams();
  
  if (params.widgetId) {
    const config: WidgetConfig = {
      widgetId: params.widgetId,
      apiUrl: params.apiUrl,
      width: params.width,
      height: params.height,
      theme: params.theme as 'light' | 'dark',
      responsive: params.responsive !== 'false'
    };

    const widget = new GoogleReviewsWidget(config);
    
    // Auto-mount to the script tag's parent or specified selector
    const mountSelector = params.mountSelector || 'script[data-grw-widget-id="' + params.widgetId + '"]';
    const scriptTag = document.querySelector(mountSelector);
    
    if (scriptTag && scriptTag.parentElement) {
      const container = document.createElement('div');
      scriptTag.parentElement.insertBefore(container, scriptTag);
      // Create a unique selector for the container
      const containerId = 'grw-container-' + params.widgetId;
      container.id = containerId;
      widget.mount('#' + containerId);
    }
  }
}

// Global API
const GoogleReviewsWidgetAPI = {
  create: (config: WidgetConfig) => new GoogleReviewsWidget(config),
  autoInit,
  parseEmbedParams
};

// Expose to global scope
(window as any).GoogleReviewsWidget = GoogleReviewsWidget;
(window as any).GoogleReviewsWidgetAPI = GoogleReviewsWidgetAPI;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', autoInit);
} else {
  autoInit();
}

export default GoogleReviewsWidget;
export { GoogleReviewsWidgetAPI, parseEmbedParams };