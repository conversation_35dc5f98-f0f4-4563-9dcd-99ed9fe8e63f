// DOM utility functions - tree-shakable
export const createElement = (tag: string, className?: string, innerHTML?: string): HTMLElement => {
  const element = document.createElement(tag);
  if (className) element.className = className;
  if (innerHTML) element.innerHTML = innerHTML;
  return element;
};

export const addEventListeners = (element: HTMLElement, events: Record<string, EventListener>): void => {
  Object.entries(events).forEach(([event, handler]) => {
    element.addEventListener(event, handler);
  });
};

export const removeEventListeners = (element: HTMLElement, events: Record<string, EventListener>): void => {
  Object.entries(events).forEach(([event, handler]) => {
    element.removeEventListener(event, handler);
  });
};

export const setStyles = (element: HTMLElement, styles: Record<string, string>): void => {
  Object.entries(styles).forEach(([property, value]) => {
    element.style.setProperty(property, value);
  });
};