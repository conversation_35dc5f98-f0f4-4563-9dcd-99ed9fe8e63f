// Image optimization and lazy loading utility
export interface ImageOptions {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  placeholder?: string;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

export class LazyImageLoader {
  private observer?: IntersectionObserver;
  private loadedImages = new Set<string>();

  constructor() {
    this.initIntersectionObserver();
  }

  private initIntersectionObserver(): void {
    if (typeof IntersectionObserver === 'undefined') {
      return; // Fallback for older browsers
    }

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            this.loadImage(img);
            this.observer?.unobserve(img);
          }
        });
      },
      {
        rootMargin: '50px 0px', // Start loading 50px before image comes into view
        threshold: 0.1
      }
    );
  }

  createOptimizedImage(options: ImageOptions): HTMLImageElement {
    const img = document.createElement('img');
    
    // Set basic attributes
    img.alt = options.alt || '';
    img.loading = options.loading || 'lazy';
    
    // Set dimensions if provided
    if (options.width) img.width = options.width;
    if (options.height) img.height = options.height;
    
    // Add CSS for better loading experience
    img.style.cssText = `
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
      background-color: #f0f0f0;
    `;

    // Set placeholder or loading state
    if (options.placeholder) {
      img.src = options.placeholder;
      img.dataset.src = this.optimizeImageUrl(options.src, options);
    } else {
      // Use a simple placeholder
      img.src = this.createPlaceholderDataUrl(options.width || 40, options.height || 40);
      img.dataset.src = this.optimizeImageUrl(options.src, options);
    }

    // Add event listeners
    if (options.onLoad) {
      img.addEventListener('load', options.onLoad);
    }
    
    if (options.onError) {
      img.addEventListener('error', options.onError);
    } else {
      // Default error handler
      img.addEventListener('error', () => {
        img.src = this.createPlaceholderDataUrl(options.width || 40, options.height || 40);
        img.style.backgroundColor = '#e0e0e0';
      });
    }

    // Use intersection observer for lazy loading if available
    if (this.observer && options.loading === 'lazy') {
      this.observer.observe(img);
    } else {
      // Fallback: load immediately
      this.loadImage(img);
    }

    return img;
  }

  private loadImage(img: HTMLImageElement): void {
    const src = img.dataset.src;
    if (!src || this.loadedImages.has(src)) return;

    this.loadedImages.add(src);
    
    // Create a new image to preload
    const tempImg = new Image();
    
    tempImg.onload = () => {
      img.src = src;
      img.style.opacity = '1';
    };
    
    tempImg.onerror = () => {
      // Keep placeholder on error
      img.style.opacity = '0.7';
    };
    
    tempImg.src = src;
  }

  private optimizeImageUrl(src: string, options: ImageOptions): string {
    // If it's already a data URL or blob, return as-is
    if (src.startsWith('data:') || src.startsWith('blob:')) {
      return src;
    }

    // For Google profile images, we can add size parameters
    if (src.includes('googleusercontent.com')) {
      const url = new URL(src);
      const size = Math.max(options.width || 40, options.height || 40);
      // Google images support size parameter
      url.searchParams.set('s', Math.min(size * 2, 200).toString()); // 2x for retina, max 200px
      return url.toString();
    }

    // For other images, return as-is (could be extended with CDN optimization)
    return src;
  }

  private createPlaceholderDataUrl(width: number, height: number): string {
    // Create a simple SVG placeholder
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <circle cx="50%" cy="40%" r="25%" fill="#d0d0d0"/>
        <path d="M25% 70% Q50% 60% 75% 70% L75% 85% L25% 85% Z" fill="#d0d0d0"/>
      </svg>
    `;
    
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.loadedImages.clear();
  }
}

// Singleton instance for the widget
export const imageLoader = new LazyImageLoader();