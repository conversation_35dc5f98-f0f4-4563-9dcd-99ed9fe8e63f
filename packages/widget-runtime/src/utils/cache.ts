// Lightweight cache utility - tree-shakable
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  etag?: string;
}

export class LightweightCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private readonly ttl: number;
  private readonly staleTtl: number;

  constructor(ttl = 5 * 60 * 1000, staleTtl = 10 * 60 * 1000) {
    this.ttl = ttl;
    this.staleTtl = staleTtl;
  }

  set(key: string, data: T, etag?: string): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      etag
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const age = Date.now() - entry.timestamp;
    if (age < this.ttl) {
      return entry.data;
    }

    if (age > this.staleTtl) {
      this.cache.delete(key);
    }

    return null;
  }

  getStale(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const age = Date.now() - entry.timestamp;
    if (age < this.staleTtl) {
      return entry.data;
    }

    this.cache.delete(key);
    return null;
  }

  getETag(key: string): string | null {
    return this.cache.get(key)?.etag || null;
  }

  clear(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  size(): number {
    return this.cache.size;
  }
}