// Lightweight HTTP client - tree-shakable
export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
}

export interface HttpResponse<T> {
  data: T;
  status: number;
  headers: Headers;
}

export class HttpError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: Response
  ) {
    super(message);
    this.name = 'HttpError';
  }
}

export const request = async <T>(url: string, options: RequestOptions = {}): Promise<HttpResponse<T>> => {
  const {
    method = 'GET',
    headers = {},
    body,
    timeout = 10000
  } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body,
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new HttpError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response
      );
    }

    const data = await response.json();
    
    return {
      data,
      status: response.status,
      headers: response.headers
    };
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof HttpError) {
      throw error;
    }
    
    if (error instanceof Error && error.name === 'AbortError') {
      throw new HttpError('Request timeout', 408);
    }
    
    throw new HttpError(
      error instanceof Error ? error.message : 'Network error',
      0
    );
  }
};

export const get = <T>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> =>
  request<T>(url, { method: 'GET', headers });

export const post = <T>(url: string, data: any, headers?: Record<string, string>): Promise<HttpResponse<T>> =>
  request<T>(url, { method: 'POST', body: JSON.stringify(data), headers });