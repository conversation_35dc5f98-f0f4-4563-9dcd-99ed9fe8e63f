/* Review Card Styles */
.grw-review-card {
  background: var(--grw-card-bg, #ffffff);
  border: 1px solid var(--grw-border-color, #e1e5e9);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.grw-review-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.grw-review-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.grw-author-photo {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--grw-placeholder-bg, #f0f0f0);
}

.grw-author-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.grw-author-info {
  flex: 1;
  min-width: 0;
}

.grw-author-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--grw-text-primary, #1a1a1a);
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
}

.grw-verified-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: var(--grw-verified-bg, #4285f4);
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: bold;
}

.grw-review-date {
  font-size: 12px;
  color: var(--grw-text-secondary, #666666);
}

.grw-rating {
  flex-shrink: 0;
}

.grw-stars {
  display: flex;
  gap: 1px;
}

.grw-star {
  font-size: 16px;
  line-height: 1;
}

.grw-star-full {
  color: var(--grw-star-filled, #ffc107);
}

.grw-star-half {
  color: var(--grw-star-filled, #ffc107);
  position: relative;
}

.grw-star-half::after {
  content: '☆';
  position: absolute;
  left: 50%;
  top: 0;
  color: var(--grw-star-empty, #e0e0e0);
  overflow: hidden;
}

.grw-star-empty {
  color: var(--grw-star-empty, #e0e0e0);
}

.grw-review-content {
  margin-top: 12px;
}

.grw-review-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--grw-text-primary, #1a1a1a);
}

.grw-text-content {
  display: block;
  transition: all 0.3s ease;
}

.grw-text-content.grw-transitioning {
  opacity: 0.7;
}

.grw-expand-btn {
  background: none;
  border: none;
  color: var(--grw-link-color, #1a73e8);
  cursor: pointer;
  font-size: 14px;
  padding: 4px 0;
  margin-top: 8px;
  text-decoration: none;
  transition: color 0.2s ease;
}

.grw-expand-btn:hover {
  color: var(--grw-link-hover, #1557b0);
  text-decoration: underline;
}

.grw-expand-btn:focus {
  outline: 2px solid var(--grw-focus-color, #1a73e8);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Reviews Display Container */
.grw-reviews-display {
  width: 100%;
  position: relative;
}

.grw-reviews-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
}

.grw-reviews-viewport {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.grw-reviews-track {
  display: flex;
  transition: transform 0.3s ease;
}

/* Carousel Layout */
.grw-layout-carousel .grw-reviews-track {
  width: 100%;
}

.grw-carousel-page {
  flex: 0 0 100%;
  display: flex;
  gap: 16px;
}

.grw-carousel-item {
  flex: 1;
  min-width: 0;
}

/* Grid Layout */
.grw-layout-grid .grw-reviews-track {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(2, 1fr);
}

.grw-grid-item {
  display: flex;
  flex-direction: column;
}

.grw-grid-page {
  display: contents;
  transition: opacity 0.3s ease;
}

/* List Layout */
.grw-layout-list .grw-reviews-track {
  flex-direction: column;
  gap: 12px;
}

.grw-list-item {
  width: 100%;
}

.grw-list-page {
  display: block;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Navigation Buttons */
.grw-nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--grw-border-color, #e1e5e9);
  border-radius: 50%;
  background: var(--grw-nav-bg, #ffffff);
  color: var(--grw-text-primary, #1a1a1a);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
  line-height: 1;
}

.grw-nav-btn:hover {
  background: var(--grw-nav-hover-bg, #f5f5f5);
  border-color: var(--grw-nav-hover-border, #d0d0d0);
  transform: scale(1.05);
}

.grw-nav-btn:focus {
  outline: 2px solid var(--grw-focus-color, #1a73e8);
  outline-offset: 2px;
}

.grw-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.grw-nav-icon {
  display: block;
  font-weight: bold;
}

/* Pagination */
.grw-pagination {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.grw-pagination-dots {
  display: flex;
  gap: 8px;
}

.grw-pagination-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: var(--grw-dot-inactive, #d0d0d0);
  cursor: pointer;
  transition: all 0.2s ease;
}

.grw-pagination-dot:hover {
  background: var(--grw-dot-hover, #999999);
  transform: scale(1.2);
}

.grw-pagination-dot.grw-active {
  background: var(--grw-dot-active, #1a73e8);
}

.grw-pagination-dot:focus {
  outline: 2px solid var(--grw-focus-color, #1a73e8);
  outline-offset: 2px;
}

/* Dark Theme */
.grw-theme-dark {
  --grw-card-bg: #2d2d2d;
  --grw-border-color: #404040;
  --grw-text-primary: #ffffff;
  --grw-text-secondary: #b0b0b0;
  --grw-placeholder-bg: #404040;
  --grw-nav-bg: #2d2d2d;
  --grw-nav-hover-bg: #404040;
  --grw-nav-hover-border: #555555;
  --grw-dot-inactive: #555555;
  --grw-dot-hover: #777777;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grw-review-card {
    padding: 12px;
  }
  
  .grw-author-photo {
    width: 32px;
    height: 32px;
  }
  
  .grw-author-name {
    font-size: 13px;
  }
  
  .grw-review-text {
    font-size: 13px;
  }
  
  .grw-layout-grid .grw-reviews-track {
    grid-template-columns: 1fr;
  }
  
  .grw-nav-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .grw-carousel-page {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .grw-review-card {
    padding: 10px;
    margin-bottom: 10px;
  }
  
  .grw-review-header {
    gap: 8px;
    margin-bottom: 10px;
  }
  
  .grw-author-photo {
    width: 28px;
    height: 28px;
  }
  
  .grw-author-name {
    font-size: 12px;
  }
  
  .grw-review-date {
    font-size: 11px;
  }
  
  .grw-review-text {
    font-size: 12px;
  }
  
  .grw-stars {
    gap: 0;
  }
  
  .grw-star {
    font-size: 14px;
  }
  
  .grw-nav-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .grw-carousel-page {
    gap: 8px;
  }
  
  .grw-pagination {
    margin-top: 12px;
  }
  
  .grw-pagination-dots {
    gap: 6px;
  }
}

/* Animation for smooth transitions */
@keyframes grw-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grw-review-card {
  animation: grw-fade-in 0.3s ease forwards;
}

/* Loading skeleton styles */
.grw-review-skeleton {
  background: var(--grw-card-bg, #ffffff);
  border: 1px solid var(--grw-border-color, #e1e5e9);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.grw-skeleton-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.grw-skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: grw-skeleton-loading 1.5s infinite;
}

.grw-skeleton-text {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: grw-skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.grw-skeleton-name {
  height: 16px;
  width: 120px;
  margin-bottom: 4px;
}

.grw-skeleton-date {
  height: 12px;
  width: 80px;
}

.grw-skeleton-content {
  height: 14px;
  width: 100%;
  margin-bottom: 8px;
}

.grw-skeleton-content:last-child {
  width: 75%;
  margin-bottom: 0;
}

@keyframes grw-skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}