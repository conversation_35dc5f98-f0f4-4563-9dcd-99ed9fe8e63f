/**
 * Widget Platform Script
 * This script handles the loading and rendering of all Google Reviews Widgets
 * Similar to how Elfsight's platform.js works
 */

interface WidgetConfig {
    template: string;
    styling: {
        colors: {
            primary: string;
            secondary: string;
            background: string;
            text: string;
            border: string;
        };
        fonts: {
            family: string;
            size: string;
            weight: string;
        };
        dimensions: {
            width: string;
            height: string;
            borderRadius: string;
        };
        spacing: {
            padding: string;
            margin: string;
            gap: string;
        };
    };
    settings: {
        maxReviews: number;
        minRating: number;
        sortBy: string;
        showPhotos: boolean;
        showDates: boolean;
        autoRefresh: boolean;
    };
    headerSettings?: {
        showHeader: boolean;
        headerText: string;
        headerSize: string;
        headerColor: string;
        headerAlign: string;
        showSubheader: boolean;
        subheaderText: string;
        subheaderColor: string;
        subheaderAlign: string;
    };
}

class GoogleReviewsWidgetPlatform {
    private static instance: GoogleReviewsWidgetPlatform;
    private loadedWidgets: Set<string> = new Set();
    private widgetConfigs: Map<string, WidgetConfig> = new Map();

    private constructor() {
        this.init();
    }

    public static getInstance(): GoogleReviewsWidgetPlatform {
        if (!GoogleReviewsWidgetPlatform.instance) {
            GoogleReviewsWidgetPlatform.instance = new GoogleReviewsWidgetPlatform();
        }
        return GoogleReviewsWidgetPlatform.instance;
    }

    private init(): void {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.scanAndLoadWidgets());
        } else {
            this.scanAndLoadWidgets();
        }

        // Also scan for widgets added dynamically
        this.observeForNewWidgets();
    }

    private scanAndLoadWidgets(): void {
        const widgets = document.querySelectorAll('[class*="grw-widget-"][data-grw-widget-lazy]');
        widgets.forEach((element) => this.loadWidget(element as HTMLElement));
    }

    private observeForNewWidgets(): void {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as HTMLElement;
                        // Check if the added element is a widget
                        if (element.matches && element.matches('[class*="grw-widget-"][data-grw-widget-lazy]')) {
                            this.loadWidget(element);
                        }
                        // Check for widgets in added subtree
                        const widgets = element.querySelectorAll && element.querySelectorAll('[class*="grw-widget-"][data-grw-widget-lazy]');
                        if (widgets) {
                            widgets.forEach((widget) => this.loadWidget(widget as HTMLElement));
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    private async loadWidget(element: HTMLElement): Promise<void> {
        const widgetId = this.extractWidgetId(element);
        if (!widgetId || this.loadedWidgets.has(widgetId)) {
            return;
        }

        this.loadedWidgets.add(widgetId);

        try {
            // Show loading state
            this.showLoadingState(element);

            // Load widget configuration
            const config = await this.loadWidgetConfig(widgetId);
            if (!config) {
                throw new Error(`Configuration not found for widget ${widgetId}`);
            }

            // Render the widget
            await this.renderWidget(element, config);

            // Remove loading state
            this.removeLoadingState(element);
        } catch (error) {
            console.error(`Failed to load widget ${widgetId}:`, error);
            this.showErrorState(element, error as Error);
        }
    }

    private extractWidgetId(element: HTMLElement): string | null {
        const classList = Array.from(element.classList);
        const widgetClass = classList.find(cls => cls.startsWith('grw-widget-'));
        return widgetClass ? widgetClass.replace('grw-widget-', '') : null;
    }

    private async loadWidgetConfig(widgetId: string): Promise<WidgetConfig | null> {
        // First try to load from cache
        if (this.widgetConfigs.has(widgetId)) {
            return this.widgetConfigs.get(widgetId)!;
        }

        try {
            // Try to load from localStorage (for development/testing)
            const localConfig = localStorage.getItem(`widget-config-${widgetId}`);
            if (localConfig) {
                const config = JSON.parse(localConfig);
                this.widgetConfigs.set(widgetId, config);
                return config;
            }

            // Dynamically determine the API base URL
            // Try the main domain first, then fall back to the script's origin
            const possibleUrls = [
                'https://google-review-liard.vercel.app',
                'https://google-review.vercel.app',
                // If the script is loaded from a different domain, try that too
                window.location.origin.includes('vercel.app') ? window.location.origin : null
            ].filter(Boolean);

            let response;
            let lastError;

            for (const apiBaseUrl of possibleUrls) {
                try {
                    response = await fetch(`${apiBaseUrl}/api/widget-config/${widgetId}`);
                    if (response.ok) {
                        break;
                    }
                } catch (error) {
                    lastError = error;
                    continue;
                }
            }

            if (!response || !response.ok) {
                throw lastError || new Error('Failed to fetch widget configuration from any URL');
            }
            if (response.ok) {
                const responseData = await response.json();
                // Extract the config from the API response wrapper
                const config = responseData.config || responseData;
                this.widgetConfigs.set(widgetId, config);
                return config;
            }
        } catch (error) {
            console.error(`Failed to load config for widget ${widgetId}:`, error);
        }

        return null;
    }

    private showLoadingState(element: HTMLElement): void {
        element.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #666;
      ">
        <div style="text-align: center;">
          <div style="
            width: 24px;
            height: 24px;
            border: 2px solid #e0e0e0;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
          "></div>
          <div>Loading reviews...</div>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;
    }

    private removeLoadingState(element: HTMLElement): void {
        // Loading state will be replaced by the actual widget content
    }

    private showErrorState(element: HTMLElement, error: Error): void {
        element.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        background: #fff5f5;
        border-radius: 8px;
        border: 1px solid #fed7d7;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #c53030;
        text-align: center;
        padding: 20px;
      ">
        <div>
          <div style="font-size: 18px; margin-bottom: 8px;">⚠️</div>
          <div style="font-weight: 500; margin-bottom: 4px;">Failed to load reviews</div>
          <div style="font-size: 12px; opacity: 0.8;">${error.message}</div>
        </div>
      </div>
    `;
    }

    private async renderWidget(element: HTMLElement, config: WidgetConfig): Promise<void> {
        // Create a simple widget renderer that uses the existing components
        const renderer = new SimpleWidgetRenderer(element, config);
        renderer.render();
    }
}

class SimpleWidgetRenderer {
    private element: HTMLElement;
    private config: WidgetConfig;

    constructor(element: HTMLElement, config: WidgetConfig) {
        this.element = element;
        this.config = config;
    }

    public render(): void {
        this.element.innerHTML = '';

        const widgetContainer = document.createElement('div');
        widgetContainer.className = 'grw-simple-widget';

        // Apply styling from config
        const styling = this.config.styling || this.config;
        widgetContainer.style.cssText = `
            width: ${styling.dimensions?.width || '400px'};
            height: ${styling.dimensions?.height || '300px'};
            background: ${styling.colors?.background || '#ffffff'};
            border: 1px solid ${styling.colors?.border || '#e0e0e0'};
            border-radius: ${styling.dimensions?.borderRadius || '8px'};
            padding: ${styling.spacing?.padding || '16px'};
            font-family: ${styling.fonts?.family || 'Arial, sans-serif'};
            font-size: ${styling.fonts?.size || '14px'};
            color: ${styling.colors?.text || '#333333'};
            overflow: hidden;
            position: relative;
        `;

        // Add header if enabled
        if (this.config.headerSettings?.showHeader) {
            const header = this.createHeader();
            widgetContainer.appendChild(header);
        }

        // Add sample reviews content
        const reviewsContent = this.createReviewsContent();
        widgetContainer.appendChild(reviewsContent);

        this.element.appendChild(widgetContainer);
    }

    private createHeader(): HTMLElement {
        const header = document.createElement('div');
        header.className = 'grw-widget-header';
        header.style.cssText = `
            text-align: ${this.config.headerSettings?.headerAlign || 'center'};
            margin-bottom: 16px;
            border-bottom: 1px solid ${this.config.styling.colors.border};
            padding-bottom: 12px;
        `;

        if (this.config.headerSettings?.showHeader) {
            const headerText = document.createElement('h3');
            headerText.textContent = this.config.headerSettings.headerText || 'Customer Reviews';
            headerText.style.cssText = `
                margin: 0 0 8px 0;
                color: ${this.config.headerSettings.headerColor || this.config.styling.colors.text};
                font-size: ${this.config.headerSettings.headerSize === 'large' ? '24px' :
                    this.config.headerSettings.headerSize === 'small' ? '16px' : '20px'};
                font-weight: 600;
            `;
            header.appendChild(headerText);
        }

        if (this.config.headerSettings?.showSubheader) {
            const subheader = document.createElement('p');
            subheader.textContent = this.config.headerSettings.subheaderText || 'What our customers are saying';
            subheader.style.cssText = `
                margin: 0;
                color: ${this.config.headerSettings.subheaderColor || '#666'};
                font-size: 14px;
                opacity: 0.8;
            `;
            header.appendChild(subheader);
        }

        return header;
    }

    private createReviewsContent(): HTMLElement {
        const content = document.createElement('div');
        content.className = 'grw-reviews-content';
        content.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: ${this.config.styling.spacing.gap};
            height: 100%;
            overflow-y: auto;
        `;

        // Create sample reviews
        const sampleReviews = [
            {
                name: 'Sarah Allen',
                rating: 5,
                text: 'Super fast delivery and we love our new toys. Thank you',
                date: '10 days ago',
                avatar: 'S'
            },
            {
                name: 'South Auckland - Manukau',
                rating: 5,
                text: 'Easy as. Ordered online, paid and within a week parcel arrived. Thank you',
                date: '12 days ago',
                avatar: 'S'
            },
            {
                name: 'Hugh & Jane Masters',
                rating: 5,
                text: 'Easy website to browse. Great efficient service on ordering. Impressed by the...',
                date: '2 weeks ago',
                avatar: 'H'
            }
        ];

        sampleReviews.slice(0, this.config.settings.maxReviews).forEach(review => {
            if (review.rating >= this.config.settings.minRating) {
                const reviewElement = this.createReviewElement(review);
                content.appendChild(reviewElement);
            }
        });

        return content;
    }

    private createReviewElement(review: any): HTMLElement {
        const reviewEl = document.createElement('div');
        reviewEl.className = 'grw-review-item';
        reviewEl.style.cssText = `
            display: flex;
            gap: 12px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        `;

        // Avatar
        const avatar = document.createElement('div');
        avatar.style.cssText = `
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: ${this.config.styling.colors.primary};
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        `;
        avatar.textContent = review.avatar;
        reviewEl.appendChild(avatar);

        // Content
        const contentEl = document.createElement('div');
        contentEl.style.cssText = 'flex: 1; min-width: 0;';

        // Name and rating
        const header = document.createElement('div');
        header.style.cssText = 'display: flex; align-items: center; gap: 8px; margin-bottom: 4px;';

        const name = document.createElement('span');
        name.textContent = review.name;
        name.style.cssText = 'font-weight: 500; font-size: 14px;';
        header.appendChild(name);

        const stars = document.createElement('span');
        stars.textContent = '★'.repeat(review.rating);
        stars.style.cssText = `color: ${this.config.styling.colors.secondary}; font-size: 12px;`;
        header.appendChild(stars);

        contentEl.appendChild(header);

        // Review text
        const text = document.createElement('p');
        text.textContent = review.text;
        text.style.cssText = 'margin: 0 0 4px 0; font-size: 13px; line-height: 1.4;';
        contentEl.appendChild(text);

        // Date
        if (this.config.settings.showDates) {
            const date = document.createElement('span');
            date.textContent = review.date;
            date.style.cssText = 'font-size: 11px; color: #666; opacity: 0.8;';
            contentEl.appendChild(date);
        }

        reviewEl.appendChild(contentEl);

        return reviewEl;
    }
}

// Initialize the platform when script loads
const platform = GoogleReviewsWidgetPlatform.getInstance();

// Export for manual widget loading if needed
(window as any).GoogleReviewsWidgetPlatform = platform;

// Also provide a simple API for manual widget rendering (backward compatibility)
(window as any).GoogleReviewsWidget = {
    render: (container: HTMLElement, config: WidgetConfig) => {
        const renderer = new (require('./components/ReviewsDisplay').WidgetRenderer)(container, config);
        renderer.render();
    }
};
