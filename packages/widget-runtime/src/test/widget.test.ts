import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { J<PERSON><PERSON> } from 'jsdom';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock AbortSignal.timeout for older environments
if (!global.AbortSignal.timeout) {
  global.AbortSignal.timeout = vi.fn().mockReturnValue(new AbortController().signal);
}

// Set up DOM environment
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
  url: 'http://localhost',
  pretendToBeVisual: true,
  resources: 'usable'
});

global.window = dom.window as any;
global.document = dom.window.document;
global.HTMLElement = dom.window.HTMLElement;
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn()
}));

// Import the widget after setting up the environment
import GoogleReviewsWidget from '../index.js';

describe('GoogleReviewsWidget - Data Loading and Caching', () => {
  let widget: GoogleReviewsWidget;
  let container: HTMLElement;

  const mockWidgetData = {
    reviews: [
      {
        id: 'review-1',
        authorName: 'John Doe',
        authorPhotoUrl: 'https://example.com/john.jpg',
        rating: 5,
        text: 'Great service!',
        publishedDate: '2023-01-01T00:00:00.000Z',
        isVerified: true
      },
      {
        id: 'review-2',
        authorName: 'Jane Smith',
        rating: 4,
        text: 'Good experience overall.',
        publishedDate: '2023-01-02T00:00:00.000Z',
        isVerified: false
      }
    ],
    businessInfo: {
      name: 'Test Business',
      rating: 4.5,
      reviewCount: 100
    },
    totalReviews: 100,
    averageRating: 4.5,
    lastUpdated: '2023-01-01T00:00:00.000Z'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset DOM
    document.body.innerHTML = '';
    
    // Create container
    container = document.createElement('div');
    container.id = 'widget-container';
    document.body.appendChild(container);

    // Reset fetch mock
    mockFetch.mockClear();
  });

  afterEach(() => {
    if (widget) {
      widget.unmount();
    }
    vi.clearAllTimers();
  });

  describe('Data Loading', () => {
    it('should load widget data successfully', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map([['ETag', '"test-etag"']]),
        json: () => Promise.resolve(mockWidgetData)
      });

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for async initialization
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/widget/test-widget/data',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          })
        })
      );
    });

    it('should show skeleton loader while loading', async () => {
      // Arrange
      let resolvePromise: (value: any) => void;
      const loadingPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });

      mockFetch.mockReturnValueOnce(loadingPromise);

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for skeleton to appear
      await new Promise(resolve => setTimeout(resolve, 50));

      // Assert
      const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
      expect(shadowRoot?.querySelector('.grw-skeleton-container')).toBeTruthy();

      // Resolve the promise to clean up
      resolvePromise!({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(mockWidgetData)
      });
    });

    it('should handle network errors with retry', async () => {
      // Arrange
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map(),
          json: () => Promise.resolve(mockWidgetData)
        });

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for retries
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Assert
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should fallback to mock data in development', async () => {
      // Arrange
      mockFetch.mockRejectedValue(new Error('Network error'));

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001' // localhost triggers mock data fallback
      });

      widget.mount('#widget-container');

      // Wait for fallback
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Assert - Should not throw and should render something
      const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
      expect(shadowRoot).toBeTruthy();
    });

    it('should categorize different error types correctly', async () => {
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001/api'
      });

      // Test timeout error
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'AbortError';
      expect((widget as any).categorizeError(timeoutError)).toBe('timeout');

      // Test network error
      const networkError = new TypeError('fetch failed');
      expect((widget as any).categorizeError(networkError)).toBe('network');

      // Test client error
      const clientError = new Error('HTTP 404: Not Found');
      expect((widget as any).categorizeError(clientError)).toBe('client-error');

      // Test server error
      const serverError = new Error('HTTP 500: Internal Server Error');
      expect((widget as any).categorizeError(serverError)).toBe('server-error');

      // Test parse error
      const parseError = new Error('JSON parse error');
      expect((widget as any).categorizeError(parseError)).toBe('parse-error');

      // Test CORS error
      const corsError = new Error('CORS policy blocked');
      expect((widget as any).categorizeError(corsError)).toBe('cors-error');

      // Test unknown error
      const unknownError = new Error('Unknown issue');
      expect((widget as any).categorizeError(unknownError)).toBe('unknown');
    });

    it('should provide appropriate error messages for different error types', async () => {
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001/api'
      });

      expect((widget as any).getErrorMessage('network')).toBe('Unable to connect to the server. Please check your internet connection.');
      expect((widget as any).getErrorMessage('timeout')).toBe('Request timed out. Please try again later.');
      expect((widget as any).getErrorMessage('server-error')).toBe('Server error occurred. Please try again later.');
      expect((widget as any).getErrorMessage('client-error')).toBe('Widget configuration error. Please check your setup.');
      expect((widget as any).getErrorMessage('parse-error')).toBe('Invalid response from server. Please try again later.');
      expect((widget as any).getErrorMessage('cors-error')).toBe('Cross-origin request blocked. Please check widget configuration.');
    });

    it('should not retry client errors', async () => {
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001/api'
      });

      expect((widget as any).shouldRetry('client-error')).toBe(false);
      expect((widget as any).shouldRetry('parse-error')).toBe(false);
      expect((widget as any).shouldRetry('cors-error')).toBe(false);
      expect((widget as any).shouldRetry('network')).toBe(true);
      expect((widget as any).shouldRetry('server-error')).toBe(true);
    });

    it('should show stale data notice when serving cached data', async () => {
      // Arrange - Set up cache with stale data
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001/api'
      });

      // Mock cache to return stale data
      const mockCache = {
        get: vi.fn().mockReturnValue(null),
        set: vi.fn(),
        getStale: vi.fn().mockReturnValue(mockWidgetData),
        clear: vi.fn()
      };
      (widget as any).cache = mockCache;

      // Mock fetch to fail
      mockFetch.mockRejectedValue(new Error('Network error'));

      // Act
      widget.mount('#widget-container');
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
      const staleNotice = shadowRoot?.querySelector('.grw-stale-notice');
      expect(staleNotice).toBeTruthy();
      expect(staleNotice?.textContent).toContain('Offline');
    });

    it('should handle HTTP error responses', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'nonexistent-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for error handling
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert
      expect(mockFetch).toHaveBeenCalled();
    });
  });

  describe('Client-side Caching', () => {
    it('should cache successful responses', async () => {
      // Arrange
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['ETag', '"test-etag"']]),
        json: () => Promise.resolve(mockWidgetData)
      });

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for initial load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Refresh should use cache
      await widget.refresh();

      // Assert
      expect(mockFetch).toHaveBeenCalledTimes(2); // Initial + refresh (cache cleared)
    });

    it('should use ETag for conditional requests', async () => {
      // Arrange
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['ETag', '"test-etag"']]),
          json: () => Promise.resolve(mockWidgetData)
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 304,
          headers: new Map()
        });

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for initial load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Clear cache and reload to trigger conditional request
      widget.clearCache();
      await widget.refresh();

      // Assert
      const secondCall = mockFetch.mock.calls[1];
      expect(secondCall[1].headers['If-None-Match']).toBe('"test-etag"');
    });

    it('should serve stale data on error', async () => {
      // Arrange
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map(),
          json: () => Promise.resolve(mockWidgetData)
        })
        .mockRejectedValueOnce(new Error('Network error'));

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for initial load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Trigger error scenario
      await widget.refresh();

      // Assert - Should not throw and should still have data
      const stats = widget.getCacheStats();
      expect(stats.hasData).toBe(true);
    });

    it('should clear cache when requested', () => {
      // Arrange
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      // Act
      widget.clearCache();
      const stats = widget.getCacheStats();

      // Assert
      expect(stats.hasData).toBe(false);
    });

    it('should provide cache statistics', async () => {
      // Arrange
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(mockWidgetData)
      });

      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Act
      const stats = widget.getCacheStats();

      // Assert
      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('hasData');
      expect(typeof stats.size).toBe('number');
      expect(typeof stats.hasData).toBe('boolean');
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON responses', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.reject(new Error('Invalid JSON'))
      });

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for error handling
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert - Should not crash
      expect(widget.isReady()).toBe(true);
    });

    it('should handle timeout errors', async () => {
      // Arrange
      mockFetch.mockRejectedValueOnce(new Error('Request timeout'));

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for timeout handling
      await new Promise(resolve => setTimeout(resolve, 100));

      // Assert - Should handle gracefully
      expect(widget.isReady()).toBe(true);
    });

    it('should respect maximum retry attempts', async () => {
      // Arrange
      mockFetch.mockRejectedValue(new Error('Persistent error'));

      // Act
      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://production-api.com' // Non-localhost to avoid mock fallback
      });

      widget.mount('#widget-container');

      // Wait for all retries
      await new Promise(resolve => setTimeout(resolve, 10000));

      // Assert - Should not exceed max retries (3) + initial attempt = 4
      expect(mockFetch).toHaveBeenCalledTimes(4);
    });
  });

  describe('Widget Lifecycle', () => {
    it('should refresh data when refresh() is called', async () => {
      // Arrange
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(mockWidgetData)
      });

      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for initial load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Act
      await widget.refresh();

      // Assert
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should handle concurrent refresh calls', async () => {
      // Arrange
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve(mockWidgetData)
      });

      widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001'
      });

      widget.mount('#widget-container');

      // Wait for initial load
      await new Promise(resolve => setTimeout(resolve, 100));

      // Act - Call refresh multiple times concurrently
      const refreshPromises = [
        widget.refresh(),
        widget.refresh(),
        widget.refresh()
      ];

      await Promise.all(refreshPromises);

      // Assert - Should handle concurrent calls gracefully
      expect(mockFetch).toHaveBeenCalledTimes(2); // Initial + one refresh (others should be ignored)
    });
  });
});