import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { <PERSON><PERSON><PERSON> } from 'jsdom';

// Mock different browser environments
const createBrowserEnvironment = (userAgent: string, features: any = {}) => {
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>', {
    url: 'http://localhost',
    pretendToBeVisual: true,
    resources: 'usable',
    userAgent,
  });

  // Apply browser-specific features
  Object.assign(dom.window, features);
  
  return dom;
};

// Browser configurations
const browsers = {
  chrome: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    features: {
      ResizeObserver: class ResizeObserver {
        observe() {}
        unobserve() {}
        disconnect() {}
      },
      IntersectionObserver: class IntersectionObserver {
        constructor() {}
        observe() {}
        unobserve() {}
        disconnect() {}
      },
    },
  },
  firefox: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    features: {
      ResizeObserver: class ResizeObserver {
        observe() {}
        unobserve() {}
        disconnect() {}
      },
    },
  },
  safari: {
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    features: {
      // Safari has limited support for some features
    },
  },
  edge: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    features: {
      ResizeObserver: class ResizeObserver {
        observe() {}
        unobserve() {}
        disconnect() {}
      },
    },
  },
  ie11: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
    features: {
      // IE11 lacks many modern features
      fetch: undefined,
      Promise: undefined,
      ResizeObserver: undefined,
      IntersectionObserver: undefined,
    },
  },
};

describe('Cross-Browser Compatibility Tests', () => {
  let originalWindow: any;
  let originalDocument: any;
  let originalGlobal: any;

  beforeEach(() => {
    // Store original globals
    originalWindow = global.window;
    originalDocument = global.document;
    originalGlobal = { ...global };
  });

  afterEach(() => {
    // Restore original globals
    global.window = originalWindow;
    global.document = originalDocument;
    Object.assign(global, originalGlobal);
    vi.clearAllMocks();
  });

  Object.entries(browsers).forEach(([browserName, config]) => {
    describe(`${browserName.toUpperCase()} Browser Tests`, () => {
      beforeEach(() => {
        const dom = createBrowserEnvironment(config.userAgent, config.features);
        global.window = dom.window as any;
        global.document = dom.window.document;
        
        // Apply browser-specific features to global scope
        Object.assign(global, config.features);
      });

      it('should initialize widget without errors', async () => {
        // Mock fetch for browsers that support it
        if (browserName !== 'ie11') {
          global.fetch = vi.fn().mockResolvedValue({
            ok: true,
            json: () => Promise.resolve({
              reviews: [],
              businessInfo: { name: 'Test', rating: 4.5, reviewCount: 10 },
              totalReviews: 10,
              averageRating: 4.5,
              lastUpdated: new Date().toISOString(),
            }),
          });
        }

        // Import widget after setting up environment
        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        container.id = 'widget-container';
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
        });

        expect(() => {
          widget.mount('#widget-container');
        }).not.toThrow();

        widget.unmount();
      });

      it('should handle missing modern APIs gracefully', async () => {
        // Remove modern APIs to test fallbacks
        delete global.ResizeObserver;
        delete global.IntersectionObserver;
        
        if (browserName === 'ie11') {
          delete global.fetch;
          delete global.Promise;
        }

        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
        });

        expect(() => {
          widget.mount(container);
        }).not.toThrow();

        widget.unmount();
      });

      it('should apply correct CSS for browser compatibility', async () => {
        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
        });

        widget.mount(container);

        // Wait for widget to render
        await new Promise(resolve => setTimeout(resolve, 100));

        const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
        expect(shadowRoot).toBeTruthy();

        const styles = shadowRoot?.querySelector('style');
        expect(styles).toBeTruthy();

        const cssText = styles?.textContent || '';

        // Check for browser-specific CSS properties
        if (browserName === 'safari' || browserName === 'ie11') {
          // Should include vendor prefixes for older browsers
          expect(cssText).toMatch(/-webkit-|-ms-/);
        }

        // Check for flexbox fallbacks for IE11
        if (browserName === 'ie11') {
          expect(cssText).toContain('display: -ms-flexbox');
        }

        widget.unmount();
      });

      it('should handle events correctly across browsers', async () => {
        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
        });

        widget.mount(container);

        // Wait for widget to render
        await new Promise(resolve => setTimeout(resolve, 100));

        const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
        const buttons = shadowRoot?.querySelectorAll('button');

        if (buttons && buttons.length > 0) {
          const button = buttons[0];
          
          // Test click event
          expect(() => {
            button.click();
          }).not.toThrow();

          // Test keyboard events
          expect(() => {
            const event = new KeyboardEvent('keydown', { key: 'Enter' });
            button.dispatchEvent(event);
          }).not.toThrow();
        }

        widget.unmount();
      });

      it('should handle touch events on mobile browsers', async () => {
        // Simulate mobile browser
        Object.defineProperty(global.window, 'ontouchstart', {
          value: {},
          writable: true,
        });

        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
        });

        widget.mount(container);

        // Wait for widget to render
        await new Promise(resolve => setTimeout(resolve, 100));

        const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
        const touchableElements = shadowRoot?.querySelectorAll('[data-touchable]');

        if (touchableElements && touchableElements.length > 0) {
          const element = touchableElements[0];
          
          // Test touch events
          expect(() => {
            const touchEvent = new TouchEvent('touchstart', {
              touches: [{ clientX: 100, clientY: 100 } as Touch],
            });
            element.dispatchEvent(touchEvent);
          }).not.toThrow();
        }

        widget.unmount();
      });

      it('should load and cache data appropriately for browser', async () => {
        let fetchCalled = false;
        
        if (browserName !== 'ie11') {
          global.fetch = vi.fn().mockImplementation(() => {
            fetchCalled = true;
            return Promise.resolve({
              ok: true,
              json: () => Promise.resolve({
                reviews: [
                  {
                    id: 'review-1',
                    authorName: 'Test User',
                    rating: 5,
                    text: 'Great!',
                    publishedDate: new Date().toISOString(),
                    isVerified: true,
                  },
                ],
                businessInfo: { name: 'Test', rating: 4.5, reviewCount: 10 },
                totalReviews: 10,
                averageRating: 4.5,
                lastUpdated: new Date().toISOString(),
              }),
            });
          });
        }

        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
        });

        widget.mount(container);

        // Wait for data loading
        await new Promise(resolve => setTimeout(resolve, 200));

        if (browserName !== 'ie11') {
          expect(fetchCalled).toBe(true);
        } else {
          // IE11 should fall back to mock data or XMLHttpRequest
          expect(widget.isReady()).toBe(true);
        }

        widget.unmount();
      });

      it('should handle CSS custom properties fallbacks', async () => {
        const { default: GoogleReviewsWidget } = await import('../index.js');

        const container = document.createElement('div');
        document.body.appendChild(container);

        const widget = new GoogleReviewsWidget({
          widgetId: 'test-widget',
          apiUrl: 'http://localhost:3001',
          config: {
            styling: {
              colors: {
                primary: '#007bff',
                background: '#ffffff',
              },
            },
          },
        });

        widget.mount(container);

        // Wait for widget to render
        await new Promise(resolve => setTimeout(resolve, 100));

        const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
        const styles = shadowRoot?.querySelector('style');
        const cssText = styles?.textContent || '';

        if (browserName === 'ie11') {
          // Should not use CSS custom properties
          expect(cssText).not.toContain('var(--');
          expect(cssText).not.toContain('--primary-color');
        } else {
          // Modern browsers can use CSS custom properties
          expect(cssText).toContain('--primary-color');
        }

        widget.unmount();
      });
    });
  });

  describe('Feature Detection Tests', () => {
    it('should detect and polyfill missing features', async () => {
      // Remove all modern features
      delete global.fetch;
      delete global.Promise;
      delete global.ResizeObserver;
      delete global.IntersectionObserver;
      delete global.CustomEvent;

      const { default: GoogleReviewsWidget } = await import('../index.js');

      const container = document.createElement('div');
      document.body.appendChild(container);

      const widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001',
      });

      // Should not throw even without modern features
      expect(() => {
        widget.mount(container);
      }).not.toThrow();

      widget.unmount();
    });

    it('should provide appropriate fallbacks for unsupported CSS', async () => {
      // Mock a browser that doesn't support modern CSS
      const originalSupports = global.CSS?.supports;
      
      if (global.CSS) {
        global.CSS.supports = vi.fn().mockReturnValue(false);
      }

      const { default: GoogleReviewsWidget } = await import('../index.js');

      const container = document.createElement('div');
      document.body.appendChild(container);

      const widget = new GoogleReviewsWidget({
        widgetId: 'test-widget',
        apiUrl: 'http://localhost:3001',
      });

      widget.mount(container);

      // Wait for widget to render
      await new Promise(resolve => setTimeout(resolve, 100));

      const shadowRoot = container.querySelector('[data-widget-id="test-widget"]')?.shadowRoot;
      const styles = shadowRoot?.querySelector('style');
      const cssText = styles?.textContent || '';

      // Should include fallback CSS
      expect(cssText).toContain('display: block'); // Fallback for flex
      expect(cssText).toContain('float: left'); // Fallback for modern layouts

      // Restore original CSS.supports
      if (global.CSS && originalSupports) {
        global.CSS.supports = originalSupports;
      }

      widget.unmount();
    });
  });
});