// Service Worker for widget asset caching
declare const self: ServiceWorkerGlobalScope;

const CACHE_NAME = 'grw-widget-cache-v1';
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

// Assets to cache immediately
const STATIC_ASSETS = [
  '/widget.js',
  '/widget.css'
];

// API endpoints to cache with different strategies
const API_CACHE_PATTERNS = [
  /\/api\/widget\/[^\/]+\/data$/,
  /\/api\/reviews\//,
  /\/api\/business\//
];

// Image patterns to cache
const IMAGE_CACHE_PATTERNS = [
  /googleusercontent\.com/,
  /\.(?:png|jpg|jpeg|svg|gif|webp)$/i
];

interface CacheEntry {
  data: any;
  timestamp: number;
  etag?: string;
}

class WidgetServiceWorker {
  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    self.addEventListener('install', (event) => {
      event.waitUntil(this.handleInstall());
    });

    self.addEventListener('activate', (event) => {
      event.waitUntil(this.handleActivate());
    });

    self.addEventListener('fetch', (event) => {
      event.respondWith(this.handleFetch(event.request));
    });

    self.addEventListener('message', (event) => {
      this.handleMessage(event);
    });
  }

  private async handleInstall(): Promise<void> {
    console.log('[SW] Installing widget service worker');
    
    try {
      const cache = await caches.open(CACHE_NAME);
      
      // Cache static assets
      const staticAssetPromises = STATIC_ASSETS.map(async (asset) => {
        try {
          const response = await fetch(asset);
          if (response.ok) {
            await cache.put(asset, response);
          }
        } catch (error) {
          console.warn(`[SW] Failed to cache static asset: ${asset}`, error);
        }
      });

      await Promise.allSettled(staticAssetPromises);
      
      // Skip waiting to activate immediately
      self.skipWaiting();
    } catch (error) {
      console.error('[SW] Installation failed:', error);
    }
  }

  private async handleActivate(): Promise<void> {
    console.log('[SW] Activating widget service worker');
    
    try {
      // Clean up old caches
      const cacheNames = await caches.keys();
      const deletePromises = cacheNames
        .filter(name => name.startsWith('grw-widget-cache-') && name !== CACHE_NAME)
        .map(name => caches.delete(name));
      
      await Promise.all(deletePromises);
      
      // Take control of all clients immediately
      await self.clients.claim();
    } catch (error) {
      console.error('[SW] Activation failed:', error);
    }
  }

  private async handleFetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    
    try {
      // Handle different types of requests with appropriate caching strategies
      if (this.isStaticAsset(url)) {
        return await this.handleStaticAsset(request);
      }
      
      if (this.isApiRequest(url)) {
        return await this.handleApiRequest(request);
      }
      
      if (this.isImageRequest(url)) {
        return await this.handleImageRequest(request);
      }
      
      // For other requests, use network-first strategy
      return await this.networkFirst(request);
    } catch (error) {
      console.error('[SW] Fetch error:', error);
      return await this.handleFetchError(request, error);
    }
  }

  private isStaticAsset(url: URL): boolean {
    return STATIC_ASSETS.some(asset => url.pathname.endsWith(asset));
  }

  private isApiRequest(url: URL): boolean {
    return API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
  }

  private isImageRequest(url: URL): boolean {
    return IMAGE_CACHE_PATTERNS.some(pattern => pattern.test(url.href));
  }

  // Cache-first strategy for static assets
  private async handleStaticAsset(request: Request): Promise<Response> {
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse && !this.isCacheExpired(cachedResponse)) {
      return cachedResponse;
    }
    
    try {
      const networkResponse = await fetch(request);
      if (networkResponse.ok) {
        await cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    } catch (error) {
      // Return stale cache if network fails
      if (cachedResponse) {
        return cachedResponse;
      }
      throw error;
    }
  }

  // Stale-while-revalidate strategy for API requests
  private async handleApiRequest(request: Request): Promise<Response> {
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    // Start network request in background
    const networkPromise = this.fetchAndCache(request, cache);
    
    // Return cached response if available and not too old
    if (cachedResponse && !this.isCacheExpired(cachedResponse)) {
      // Update cache in background
      networkPromise.catch(() => {
        // Ignore network errors when serving from cache
      });
      return cachedResponse;
    }
    
    // Wait for network response if no cache or cache is expired
    try {
      return await networkPromise;
    } catch (error) {
      // Return stale cache as fallback
      if (cachedResponse) {
        return cachedResponse;
      }
      throw error;
    }
  }

  // Cache-first strategy for images with long TTL
  private async handleImageRequest(request: Request): Promise<Response> {
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    try {
      const networkResponse = await fetch(request);
      if (networkResponse.ok) {
        // Cache images for longer period
        const responseToCache = networkResponse.clone();
        await cache.put(request, responseToCache);
      }
      return networkResponse;
    } catch (error) {
      // Return placeholder image on error
      return this.createPlaceholderImageResponse();
    }
  }

  // Network-first strategy for other requests
  private async networkFirst(request: Request): Promise<Response> {
    try {
      const networkResponse = await fetch(request);
      
      // Cache successful GET requests
      if (request.method === 'GET' && networkResponse.ok) {
        const cache = await caches.open(CACHE_NAME);
        await cache.put(request, networkResponse.clone());
      }
      
      return networkResponse;
    } catch (error) {
      // Try to serve from cache
      const cache = await caches.open(CACHE_NAME);
      const cachedResponse = await cache.match(request);
      
      if (cachedResponse) {
        return cachedResponse;
      }
      
      throw error;
    }
  }

  private async fetchAndCache(request: Request, cache: Cache): Promise<Response> {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  }

  private isCacheExpired(response: Response): boolean {
    const dateHeader = response.headers.get('date');
    if (!dateHeader) return false;
    
    const responseDate = new Date(dateHeader);
    const now = new Date();
    
    return (now.getTime() - responseDate.getTime()) > CACHE_EXPIRY;
  }

  private createPlaceholderImageResponse(): Response {
    // Create a simple SVG placeholder
    const svg = `
      <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <circle cx="20" cy="16" r="10" fill="#d0d0d0"/>
        <path d="M10 28 Q20 24 30 28 L30 34 L10 34 Z" fill="#d0d0d0"/>
      </svg>
    `;
    
    return new Response(svg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=86400'
      }
    });
  }

  private async handleFetchError(request: Request, error: any): Promise<Response> {
    // Try to serve from cache as last resort
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return appropriate error response based on request type
    if (this.isApiRequest(new URL(request.url))) {
      return new Response(
        JSON.stringify({ error: 'Service temporarily unavailable' }),
        {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (this.isImageRequest(new URL(request.url))) {
      return this.createPlaceholderImageResponse();
    }
    
    // Generic error response
    return new Response('Service temporarily unavailable', {
      status: 503,
      headers: { 'Content-Type': 'text/plain' }
    });
  }

  private handleMessage(event: ExtendableMessageEvent): void {
    const { type, payload } = event.data;
    
    switch (type) {
      case 'CACHE_WIDGET_DATA':
        this.cacheWidgetData(payload);
        break;
      case 'CLEAR_CACHE':
        this.clearCache(payload?.pattern);
        break;
      case 'GET_CACHE_STATUS':
        this.getCacheStatus().then(status => {
          event.ports[0]?.postMessage(status);
        });
        break;
    }
  }

  private async cacheWidgetData(payload: { widgetId: string; data: any }): Promise<void> {
    try {
      const cache = await caches.open(CACHE_NAME);
      const url = `/api/widget/${payload.widgetId}/data`;
      
      const response = new Response(JSON.stringify(payload.data), {
        headers: {
          'Content-Type': 'application/json',
          'Date': new Date().toISOString()
        }
      });
      
      await cache.put(url, response);
    } catch (error) {
      console.error('[SW] Failed to cache widget data:', error);
    }
  }

  private async clearCache(pattern?: string): Promise<void> {
    try {
      const cache = await caches.open(CACHE_NAME);
      
      if (pattern) {
        const requests = await cache.keys();
        const deletePromises = requests
          .filter(request => new RegExp(pattern).test(request.url))
          .map(request => cache.delete(request));
        
        await Promise.all(deletePromises);
      } else {
        await caches.delete(CACHE_NAME);
        await caches.open(CACHE_NAME); // Recreate empty cache
      }
    } catch (error) {
      console.error('[SW] Failed to clear cache:', error);
    }
  }

  private async getCacheStatus(): Promise<any> {
    try {
      const cache = await caches.open(CACHE_NAME);
      const requests = await cache.keys();
      
      return {
        cacheSize: requests.length,
        cacheName: CACHE_NAME,
        cachedUrls: requests.map(req => req.url)
      };
    } catch (error) {
      console.error('[SW] Failed to get cache status:', error);
      return { error: 'Failed to get cache status' };
    }
  }
}

// Initialize the service worker
new WidgetServiceWorker();