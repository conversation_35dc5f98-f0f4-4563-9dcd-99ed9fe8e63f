{"name": "@grwg/widget-runtime", "version": "1.0.0", "description": "Lightweight embeddable widget runtime for Google Reviews", "private": true, "type": "module", "main": "dist/widget.js", "scripts": {"dev": "vite build --watch", "build": "vite build", "build:analyze": "npm run build && npm run bundle-size", "preview": "vite preview --host 0.0.0.0 --port ${PORT:-3002}", "test": "vitest run", "test:watch": "vitest", "test:unit": "vitest run --reporter=verbose", "test:cross-browser": "vitest run --reporter=verbose src/test/cross-browser.test.ts", "test:performance": "vitest run --reporter=verbose src/test/performance.test.ts", "test:all": "npm run test:unit && npm run test:cross-browser && npm run test:performance", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix", "bundle-size": "ls -lh dist/ && echo 'Widget bundle size:' && du -sh dist/widget.iife.js", "performance": "npm run test:performance && npm run bundle-size"}, "dependencies": {"axios": "^1.4.0"}, "devDependencies": {"@types/node": "^20.4.0", "vite": "^4.4.0", "typescript": "^5.1.0", "vitest": "^0.33.0", "jsdom": "^22.1.0", "terser": "^5.19.0", "playwright": "^1.40.0", "@playwright/test": "^1.40.0", "puppeteer": "^21.6.0"}}