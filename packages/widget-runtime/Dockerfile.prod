FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY packages/widget-runtime/package*.json ./packages/widget-runtime/

# Install dependencies
RUN npm ci

# Copy source code
COPY packages/widget-runtime ./packages/widget-runtime

# Build the application
WORKDIR /app/packages/widget-runtime
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/packages/widget-runtime/dist /usr/share/nginx/html

# Copy nginx configuration for widget assets
COPY packages/widget-runtime/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]