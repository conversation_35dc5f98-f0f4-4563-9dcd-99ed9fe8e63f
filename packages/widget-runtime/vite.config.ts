import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    outDir: 'dist',
    sourcemap: false, // Disable sourcemaps for production to reduce bundle size
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log statements
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.warn'], // Remove specific console methods
        passes: 2, // Run compression twice for better results
      },
      mangle: {
        properties: {
          regex: /^_/, // Mangle private properties starting with _
        },
      },
      format: {
        comments: false, // Remove all comments
      },
    },
    rollupOptions: {
      input: {
        widget: 'src/index.ts',
        'widget-platform': 'src/widget-platform.ts',
      },
      output: {
        entryFileNames: '[name].js',
        format: 'es',
        // Optimize for smaller bundle size
        compact: true,
      },
      // Enable tree shaking by marking side effects
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        unknownGlobalSideEffects: false,
      },
      // External dependencies that should not be bundled
      external: [],
    },
    // Target modern browsers for smaller bundle
    target: ['es2020', 'chrome80', 'firefox78', 'safari14'],
    // Optimize chunk size
    chunkSizeWarningLimit: 50, // Widget should be under 50KB
  },
  server: {
    port: 3002,
    host: true,
  },
  test: {
    globals: true,
    environment: 'jsdom',
  },
  // Enable tree shaking in development too
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
});
