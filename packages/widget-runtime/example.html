<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Reviews Widget - Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .example {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .example h3 {
            margin-top: 0;
            color: #333;
        }
        
        .widget-container {
            margin-top: 20px;
            min-height: 300px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .controls {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
        
        .embed-example {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
        }
        
        .embed-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Google Reviews Widget Examples</h1>
            <p>Interactive examples of the embeddable Google Reviews widget with different layouts and configurations</p>
        </div>
        
        <div class="examples">
            <div class="example">
                <h3>Carousel Layout</h3>
                <p>Default carousel with navigation and pagination</p>
                <div class="widget-container" id="widget-1"></div>
                <div class="controls">
                    <button onclick="toggleTheme('widget-1')">Toggle Theme</button>
                    <button onclick="refreshWidget('widget-1')">Refresh</button>
                    <button onclick="toggleAutoPlay('widget-1')">Toggle AutoPlay</button>
                </div>
            </div>
            
            <div class="example">
                <h3>Grid Layout</h3>
                <p>Grid layout with 2 columns showing multiple reviews</p>
                <div class="widget-container" id="widget-2"></div>
                <div class="controls">
                    <button onclick="toggleLayout('widget-2')">Change Layout</button>
                    <button onclick="toggleColumns('widget-2')">Toggle Columns</button>
                </div>
            </div>
            
            <div class="example">
                <h3>List Layout</h3>
                <p>Simple vertical list of reviews</p>
                <div class="widget-container" id="widget-3"></div>
                <div class="controls">
                    <button onclick="toggleTextLength('widget-3')">Toggle Text Length</button>
                    <button onclick="togglePhotos('widget-3')">Toggle Photos</button>
                </div>
            </div>
            
            <div class="example">
                <h3>Auto-play Carousel</h3>
                <p>Carousel with auto-play and smooth transitions</p>
                <div class="widget-container" id="widget-4"></div>
                <div class="controls">
                    <button onclick="pauseAutoPlay('widget-4')">Pause/Resume</button>
                    <button onclick="changeSpeed('widget-4')">Change Speed</button>
                </div>
            </div>
        </div>
        
        <div class="embed-example">
            <h3>Embed Code Example</h3>
            <p>Here's how you would embed the widget on your website:</p>
            <div class="embed-code">
&lt;script 
  src="https://your-domain.com/widget.js"
  data-grw-widget-id="your-widget-id"
  data-grw-layout="carousel"
  data-grw-theme="light"
  data-grw-max-reviews="5"
  data-grw-show-photos="true"
  data-grw-auto-play="false"&gt;
&lt;/script&gt;
            </div>
            <p>The widget will automatically initialize and display your Google reviews!</p>
        </div>
    </div>

    <!-- Load the widget script -->
    <script src="dist/widget.js"></script>
    
    <script>
        // Widget instances
        const widgets = {};
        
        // Initialize widgets when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Carousel widget
            widgets['widget-1'] = GoogleReviewsWidgetAPI.create({
                widgetId: 'demo-widget-1',
                layout: 'carousel',
                maxReviews: 4,
                showNavigation: true,
                showPagination: true,
                itemsPerPage: 1,
                autoPlay: false
            });
            widgets['widget-1'].mount('#widget-1');
            
            // Grid layout widget
            widgets['widget-2'] = GoogleReviewsWidgetAPI.create({
                widgetId: 'demo-widget-2',
                layout: 'grid',
                gridColumns: 2,
                maxReviews: 4,
                showNavigation: false,
                showPagination: false
            });
            widgets['widget-2'].mount('#widget-2');
            
            // List layout widget
            widgets['widget-3'] = GoogleReviewsWidgetAPI.create({
                widgetId: 'demo-widget-3',
                layout: 'list',
                maxReviews: 4,
                showNavigation: false,
                showPagination: false,
                maxTextLength: 100
            });
            widgets['widget-3'].mount('#widget-3');
            
            // Auto-play carousel
            widgets['widget-4'] = GoogleReviewsWidgetAPI.create({
                widgetId: 'demo-widget-4',
                layout: 'carousel',
                autoPlay: true,
                autoPlayInterval: 4000,
                maxReviews: 4,
                showNavigation: true,
                showPagination: true,
                itemsPerPage: 1
            });
            widgets['widget-4'].mount('#widget-4');
        });
        
        // Control functions
        function toggleTheme(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                const newTheme = currentConfig.theme === 'dark' ? 'light' : 'dark';
                widget.updateConfig({ theme: newTheme });
            }
        }
        
        function refreshWidget(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                widget.unmount();
                setTimeout(() => {
                    const newWidget = GoogleReviewsWidgetAPI.create({
                        widgetId: 'demo-widget-1-refresh',
                        layout: 'carousel',
                        maxReviews: 4,
                        showNavigation: true,
                        showPagination: true
                    });
                    newWidget.mount('#' + widgetId);
                    widgets[widgetId] = newWidget;
                }, 100);
            }
        }
        
        function toggleLayout(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                const layouts = ['carousel', 'grid', 'list'];
                const currentIndex = layouts.indexOf(currentConfig.layout);
                const nextLayout = layouts[(currentIndex + 1) % layouts.length];
                
                widget.updateConfig({ 
                    layout: nextLayout,
                    showNavigation: nextLayout === 'carousel',
                    showPagination: nextLayout === 'carousel'
                });
            }
        }
        
        function toggleAutoPlay(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                widget.updateConfig({ 
                    autoPlay: !currentConfig.autoPlay,
                    layout: 'carousel' // Ensure carousel layout for autoplay
                });
            }
        }
        
        function toggleColumns(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                const newColumns = currentConfig.gridColumns === 2 ? 3 : 2;
                widget.updateConfig({ 
                    gridColumns: newColumns,
                    layout: 'grid'
                });
            }
        }
        
        function toggleTextLength(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                const newLength = currentConfig.maxTextLength === 100 ? 200 : 100;
                widget.updateConfig({ maxTextLength: newLength });
            }
        }
        
        function togglePhotos(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                widget.updateConfig({ showPhotos: !currentConfig.showPhotos });
            }
        }
        
        function pauseAutoPlay(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                widget.updateConfig({ autoPlay: !currentConfig.autoPlay });
            }
        }
        
        function changeSpeed(widgetId) {
            const widget = widgets[widgetId];
            if (widget) {
                const currentConfig = widget.getConfig();
                const speeds = [2000, 3000, 4000, 6000];
                const currentIndex = speeds.indexOf(currentConfig.autoPlayInterval);
                const nextSpeed = speeds[(currentIndex + 1) % speeds.length];
                widget.updateConfig({ 
                    autoPlayInterval: nextSpeed,
                    autoPlay: true
                });
            }
        }
    </script>
</body>
</html>