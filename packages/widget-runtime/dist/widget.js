var t=Object.defineProperty,n=(n,e,i)=>(((n,e,i)=>{e in n?t(n,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[e]=i})(n,"symbol"!=typeof e?e+"":e,i),i);const e=new class{constructor(){n(this,"observer"),n(this,"loadedImages",new Set),this.initIntersectionObserver()}initIntersectionObserver(){"undefined"!=typeof IntersectionObserver&&(this.observer=new IntersectionObserver(t=>{t.forEach(t=>{var n;if(t.isIntersecting){const e=t.target;this.loadImage(e),null==(n=this.observer)||n.unobserve(e)}})},{rootMargin:"50px 0px",threshold:.1}))}createOptimizedImage(t){const n=document.createElement("img");return n.alt=t.alt||"",n.loading=t.loading||"lazy",t.width&&(n.width=t.width),t.height&&(n.height=t.height),n.style.cssText="\n      display: block;\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: opacity 0.3s ease;\n      background-color: #f0f0f0;\n    ",t.placeholder?(n.src=t.placeholder,n.dataset.src=this.optimizeImageUrl(t.src,t)):(n.src=this.createPlaceholderDataUrl(t.width||40,t.height||40),n.dataset.src=this.optimizeImageUrl(t.src,t)),t.onLoad&&n.addEventListener("load",t.onLoad),t.onError?n.addEventListener("error",t.onError):n.addEventListener("error",()=>{n.src=this.createPlaceholderDataUrl(t.width||40,t.height||40),n.style.backgroundColor="#e0e0e0"}),this.observer&&"lazy"===t.loading?this.observer.observe(n):this.loadImage(n),n}loadImage(t){const n=t.dataset.src;if(!n||this.loadedImages.has(n))return;this.loadedImages.add(n);const e=new Image;e.onload=()=>{t.src=n,t.style.opacity="1"},e.onerror=()=>{t.style.opacity="0.7"},e.src=n}optimizeImageUrl(t,n){if(t.startsWith("data:")||t.startsWith("blob:"))return t;if(t.includes("googleusercontent.com")){const e=new URL(t),i=Math.max(n.width||40,n.height||40);return e.searchParams.set("s",Math.min(2*i,200).toString()),e.toString()}return t}createPlaceholderDataUrl(t,n){return`data:image/svg+xml;base64,${btoa(`\n      <svg width="${t}" height="${n}" xmlns="http://www.w3.org/2000/svg">\n        <rect width="100%" height="100%" fill="#f0f0f0"/>\n        <circle cx="50%" cy="40%" r="25%" fill="#d0d0d0"/>\n        <path d="M25% 70% Q50% 60% 75% 70% L75% 85% L25% 85% Z" fill="#d0d0d0"/>\n      </svg>\n    `)}`}destroy(){this.observer&&this.observer.disconnect(),this.loadedImages.clear()}};class i{constructor(t,e){n(this,"review"),n(this,"config"),n(this,"element"),n(this,"isExpanded",!1),this.review=t,this.config=e,this.element=this.createElement()}createElement(){const t=document.createElement("div");return t.className="grw-review-card",t.setAttribute("data-review-id",this.review.id),t.innerHTML=this.getCardHTML(),this.attachEventListeners(t),this.loadOptimizedImages(t),t}getCardHTML(){const t=this.getTruncatedText(),n=this.review.text.length>this.config.maxTextLength,e=this.formatDate(this.review.publishedDate);return`\n      <div class="grw-review-header">\n        ${this.config.showPhotos&&this.review.authorPhotoUrl?'\n          <div class="grw-author-photo" data-photo-container="true"></div>\n        ':""}\n        <div class="grw-author-info">\n          <div class="grw-author-name">\n            ${this.escapeHtml(this.review.authorName)}\n            ${this.review.isVerified?'<span class="grw-verified-badge">✓</span>':""}\n          </div>\n          ${this.config.showDates?`\n            <div class="grw-review-date">${e}</div>\n          `:""}\n        </div>\n        <div class="grw-rating">\n          ${this.generateStarRating(this.review.rating)}\n        </div>\n      </div>\n      <div class="grw-review-content">\n        <div class="grw-review-text ${n?"grw-expandable":""}">\n          <span class="grw-text-content">${this.escapeHtml(t)}</span>\n          ${n?'\n            <button class="grw-expand-btn" type="button" aria-label="Expand review">\n              <span class="grw-expand-text">Show more</span>\n              <span class="grw-collapse-text" style="display: none;">Show less</span>\n            </button>\n          ':""}\n        </div>\n      </div>\n    `}getTruncatedText(){if(this.review.text.length<=this.config.maxTextLength)return this.review.text;const t=this.review.text.substring(0,this.config.maxTextLength),n=t.lastIndexOf(" ");return n>0?t.substring(0,n)+"...":t+"..."}generateStarRating(t){const n=Math.floor(t),e=t%1>=.5,i=5-n-(e?1:0);let r="";for(let s=0;s<n;s++)r+='<span class="grw-star grw-star-full">★</span>';e&&(r+='<span class="grw-star grw-star-half">★</span>');for(let s=0;s<i;s++)r+='<span class="grw-star grw-star-empty">☆</span>';return`<div class="grw-stars" aria-label="${t} out of 5 stars">${r}</div>`}formatDate(t){const n=new Date,e=Math.abs(n.getTime()-t.getTime()),i=Math.ceil(e/864e5);if(1===i)return"Yesterday";if(i<7)return`${i} days ago`;if(i<30){const t=Math.floor(i/7);return`${t} week${t>1?"s":""} ago`}if(i<365){const t=Math.floor(i/30);return`${t} month${t>1?"s":""} ago`}return t.toLocaleDateString()}escapeHtml(t){const n=document.createElement("div");return n.textContent=t,n.innerHTML}loadOptimizedImages(t){if(!this.config.showPhotos||!this.review.authorPhotoUrl)return;const n=t.querySelector('[data-photo-container="true"]');if(!n)return;const i=e.createOptimizedImage({src:this.review.authorPhotoUrl,alt:this.review.authorName,width:40,height:40,loading:"lazy",onLoad:()=>{i.style.opacity="0",i.style.transition="opacity 0.3s ease",setTimeout(()=>{i.style.opacity="1"},10)},onError:()=>{n.innerHTML=`\n          <div style="\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            background: #e0e0e0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-weight: 600;\n            font-size: 14px;\n            color: #666;\n          ">\n            ${this.getInitials(this.review.authorName)}\n          </div>\n        `}});n.appendChild(i)}getInitials(t){return t.split(" ").map(t=>t.charAt(0)).join("").substring(0,2).toUpperCase()}attachEventListeners(t){const n=t.querySelector(".grw-expand-btn");n&&n.addEventListener("click",t=>{t.preventDefault(),this.toggleExpansion()})}toggleExpansion(){const t=this.element.querySelector(".grw-text-content"),n=this.element.querySelector(".grw-expand-text"),e=this.element.querySelector(".grw-collapse-text"),i=this.element.querySelector(".grw-expand-btn");t&&n&&e&&i&&(this.isExpanded=!this.isExpanded,t.classList.add("grw-transitioning"),this.isExpanded?(t.textContent=this.review.text,n.style.display="none",e.style.display="inline",i.setAttribute("aria-label","Collapse review"),this.element.classList.add("grw-expanded")):(t.textContent=this.getTruncatedText(),n.style.display="inline",e.style.display="none",i.setAttribute("aria-label","Expand review"),this.element.classList.remove("grw-expanded")),setTimeout(()=>{t.classList.remove("grw-transitioning")},300))}getElement(){return this.element}getReview(){return this.review}updateConfig(t){this.config={...this.config,...t},this.element.innerHTML=this.getCardHTML(),this.attachEventListeners(this.element)}destroy(){this.element.parentNode&&this.element.parentNode.removeChild(this.element)}}class r{constructor(t,e){n(this,"reviews"),n(this,"config"),n(this,"element"),n(this,"reviewCards",[]),n(this,"currentPage",0),n(this,"autoPlayTimer"),n(this,"isTransitioning",!1),this.reviews=t.slice(0,e.maxReviews),this.config=e,this.element=this.createElement(),this.init()}createElement(){const t=document.createElement("div");return t.className=`grw-reviews-display grw-layout-${this.config.layout}`,t.innerHTML=this.getContainerHTML(),this.attachEventListeners(t),t}getContainerHTML(){const t=this.config.showNavigation&&("carousel"===this.config.layout||this.config.showPagination&&this.hasMultiplePages());return`\n      <div class="grw-reviews-container">\n        ${t?'\n          <button class="grw-nav-btn grw-nav-prev" type="button" aria-label="Previous reviews">\n            <span class="grw-nav-icon">‹</span>\n          </button>\n        ':""}\n        \n        <div class="grw-reviews-viewport">\n          <div class="grw-reviews-track"></div>\n        </div>\n        \n        ${t?'\n          <button class="grw-nav-btn grw-nav-next" type="button" aria-label="Next reviews">\n            <span class="grw-nav-icon">›</span>\n          </button>\n        ':""}\n      </div>\n      \n      ${this.config.showPagination?'\n        <div class="grw-pagination">\n          <div class="grw-pagination-dots"></div>\n        </div>\n      ':""}\n    `}hasMultiplePages(){let t;switch(this.config.layout){case"carousel":t=this.config.itemsPerPage||1;break;case"grid":const n=this.config.gridColumns||2;t=this.config.itemsPerPage||2*n;break;case"list":t=this.config.itemsPerPage||5}return this.reviews.length>t}init(){this.createReviewCards(),this.renderReviews(),this.setupPagination(),this.setupAutoPlay(),this.setupTouchGestures()}createReviewCards(){this.reviewCards=this.reviews.map(t=>new i(t,this.config.reviewCardConfig))}renderReviews(){const t=this.element.querySelector(".grw-reviews-track");if(t)switch(t.innerHTML="",this.config.layout){case"carousel":this.renderCarousel(t);break;case"grid":this.renderGrid(t);break;case"list":this.renderList(t)}}renderCarousel(t){const n=this.config.itemsPerPage||1,e=Math.ceil(this.reviewCards.length/n);for(let i=0;i<e;i++){const e=document.createElement("div");e.className="grw-carousel-page";const r=i*n,s=Math.min(r+n,this.reviewCards.length);for(let t=r;t<s;t++){const n=document.createElement("div");n.className="grw-carousel-item",n.appendChild(this.reviewCards[t].getElement()),e.appendChild(n)}t.appendChild(e)}t.style.transform="translateX(0%)"}renderGrid(t){const n=this.config.gridColumns||2,e=this.config.itemsPerPage||2*n;this.config.showPagination&&this.reviewCards.length>e?this.renderPaginatedGrid(t,n,e):(t.style.gridTemplateColumns=`repeat(${n}, 1fr)`,this.reviewCards.forEach(n=>{const e=document.createElement("div");e.className="grw-grid-item",e.appendChild(n.getElement()),t.appendChild(e)}))}renderList(t){const n=this.config.itemsPerPage||5;this.config.showPagination&&this.reviewCards.length>n?this.renderPaginatedList(t,n):this.reviewCards.forEach(n=>{const e=document.createElement("div");e.className="grw-list-item",e.appendChild(n.getElement()),t.appendChild(e)})}renderPaginatedGrid(t,n,e){const i=Math.ceil(this.reviewCards.length/e);t.style.gridTemplateColumns=`repeat(${n}, 1fr)`;for(let r=0;r<i;r++){const n=document.createElement("div");n.className="grw-grid-page",n.style.display=0===r?"contents":"none";const i=r*e,s=Math.min(i+e,this.reviewCards.length);for(let t=i;t<s;t++){const e=document.createElement("div");e.className="grw-grid-item",e.appendChild(this.reviewCards[t].getElement()),n.appendChild(e)}t.appendChild(n)}}renderPaginatedList(t,n){const e=Math.ceil(this.reviewCards.length/n);for(let i=0;i<e;i++){const e=document.createElement("div");e.className="grw-list-page",e.style.display=0===i?"block":"none";const r=i*n,s=Math.min(r+n,this.reviewCards.length);for(let t=r;t<s;t++){const n=document.createElement("div");n.className="grw-list-item",n.appendChild(this.reviewCards[t].getElement()),e.appendChild(n)}t.appendChild(e)}}setupPagination(){if(!this.config.showPagination)return;const t=this.element.querySelector(".grw-pagination-dots");if(!t)return;let n,e;switch(this.config.layout){case"carousel":n=this.config.itemsPerPage||1;break;case"grid":const t=this.config.gridColumns||2;n=this.config.itemsPerPage||2*t;break;case"list":n=this.config.itemsPerPage||5}if(e=Math.ceil(this.reviewCards.length/n),e<=1)t.style.display="none";else{t.style.display="flex",t.innerHTML="";for(let n=0;n<e;n++){const e=document.createElement("button");e.className="grw-pagination-dot "+(0===n?"grw-active":""),e.setAttribute("type","button"),e.setAttribute("aria-label",`Go to page ${n+1}`),e.addEventListener("click",()=>this.goToPage(n)),t.appendChild(e)}}}setupAutoPlay(){if(!this.config.autoPlay||"carousel"!==this.config.layout)return;const t=this.config.autoPlayInterval||5e3;setTimeout(()=>{this.config.autoPlay&&"carousel"===this.config.layout&&!this.autoPlayTimer&&(this.autoPlayTimer=window.setInterval(()=>{this.isTransitioning||this.nextPage()},t))},100),this.element.addEventListener("mouseenter",()=>this.pauseAutoPlay()),this.element.addEventListener("mouseleave",()=>this.resumeAutoPlay())}setupTouchGestures(){if(!this.hasMultiplePages())return;let t=0,n=0,e=!1;const i=this.element.querySelector(".grw-reviews-track");i&&(i.addEventListener("touchstart",i=>{t=i.touches[0].clientX,n=i.touches[0].clientY,e=!0,"carousel"===this.config.layout&&this.pauseAutoPlay()},{passive:!0}),i.addEventListener("touchmove",i=>{if(!e)return;const r=i.touches[0].clientX,s=i.touches[0].clientY,a=Math.abs(r-t),o=Math.abs(s-n),h="carousel"===this.config.layout?10:20;a>o&&a>h&&i.preventDefault()},{passive:!1}),i.addEventListener("touchend",i=>{if(!e)return;const r=i.changedTouches[0].clientX,s=i.changedTouches[0].clientY,a=t-r,o=n-s,h="carousel"===this.config.layout?50:80;Math.abs(a)>h&&Math.abs(a)>Math.abs(o)?a>0?this.nextPage():this.previousPage():"carousel"!==this.config.layout&&Math.abs(o)>60&&(o>0?this.nextPage():this.previousPage()),e=!1,"carousel"===this.config.layout&&this.resumeAutoPlay()},{passive:!0}))}attachEventListeners(t){const n=t.querySelector(".grw-nav-prev"),e=t.querySelector(".grw-nav-next");if(n&&n.addEventListener("click",()=>this.previousPage()),e&&e.addEventListener("click",()=>this.nextPage()),t.addEventListener("keydown",t=>{if(this.hasMultiplePages())switch(t.key){case"ArrowLeft":case"PageUp":t.preventDefault(),this.previousPage(),this.announcePageChange();break;case"ArrowRight":case"PageDown":t.preventDefault(),this.nextPage(),this.announcePageChange();break;case"Home":t.preventDefault(),this.goToPage(0),this.announcePageChange();break;case"End":let n;switch(t.preventDefault(),this.config.layout){case"carousel":n=this.config.itemsPerPage||1;break;case"grid":const t=this.config.gridColumns||2;n=this.config.itemsPerPage||2*t;break;case"list":n=this.config.itemsPerPage||5}const e=Math.ceil(this.reviewCards.length/n);this.goToPage(e-1),this.announcePageChange()}}),this.hasMultiplePages()){t.setAttribute("tabindex","0"),t.setAttribute("role","region");const n="carousel"===this.config.layout?"carousel":"grid"===this.config.layout?"grid":"list";t.setAttribute("aria-label",`Reviews ${n}`),t.setAttribute("aria-live","polite");const e="carousel"===this.config.layout?"Use arrow keys to navigate between pages, Home/End to go to first/last page":"Use arrow keys or Page Up/Down to navigate between pages, Home/End to go to first/last page";t.setAttribute("aria-describedby","grw-nav-instructions");const i=document.createElement("div");i.id="grw-nav-instructions",i.className="grw-sr-only",i.textContent=e,i.style.cssText="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;",t.appendChild(i)}}goToPage(t){if(this.isTransitioning)return;let n,e;switch(this.config.layout){case"carousel":n=this.config.itemsPerPage||1;break;case"grid":const t=this.config.gridColumns||2;n=this.config.itemsPerPage||2*t;break;case"list":n=this.config.itemsPerPage||5}switch(e=Math.ceil(this.reviewCards.length/n),this.currentPage=Math.max(0,Math.min(t,e-1)),this.config.layout){case"carousel":this.updateCarouselPosition();break;case"grid":this.updateGridPosition();break;case"list":this.updateListPosition()}this.updatePaginationDots()}nextPage(){let t,n;switch(this.config.layout){case"carousel":t=this.config.itemsPerPage||1;break;case"grid":const n=this.config.gridColumns||2;t=this.config.itemsPerPage||2*n;break;case"list":t=this.config.itemsPerPage||5}if(n=Math.ceil(this.reviewCards.length/t),n<=1)return;const e="carousel"===this.config.layout?(this.currentPage+1)%n:Math.min(this.currentPage+1,n-1);this.goToPage(e)}previousPage(){let t,n;switch(this.config.layout){case"carousel":t=this.config.itemsPerPage||1;break;case"grid":const n=this.config.gridColumns||2;t=this.config.itemsPerPage||2*n;break;case"list":t=this.config.itemsPerPage||5}if(n=Math.ceil(this.reviewCards.length/t),n<=1)return;const e="carousel"===this.config.layout?0===this.currentPage?n-1:this.currentPage-1:Math.max(this.currentPage-1,0);this.goToPage(e)}updateCarouselPosition(){const t=this.element.querySelector(".grw-reviews-track");if(!t)return;this.isTransitioning=!0;const n=100*-this.currentPage;t.style.transform=`translateX(${n}%)`,setTimeout(()=>{this.isTransitioning=!1},300)}updateGridPosition(){const t=this.element.querySelector(".grw-reviews-track");t&&(this.isTransitioning=!0,t.querySelectorAll(".grw-grid-page").forEach((t,n)=>{n===this.currentPage?(t.style.display="contents",t.style.opacity="1"):(t.style.display="none",t.style.opacity="0")}),this.isTransitioning=!1)}updateListPosition(){const t=this.element.querySelector(".grw-reviews-track");t&&(this.isTransitioning=!0,t.querySelectorAll(".grw-list-page").forEach((t,n)=>{n===this.currentPage?(t.style.display="block",t.style.transition="opacity 0.3s ease, transform 0.3s ease",t.style.opacity="1",t.style.transform="translateY(0)"):(t.style.display="none",t.style.opacity="0",t.style.transform="translateY(-20px)")}),this.isTransitioning=!1)}updatePaginationDots(){this.element.querySelectorAll(".grw-pagination-dot").forEach((t,n)=>{t.classList.toggle("grw-active",n===this.currentPage)})}pauseAutoPlay(){this.autoPlayTimer&&(clearInterval(this.autoPlayTimer),this.autoPlayTimer=void 0)}resumeAutoPlay(){if(this.config.autoPlay&&"carousel"===this.config.layout&&!this.autoPlayTimer){const t=this.config.autoPlayInterval||5e3;this.autoPlayTimer=window.setInterval(()=>{this.isTransitioning||this.nextPage()},t)}}announcePageChange(){const t=this.config.itemsPerPage||1,n=Math.ceil(this.reviewCards.length/t),e=this.currentPage+1;let i=this.element.querySelector(".grw-sr-announcement");i||(i=document.createElement("div"),i.className="grw-sr-announcement",i.setAttribute("aria-live","polite"),i.setAttribute("aria-atomic","true"),i.style.cssText="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;",this.element.appendChild(i)),i.textContent=`Page ${e} of ${n}`}getElement(){return this.element}updateReviews(t){this.reviews=t.slice(0,this.config.maxReviews),this.reviewCards.forEach(t=>t.destroy()),this.createReviewCards(),this.renderReviews(),this.setupPagination(),this.currentPage=0,"carousel"===this.config.layout&&(this.updateCarouselPosition(),this.updatePaginationDots())}updateConfig(t){const n=this.config.layout;this.config={...this.config,...t},t.reviewCardConfig&&this.reviewCards.forEach(n=>n.updateConfig(t.reviewCardConfig)),t.layout&&t.layout!==n&&(this.element.className=`grw-reviews-display grw-layout-${this.config.layout}`,this.element.innerHTML=this.getContainerHTML(),this.attachEventListeners(this.element),this.renderReviews(),this.setupPagination(),this.setupAutoPlay(),this.setupTouchGestures())}destroy(){this.pauseAutoPlay(),this.reviewCards.forEach(t=>t.destroy()),this.element.parentNode&&this.element.parentNode.removeChild(this.element)}}class s{constructor(){n(this,"cache",new Map),n(this,"CACHE_TTL",3e5),n(this,"STALE_TTL",6e5)}set(t,n,e){this.cache.set(t,{data:n,timestamp:Date.now(),etag:e})}get(t){const n=this.cache.get(t);if(!n)return null;const e=Date.now()-n.timestamp;return e<this.CACHE_TTL?n.data:(e>this.STALE_TTL&&this.cache.delete(t),null)}getStale(t){const n=this.cache.get(t);return n?Date.now()-n.timestamp<this.STALE_TTL?n.data:(this.cache.delete(t),null):null}getETag(t){const n=this.cache.get(t);return(null==n?void 0:n.etag)||null}clear(t){t?this.cache.delete(t):this.cache.clear()}size(){return this.cache.size}}class a{constructor(t){n(this,"config"),n(this,"container"),n(this,"shadowRoot"),n(this,"isInitialized",!1),n(this,"resizeObserver"),n(this,"reviewsDisplay"),n(this,"reviews",[]),n(this,"isLoading",!1),n(this,"loadError",null),n(this,"retryCount",0),n(this,"maxRetries",3),n(this,"retryDelay",1e3),n(this,"cache"),n(this,"serviceWorkerRegistration"),this.config=this.mergeDefaultConfig(t),this.container=this.createContainer(),this.shadowRoot=this.createShadowDOM(),this.cache=new s,this.registerServiceWorker(),this.init()}mergeDefaultConfig(t){return{apiUrl:"http://localhost:3001",width:"100%",height:"auto",theme:"light",responsive:!0,layout:"carousel",maxReviews:10,showPhotos:!0,showDates:!0,maxTextLength:150,autoPlay:!1,autoPlayInterval:5e3,showNavigation:!0,showPagination:!0,itemsPerPage:1,gridColumns:2,...t}}createContainer(){const t=document.createElement("div");return t.className="grw-widget-root",t.setAttribute("data-widget-id",this.config.widgetId),t.style.cssText=`\n      display: block;\n      width: ${this.config.width};\n      height: ${this.config.height};\n      max-width: 100%;\n      box-sizing: border-box;\n      overflow: hidden;\n    `,t}createShadowDOM(){const t=this.container.attachShadow({mode:"closed"}),n=document.createElement("style");return n.textContent=this.getIsolatedCSS(),t.appendChild(n),t}getIsolatedCSS(){return`\n      :host {\n        all: initial;\n        display: block;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        line-height: 1.4;\n        color: #333;\n        box-sizing: border-box;\n        ${"dark"===this.config.theme?"--grw-card-bg: #2d2d2d; --grw-border-color: #404040; --grw-text-primary: #ffffff; --grw-text-secondary: #b0b0b0; --grw-placeholder-bg: #404040; --grw-nav-bg: #2d2d2d; --grw-nav-hover-bg: #404040; --grw-nav-hover-border: #555555; --grw-dot-inactive: #555555; --grw-dot-hover: #777777;":"--grw-card-bg: #ffffff; --grw-border-color: #e1e5e9; --grw-text-primary: #1a1a1a; --grw-text-secondary: #666666; --grw-placeholder-bg: #f0f0f0; --grw-nav-bg: #ffffff; --grw-nav-hover-bg: #f5f5f5; --grw-nav-hover-border: #d0d0d0; --grw-dot-inactive: #d0d0d0; --grw-dot-hover: #999999;"}\n        --grw-verified-bg: #4285f4;\n        --grw-star-filled: #ffc107;\n        --grw-star-empty: #e0e0e0;\n        --grw-link-color: #1a73e8;\n        --grw-link-hover: #1557b0;\n        --grw-focus-color: #1a73e8;\n        --grw-dot-active: #1a73e8;\n      }\n\n      *, *::before, *::after {\n        box-sizing: border-box;\n      }\n\n      .grw-widget {\n        width: 100%;\n        height: 100%;\n        background: ${"dark"===this.config.theme?"#1a1a1a":"#ffffff"};\n        border-radius: 8px;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        overflow: hidden;\n        position: relative;\n        padding: 16px;\n      }\n\n      .grw-loading {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        height: 200px;\n        color: ${"dark"===this.config.theme?"#ffffff":"#666666"};\n      }\n\n      .grw-error {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        height: 200px;\n        color: #e74c3c;\n        text-align: center;\n        padding: 20px;\n      }\n\n      .grw-spinner {\n        width: 24px;\n        height: 24px;\n        border: 2px solid #f3f3f3;\n        border-top: 2px solid #3498db;\n        border-radius: 50%;\n        animation: grw-spin 1s linear infinite;\n        margin-right: 10px;\n      }\n\n      @keyframes grw-spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n\n      /* Review Card Styles */\n      .grw-review-card {\n        background: var(--grw-card-bg);\n        border: 1px solid var(--grw-border-color);\n        border-radius: 8px;\n        padding: 16px;\n        margin-bottom: 12px;\n        transition: all 0.2s ease;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      }\n\n      .grw-review-card:hover {\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        transform: translateY(-1px);\n      }\n\n      .grw-review-header {\n        display: flex;\n        align-items: flex-start;\n        gap: 12px;\n        margin-bottom: 12px;\n      }\n\n      .grw-author-photo {\n        flex-shrink: 0;\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        overflow: hidden;\n        background: var(--grw-placeholder-bg);\n      }\n\n      .grw-author-photo img {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n      }\n\n      .grw-author-info {\n        flex: 1;\n        min-width: 0;\n      }\n\n      .grw-author-name {\n        font-weight: 600;\n        font-size: 14px;\n        color: var(--grw-text-primary);\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        margin-bottom: 2px;\n      }\n\n      .grw-verified-badge {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        width: 16px;\n        height: 16px;\n        background: var(--grw-verified-bg);\n        color: white;\n        border-radius: 50%;\n        font-size: 10px;\n        font-weight: bold;\n      }\n\n      .grw-review-date {\n        font-size: 12px;\n        color: var(--grw-text-secondary);\n      }\n\n      .grw-rating {\n        flex-shrink: 0;\n      }\n\n      .grw-stars {\n        display: flex;\n        gap: 1px;\n      }\n\n      .grw-star {\n        font-size: 16px;\n        line-height: 1;\n      }\n\n      .grw-star-full {\n        color: var(--grw-star-filled);\n      }\n\n      .grw-star-half {\n        color: var(--grw-star-filled);\n        position: relative;\n      }\n\n      .grw-star-half::after {\n        content: '☆';\n        position: absolute;\n        left: 50%;\n        top: 0;\n        color: var(--grw-star-empty);\n        overflow: hidden;\n      }\n\n      .grw-star-empty {\n        color: var(--grw-star-empty);\n      }\n\n      .grw-review-content {\n        margin-top: 12px;\n      }\n\n      .grw-review-text {\n        font-size: 14px;\n        line-height: 1.5;\n        color: var(--grw-text-primary);\n      }\n\n      .grw-text-content {\n        display: block;\n        transition: all 0.3s ease;\n      }\n\n      .grw-text-content.grw-transitioning {\n        opacity: 0.7;\n      }\n\n      .grw-expand-btn {\n        background: none;\n        border: none;\n        color: var(--grw-link-color);\n        cursor: pointer;\n        font-size: 14px;\n        padding: 4px 0;\n        margin-top: 8px;\n        text-decoration: none;\n        transition: color 0.2s ease;\n      }\n\n      .grw-expand-btn:hover {\n        color: var(--grw-link-hover);\n        text-decoration: underline;\n      }\n\n      .grw-expand-btn:focus {\n        outline: 2px solid var(--grw-focus-color);\n        outline-offset: 2px;\n        border-radius: 2px;\n      }\n\n      /* Reviews Display Container */\n      .grw-reviews-display {\n        width: 100%;\n        position: relative;\n      }\n\n      .grw-reviews-container {\n        position: relative;\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .grw-reviews-viewport {\n        flex: 1;\n        overflow: hidden;\n        position: relative;\n      }\n\n      .grw-reviews-track {\n        display: flex;\n        transition: transform 0.3s ease;\n      }\n\n      /* Carousel Layout */\n      .grw-layout-carousel .grw-reviews-track {\n        width: 100%;\n      }\n\n      .grw-carousel-page {\n        flex: 0 0 100%;\n        display: flex;\n        gap: 16px;\n      }\n\n      .grw-carousel-item {\n        flex: 1;\n        min-width: 0;\n      }\n\n      /* Grid Layout */\n      .grw-layout-grid .grw-reviews-track {\n        display: grid;\n        gap: 16px;\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .grw-grid-item {\n        display: flex;\n        flex-direction: column;\n      }\n\n      /* List Layout */\n      .grw-layout-list .grw-reviews-track {\n        flex-direction: column;\n        gap: 12px;\n      }\n\n      .grw-list-item {\n        width: 100%;\n      }\n\n      /* Navigation Buttons */\n      .grw-nav-btn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 40px;\n        height: 40px;\n        border: 1px solid var(--grw-border-color);\n        border-radius: 50%;\n        background: var(--grw-nav-bg);\n        color: var(--grw-text-primary);\n        cursor: pointer;\n        transition: all 0.2s ease;\n        font-size: 18px;\n        line-height: 1;\n      }\n\n      .grw-nav-btn:hover {\n        background: var(--grw-nav-hover-bg);\n        border-color: var(--grw-nav-hover-border);\n        transform: scale(1.05);\n      }\n\n      .grw-nav-btn:focus {\n        outline: 2px solid var(--grw-focus-color);\n        outline-offset: 2px;\n      }\n\n      .grw-nav-btn:disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n        transform: none;\n      }\n\n      .grw-nav-icon {\n        display: block;\n        font-weight: bold;\n      }\n\n      /* Pagination */\n      .grw-pagination {\n        display: flex;\n        justify-content: center;\n        margin-top: 16px;\n      }\n\n      .grw-pagination-dots {\n        display: flex;\n        gap: 8px;\n      }\n\n      .grw-pagination-dot {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        border: none;\n        background: var(--grw-dot-inactive);\n        cursor: pointer;\n        transition: all 0.2s ease;\n      }\n\n      .grw-pagination-dot:hover {\n        background: var(--grw-dot-hover);\n        transform: scale(1.2);\n      }\n\n      .grw-pagination-dot.grw-active {\n        background: var(--grw-dot-active);\n      }\n\n      .grw-pagination-dot:focus {\n        outline: 2px solid var(--grw-focus-color);\n        outline-offset: 2px;\n      }\n\n      /* Responsive styles */\n      @media (max-width: 768px) {\n        .grw-widget {\n          border-radius: 4px;\n          padding: 12px;\n        }\n        \n        .grw-review-card {\n          padding: 12px;\n        }\n        \n        .grw-author-photo {\n          width: 32px;\n          height: 32px;\n        }\n        \n        .grw-author-name {\n          font-size: 13px;\n        }\n        \n        .grw-review-text {\n          font-size: 13px;\n        }\n        \n        .grw-layout-grid .grw-reviews-track {\n          grid-template-columns: 1fr;\n        }\n        \n        .grw-nav-btn {\n          width: 36px;\n          height: 36px;\n          font-size: 16px;\n        }\n        \n        .grw-carousel-page {\n          gap: 12px;\n        }\n      }\n\n      @media (max-width: 480px) {\n        :host {\n          font-size: 12px;\n        }\n        \n        .grw-widget {\n          padding: 10px;\n        }\n        \n        .grw-review-card {\n          padding: 10px;\n          margin-bottom: 10px;\n        }\n        \n        .grw-review-header {\n          gap: 8px;\n          margin-bottom: 10px;\n        }\n        \n        .grw-author-photo {\n          width: 28px;\n          height: 28px;\n        }\n        \n        .grw-author-name {\n          font-size: 12px;\n        }\n        \n        .grw-review-date {\n          font-size: 11px;\n        }\n        \n        .grw-review-text {\n          font-size: 12px;\n        }\n        \n        .grw-stars {\n          gap: 0;\n        }\n        \n        .grw-star {\n          font-size: 14px;\n        }\n        \n        .grw-nav-btn {\n          width: 32px;\n          height: 32px;\n          font-size: 14px;\n        }\n        \n        .grw-carousel-page {\n          gap: 8px;\n        }\n        \n        .grw-pagination {\n          margin-top: 12px;\n        }\n        \n        .grw-pagination-dots {\n          gap: 6px;\n        }\n      }\n\n      /* Animation for smooth transitions */\n      @keyframes grw-fade-in {\n        from {\n          opacity: 0;\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0);\n        }\n      }\n\n      .grw-review-card {\n        animation: grw-fade-in 0.3s ease forwards;\n      }\n    `}async registerServiceWorker(){var t,n;if("undefined"!=typeof navigator&&"serviceWorker"in navigator&&!(null==(t=this.config.apiUrl)?void 0:t.includes("localhost"))&&!(null==(n=this.config.apiUrl)?void 0:n.includes("127.0.0.1")))try{const t=await navigator.serviceWorker.register("/grw-service-worker.js",{scope:"/"});this.serviceWorkerRegistration=t,t.addEventListener("updatefound",()=>{const n=t.installing;n&&n.addEventListener("statechange",()=>{"installed"===n.state&&navigator.serviceWorker.controller})})}catch(e){}}async init(){if(!this.isInitialized)try{this.render(),this.setupResponsiveContainer(),await this.loadReviews(),this.isInitialized=!0}catch(t){this.renderError("Failed to initialize widget")}}render(){const t=document.createElement("div");t.className="grw-widget",t.innerHTML='\n      <div class="grw-loading">\n        <div class="grw-spinner"></div>\n        <span>Loading reviews...</span>\n      </div>\n    ',this.shadowRoot.appendChild(t)}renderError(t){const n=this.shadowRoot.querySelector(".grw-widget");n&&(n.innerHTML=`\n        <div class="grw-error">\n          <span>${t}</span>\n        </div>\n      `)}setupResponsiveContainer(){this.config.responsive&&("undefined"!=typeof ResizeObserver&&(this.resizeObserver=new ResizeObserver(t=>{for(const n of t)this.handleResize(n.contentRect)}),this.resizeObserver.observe(this.container)),window.addEventListener("resize",()=>{this.handleResize(this.container.getBoundingClientRect())}))}handleResize(t){const n=this.shadowRoot.querySelector(".grw-widget");n&&(t.width<480?n.classList.add("grw-mobile"):n.classList.remove("grw-mobile"),t.width<768?n.classList.add("grw-tablet"):n.classList.remove("grw-tablet"))}async loadReviews(){if(!this.isLoading){this.isLoading=!0,this.loadError=null;try{const t=this.cache.get(this.config.widgetId);if(t)return this.processWidgetData(t),void(this.isLoading=!1);this.renderSkeletonLoader();const n={Accept:"application/json","Content-Type":"application/json"},e=this.cache.getETag(this.config.widgetId);e&&(n["If-None-Match"]=e);const i=await fetch(`${this.config.apiUrl}/api/widget/${this.config.widgetId}/data`,{method:"GET",headers:n,signal:AbortSignal.timeout?AbortSignal.timeout(1e4):void 0});if(304===i.status){const t=this.cache.getStale(this.config.widgetId);if(t)return this.processWidgetData(t),void(this.isLoading=!1)}if(!i.ok)throw new Error(`HTTP ${i.status}: ${i.statusText}`);const r=await i.json();r.lastUpdated=new Date(r.lastUpdated),r.reviews=r.reviews.map(t=>({...t,publishedDate:new Date(t.publishedDate)}));const s=i.headers.get("ETag");this.cache.set(this.config.widgetId,r,s||void 0),this.processWidgetData(r),this.retryCount=0}catch(t){this.handleLoadError(t)}finally{this.isLoading=!1}}}processWidgetData(t){this.reviews=t.reviews||[],this.renderReviews()}handleLoadError(t){this.loadError=t instanceof Error?t.message:"Unknown error";const n=this.categorizeError(t);this.logError(t,n,{widgetId:this.config.widgetId,apiUrl:this.config.apiUrl,retryCount:this.retryCount,hasStaleData:!!this.cache.getStale(this.config.widgetId)});const e=this.cache.getStale(this.config.widgetId);if(e)return this.processWidgetData(e),void this.showStaleDataNotice();if(this.retryCount<this.maxRetries&&this.shouldRetry(n)){this.retryCount++;const t=this.retryDelay*Math.pow(2,this.retryCount-1);return void setTimeout(()=>{this.loadReviews()},t)}this.shouldUseMockData()?this.loadMockReviews():this.renderError(this.getErrorMessage(n))}shouldUseMockData(){var t,n;return(null==(t=this.config.apiUrl)?void 0:t.includes("localhost"))||(null==(n=this.config.apiUrl)?void 0:n.includes("127.0.0.1"))||!0===window.GRW_USE_MOCK_DATA}categorizeError(t){if(!t)return"unknown";const n=t.message||t.toString();return"AbortError"===t.name||n.includes("timeout")?"timeout":"TypeError"===t.name&&n.includes("fetch")?"network":n.includes("HTTP 4")?"client-error":n.includes("HTTP 5")?"server-error":n.includes("JSON")||n.includes("parse")?"parse-error":n.includes("CORS")?"cors-error":"unknown"}shouldRetry(t){return!["client-error","parse-error","cors-error"].includes(t)}getErrorMessage(t){switch(t){case"network":return"Unable to connect to the server. Please check your internet connection.";case"timeout":return"Request timed out. Please try again later.";case"server-error":return"Server error occurred. Please try again later.";case"client-error":return"Widget configuration error. Please check your setup.";case"parse-error":return"Invalid response from server. Please try again later.";case"cors-error":return"Cross-origin request blocked. Please check widget configuration.";default:return`Failed to load reviews: ${this.loadError}`}}logError(t,n,e){var i,r;const s={message:(null==t?void 0:t.message)||"Unknown error",type:n,stack:null==t?void 0:t.stack,context:e,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,url:window.location.href};(null==(i=this.config.apiUrl)?void 0:i.includes("localhost"))||null==(r=this.config.apiUrl)||r.includes("127.0.0.1");try{"undefined"!=typeof fetch&&this.config.apiUrl&&!this.config.apiUrl.includes("localhost")&&fetch(`${this.config.apiUrl}/api/widget/error-log`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}).catch(()=>{})}catch{}}showStaleDataNotice(){const t=this.shadowRoot.querySelector(".grw-widget");if(!t)return;const n=document.createElement("div");n.className="grw-stale-notice",n.innerHTML='\n      <style>\n        .grw-stale-notice {\n          position: absolute;\n          top: 8px;\n          right: 8px;\n          background: rgba(255, 193, 7, 0.9);\n          color: #856404;\n          padding: 4px 8px;\n          border-radius: 4px;\n          font-size: 11px;\n          font-weight: 500;\n          z-index: 10;\n          opacity: 0;\n          animation: grw-fade-in-notice 0.3s ease forwards;\n        }\n        \n        @keyframes grw-fade-in-notice {\n          to { opacity: 1; }\n        }\n      </style>\n      <span title="Reviews data may be outdated">⚠️ Offline</span>\n    ',t.appendChild(n),setTimeout(()=>{n.parentNode&&n.remove()},5e3)}renderSkeletonLoader(){const t=this.shadowRoot.querySelector(".grw-widget");if(!t)return;const n=this.generateSkeletonHTML();t.innerHTML=n}generateSkeletonHTML(){const t="carousel"===this.config.layout?this.config.itemsPerPage||1:3;return`\n      <style>\n        .grw-skeleton-card {\n          background: var(--grw-card-bg);\n          border: 1px solid var(--grw-border-color);\n          border-radius: 8px;\n          padding: 16px;\n          margin-bottom: 12px;\n          animation: grw-skeleton-pulse 1.5s ease-in-out infinite;\n        }\n        \n        .grw-skeleton-header {\n          display: flex;\n          align-items: flex-start;\n          gap: 12px;\n          margin-bottom: 12px;\n        }\n        \n        .grw-skeleton-avatar {\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          background: var(--grw-placeholder-bg);\n        }\n        \n        .grw-skeleton-info {\n          flex: 1;\n        }\n        \n        .grw-skeleton-name {\n          width: 120px;\n          height: 16px;\n          background: var(--grw-placeholder-bg);\n          border-radius: 4px;\n          margin-bottom: 4px;\n        }\n        \n        .grw-skeleton-date {\n          width: 80px;\n          height: 12px;\n          background: var(--grw-placeholder-bg);\n          border-radius: 4px;\n        }\n        \n        .grw-skeleton-rating {\n          width: 100px;\n          height: 16px;\n          background: var(--grw-placeholder-bg);\n          border-radius: 4px;\n        }\n        \n        .grw-skeleton-stars {\n          width: 100%;\n          height: 100%;\n          background: var(--grw-placeholder-bg);\n          border-radius: 4px;\n        }\n        \n        .grw-skeleton-content {\n          margin-top: 12px;\n        }\n        \n        .grw-skeleton-text {\n          height: 14px;\n          background: var(--grw-placeholder-bg);\n          border-radius: 4px;\n          margin-bottom: 8px;\n        }\n        \n        .grw-skeleton-text:not(.short) {\n          width: 100%;\n        }\n        \n        .grw-skeleton-text.short {\n          width: 70%;\n        }\n        \n        @keyframes grw-skeleton-pulse {\n          0%, 100% {\n            opacity: 1;\n          }\n          50% {\n            opacity: 0.6;\n          }\n        }\n      </style>\n      <div class="grw-skeleton-container">\n        ${Array(t).fill(0).map((t,n)=>`\n      <div class="grw-skeleton-card" style="animation-delay: ${.1*n}s">\n        <div class="grw-skeleton-header">\n          <div class="grw-skeleton-avatar"></div>\n          <div class="grw-skeleton-info">\n            <div class="grw-skeleton-name"></div>\n            <div class="grw-skeleton-date"></div>\n          </div>\n          <div class="grw-skeleton-rating">\n            <div class="grw-skeleton-stars"></div>\n          </div>\n        </div>\n        <div class="grw-skeleton-content">\n          <div class="grw-skeleton-text"></div>\n          <div class="grw-skeleton-text short"></div>\n        </div>\n      </div>\n    `).join("")}\n      </div>\n    `}loadMockReviews(){this.reviews=[{id:"1",authorName:"John Smith",authorPhotoUrl:"https://via.placeholder.com/40x40",rating:5,text:"Excellent service! The team was professional and delivered exactly what we needed. Highly recommend their services to anyone looking for quality work.",publishedDate:new Date(Date.now()-1728e5),isVerified:!0},{id:"2",authorName:"Sarah Johnson",rating:4,text:"Great experience overall. The staff was friendly and helpful throughout the entire process.",publishedDate:new Date(Date.now()-6048e5),isVerified:!1},{id:"3",authorName:"Mike Davis",authorPhotoUrl:"https://via.placeholder.com/40x40",rating:5,text:"Outstanding quality and attention to detail. They went above and beyond our expectations. The final result exceeded what we had hoped for and the communication was excellent throughout.",publishedDate:new Date(Date.now()-12096e5),isVerified:!0},{id:"4",authorName:"Emily Wilson",rating:4,text:"Very satisfied with the service. Professional team and timely delivery.",publishedDate:new Date(Date.now()-18144e5),isVerified:!1}],this.renderReviews()}renderReviews(){const t=this.shadowRoot.querySelector(".grw-widget");if(!t||0===this.reviews.length)return void this.renderError("No reviews available");const n={showPhotos:this.config.showPhotos||!0,showDates:this.config.showDates||!0,maxTextLength:this.config.maxTextLength||150,theme:this.config.theme||"light"},e={layout:this.config.layout||"carousel",maxReviews:this.config.maxReviews||10,reviewCardConfig:n,autoPlay:this.config.autoPlay||!1,autoPlayInterval:this.config.autoPlayInterval||5e3,showNavigation:!1!==this.config.showNavigation,showPagination:!1!==this.config.showPagination,itemsPerPage:this.config.itemsPerPage||1,gridColumns:this.config.gridColumns||2};this.reviewsDisplay=new r(this.reviews,e),t.innerHTML="",t.appendChild(this.reviewsDisplay.getElement())}mount(t){try{const n=document.querySelector(t);return!!n&&(n.appendChild(this.container),!0)}catch(n){return!1}}unmount(){this.resizeObserver&&this.resizeObserver.disconnect(),this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.isInitialized=!1}updateConfig(t){if(this.config={...this.config,...t},(t.width||t.height)&&(this.container.style.width=this.config.width||"100%",this.container.style.height=this.config.height||"auto"),t.theme){const t=this.shadowRoot.querySelector("style");t&&(t.textContent=this.getIsolatedCSS())}}getConfig(){return{...this.config}}isReady(){return this.isInitialized}async refresh(){this.cache.clear(this.config.widgetId),this.retryCount=0,await this.loadReviews()}clearCache(){this.cache.clear(this.config.widgetId)}getCacheStats(){return{size:this.cache.size(),hasData:null!==this.cache.get(this.config.widgetId)}}}function o(){const t=document.querySelectorAll("script[data-grw-widget-id]"),n={};return t.forEach(t=>{Array.from(t.attributes).forEach(t=>{if(t.name.startsWith("data-grw-")){const e=t.name.replace("data-grw-","").replace(/-([a-z])/g,(t,n)=>n.toUpperCase());n[e]=t.value}})}),n}function h(){const t=o();if(t.widgetId){const n={widgetId:t.widgetId,apiUrl:t.apiUrl,width:t.width,height:t.height,theme:t.theme,responsive:"false"!==t.responsive},e=new a(n),i=t.mountSelector||'script[data-grw-widget-id="'+t.widgetId+'"]',r=document.querySelector(i);if(r&&r.parentElement){const n=document.createElement("div");r.parentElement.insertBefore(n,r);const i="grw-container-"+t.widgetId;n.id=i,e.mount("#"+i)}}}const d={create:t=>new a(t),autoInit:h,parseEmbedParams:o};window.GoogleReviewsWidget=a,window.GoogleReviewsWidgetAPI=d,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",h):h();