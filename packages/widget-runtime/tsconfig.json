{"extends": "../../tsconfig.json", "compilerOptions": {"composite": true, "target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ESNext", "moduleResolution": "bundler", "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "types": ["vitest/globals"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}